# 🎯 Comprehensive Conditions Guide

## 🌟 **Complete Condition System**

The WoW Simulator now features **100+ condition types** covering every aspect of WoW-like gameplay mechanics. Create incredibly sophisticated spell interactions with realistic conditions!

## 📋 **Condition Categories**

### **⚡ Spell Conditions** (12 types)
Control when effects trigger based on spell properties:

#### **Basic Spell Events:**
- `spell_critical` - When spell crits
- `spell_hit` - When spell hits successfully  
- `spell_miss` - When spell misses
- `spell_interrupted` - When casting is interrupted

#### **Spell Properties:**
- `spell_school` - Magic school (fire, frost, arcane, etc.)
- `spell_type` - Spell type (instant, channeled, dot, aoe)
- `spell_damage_type` - Physical vs magical damage
- `spell_cast_time` - Based on cast time
- `spell_mana_cost` - Based on mana cost
- `spell_power_above` - Spell power threshold

#### **Spell State:**
- `spell_on_cooldown` - When specific spell is on cooldown
- `spell_recently_cast` - When spell was cast recently

### **🎯 Target Conditions** (18 types)
Sophisticated target-based triggers:

#### **Target Health & Status:**
- `target_health_below/above` - Health percentage
- `target_level` - Target level
- `target_armor` - Armor value
- `target_resistance` - Resistance values

#### **Target State:**
- `target_moving` - Target is moving
- `target_casting` - Target is casting
- `target_stunned` - Target is stunned
- `target_silenced` - Target is silenced
- `target_feared` - Target is feared
- `target_rooted` - Target is immobilized
- `target_polymorphed` - Target is polymorphed

#### **Target Position:**
- `target_distance` - Distance to target
- `target_behind` - Target behind caster
- `target_facing` - Target facing caster

#### **Target Buffs:**
- `target_has_buff` - Specific buff present
- `target_has_debuff` - Specific debuff present
- `target_type` - Target type (humanoid, beast, etc.)

### **🧙 Caster Conditions** (18 types)
Comprehensive caster state conditions:

#### **Caster Resources:**
- `caster_health_below/above` - Health percentage
- `caster_mana_below/above` - Mana percentage
- `caster_level` - Caster level

#### **Caster Identity:**
- `caster_class` - Character class
- `caster_race` - Character race
- `caster_stance` - Stance/form (bear, cat, defensive)

#### **Caster State:**
- `caster_moving` - Caster is moving
- `caster_casting` - Caster is casting
- `caster_in_combat` - In combat state
- `caster_stealthed` - Stealth active
- `caster_mounted` - Mounted state

#### **Caster Equipment:**
- `caster_weapon_equipped` - Weapon type equipped

#### **Caster Buffs:**
- `caster_has_buff` - Specific buff present
- `caster_has_debuff` - Specific debuff present
- `buff_present` - General buff check
- `buff_stacks` - Buff stack count

### **🎲 Proc Conditions** (6 types)
Advanced proc-based triggers:

- `proc_triggered` - When specific proc activates
- `proc_chance` - Random percentage chance
- `proc_cooldown_ready` - Proc cooldown expired
- `consecutive_procs` - Consecutive proc count
- `proc_stacks` - Proc stack count
- `proc_rate` - Procs per minute frequency

### **⚔️ Combat Conditions** (14 types)
Complex combat state conditions:

#### **Combat Timing:**
- `combat_time` - Duration in combat
- `effect_expires` - When effect expires

#### **Group Dynamics:**
- `enemies_nearby` - Enemy count in range
- `allies_nearby` - Ally count in range
- `group_size` - Party/raid size
- `threat_level` - Aggro/threat level

#### **Combat Resources:**
- `combo_points` - Combo points (rogue/druid)
- `rage_energy` - Rage/energy amount
- `holy_power` - Holy power (paladin)
- `soul_shards` - Soul shards (warlock)

#### **Combat Stats:**
- `combat_rating` - Hit/crit/haste ratings
- `weapon_skill` - Weapon skill level

#### **Spell Sequences:**
- `spell_combo` - Spell cast sequences
- `resource_threshold` - Resource thresholds

### **🌍 Environmental Conditions** (10 types)
World and zone-based conditions:

#### **Time & Weather:**
- `time_of_day` - Day/night/dawn/dusk
- `weather` - Rain/snow/clear weather

#### **Zone Properties:**
- `zone_type` - Indoor/outdoor zones
- `pvp_zone` - PvP zone active
- `sanctuary_zone` - Sanctuary protection
- `instance_type` - Dungeon/raid/battleground

#### **Encounter Type:**
- `boss_encounter` - Boss fight active
- `elite_target` - Elite mob target
- `player_target` - Player target
- `npc_target` - NPC target

### **🔧 Advanced Conditions** (12 types)
Sophisticated gameplay mechanics:

#### **Damage & Healing:**
- `damage_taken_recently` - Recent damage received
- `healing_received` - Recent healing received

#### **Immunities & Protections:**
- `spell_reflect_active` - Spell reflect up
- `magic_immunity` - Magic immunity active
- `physical_immunity` - Physical immunity active

#### **Crowd Control:**
- `crowd_control_active` - CC effects active
- `dispel_available` - Dispellable effects
- `interrupt_available` - Interrupt ready

#### **Positioning:**
- `line_of_sight` - LoS to target
- `facing_target` - Facing target
- `behind_target` - Behind target
- `flanking_target` - Flanking position

## 🎮 **Real WoW Examples**

### **🔥 Fire Mage Combustion**
```javascript
// Combustion: Spreads DoTs when multiple fire effects active
IF (target_has_debuff = "Ignite" AND target_has_debuff = "Living Bomb") 
   AND spell_school = "fire" 
   AND caster_has_buff = "Combustion"
```

### **🗡️ Rogue Execute**
```javascript
// Cheap Shot: Only works from stealth on humanoids
IF caster_stealthed = true 
   AND target_type = "humanoid" 
   AND target_distance <= 5
```

### **🛡️ Paladin Emergency Heal**
```javascript
// Lay on Hands: Emergency heal when low health
IF caster_health_below <= 20 
   AND holy_power >= 3 
   AND spell_on_cooldown != "Lay on Hands"
```

### **🐻 Druid Form Abilities**
```javascript
// Bear Form abilities only work in bear form
IF caster_stance = "bear" 
   AND caster_in_combat = true 
   AND threat_level = "high"
```

### **⚡ Shaman Chain Lightning**
```javascript
// Chain Lightning: More effective with multiple enemies
IF enemies_nearby >= 3 
   AND line_of_sight = true 
   AND caster_mana_above >= 30
```

## 🧠 **Advanced Logic Examples**

### **Complex Proc Chains**
```javascript
// Impact: Hot Streak + Fire spell + Multiple enemies
IF (proc_triggered = "Hot Streak" AND spell_school = "fire") 
   AND enemies_nearby >= 2 
   AND target_distance <= 30
```

### **Situational Bonuses**
```javascript
// Night Elf racial: Bonus damage at night while stealthed
IF time_of_day = "night" 
   AND caster_race = "night elf" 
   AND caster_stealthed = true
```

### **PvP Conditions**
```javascript
// PvP burst combo: Player target + low health + cooldowns ready
IF player_target = true 
   AND target_health_below <= 30 
   AND interrupt_available = true 
   AND pvp_zone = true
```

### **Raid Mechanics**
```javascript
// Raid healing: Boss fight + multiple injured allies
IF boss_encounter = true 
   AND allies_nearby >= 3 
   AND group_size >= 20 
   AND healing_received <= 100
```

## 🎯 **Condition Building Tips**

### **Performance Conditions**
```javascript
// High-performance scenarios
IF combat_rating >= "hit:300,crit:200" 
   AND weapon_skill >= 300 
   AND spell_power_above >= 400
```

### **Resource Management**
```javascript
// Mana efficiency conditions
IF caster_mana_below <= 25 
   OR proc_triggered = "Clearcasting" 
   OR time_of_day = "night"  // Night Elf racial
```

### **Positioning Tactics**
```javascript
// Backstab conditions
IF behind_target = true 
   AND target_distance <= 5 
   AND caster_stealthed = true 
   AND weapon_equipped = "dagger"
```

## 🔥 **What You Can Build**

With 100+ conditions, create authentic WoW mechanics:

### **🎭 Class-Specific Systems**
- **Mage**: Combustion, Hot Streak, Clearcasting chains
- **Rogue**: Stealth attacks, combo point builders
- **Paladin**: Holy power systems, emergency heals
- **Druid**: Form-specific abilities, nature synergies
- **Warlock**: Soul shard mechanics, DoT spreading
- **Priest**: Healing chains, shadow/holy synergies

### **🌍 Environmental Interactions**
- **Time-based**: Day/night racial bonuses
- **Weather**: Storm-enhanced lightning spells
- **Zone**: PvP burst combos, sanctuary protections
- **Instance**: Raid mechanics, dungeon strategies

### **⚔️ Combat Dynamics**
- **Positioning**: Backstab, flanking bonuses
- **Group**: AoE thresholds, healing priorities
- **Threat**: Tank abilities, aggro management
- **Resources**: Mana efficiency, combo systems

The comprehensive condition system transforms spell creation into a sophisticated game design tool that captures the full complexity of WoW's combat mechanics! 🎯✨
