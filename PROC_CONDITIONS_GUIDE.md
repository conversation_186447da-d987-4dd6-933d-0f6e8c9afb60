# 🎯 Proc Conditions Guide

## 🌟 **What Are Proc Conditions?**

Proc conditions are special triggers that activate based on proc events, creating complex spell interactions and chains. They allow you to build spells that respond to other procs, creating realistic WoW-like mechanics.

## 🔥 **New Proc Condition Types**

### **🎲 proc_triggered**
**"When specific proc triggers"**
- **Description**: Activates when a named proc effect triggers
- **Example**: `proc_triggered = "Hot Streak"`
- **Use Case**: Impact spreads DoTs when Hot Streak procs

### **🎯 proc_chance**
**"Random proc chance"**
- **Description**: Independent random chance to trigger
- **Example**: `proc_chance <= 30` (30% chance)
- **Use Case**: Clearcasting has 10% chance on any spell cast

### **⏰ proc_cooldown_ready**
**"Proc cooldown is ready"**
- **Description**: Triggers when proc's internal cooldown expires
- **Example**: `proc_cooldown_ready = "Ignite"`
- **Use Case**: Enhanced effects when proc is available

### **🔗 consecutive_procs**
**"Consecutive proc triggers"**
- **Description**: Triggers after X procs in a row
- **Example**: `consecutive_procs >= 2`
- **Use Case**: Bonus effects after multiple procs

### **📚 proc_stacks**
**"Proc effect has stacks"**
- **Description**: Based on number of proc stacks
- **Example**: `proc_stacks >= 5`
- **Use Case**: Ignite explosion at max stacks

### **⚡ proc_rate**
**"Proc trigger rate"**
- **Description**: Based on procs per minute frequency
- **Example**: `proc_rate >= 10`
- **Use Case**: High-frequency proc bonuses

## 🎮 **Real WoW Examples**

### **🔥 Hot Streak → Impact Chain**
```
Hot Streak Proc:
- Condition: spell_critical = "fire"
- Effect: Grants Hot Streak buff

Impact Proc:
- Condition: proc_triggered = "Hot Streak" AND spell_school = "fire"
- Effect: Spreads DoTs to nearby enemies
```

### **✨ Clearcasting Random Proc**
```
Clearcasting:
- Condition: proc_chance <= 10
- Effect: Next spell costs 0 mana
- Trigger: Any spell cast
```

### **🔥 Ignite Stacking System**
```
Ignite:
- Condition: spell_critical = "fire"
- Effect: Add DoT stack
- Max Stacks: 5

Ignite Explosion:
- Condition: proc_stacks >= 5
- Effect: Massive AoE damage
```

## 🧠 **Advanced Logic Combinations**

### **Complex Proc Chains**
```javascript
// Impact: Spreads DoTs when Hot Streak + Fire spell
IF proc_triggered = "Hot Streak" AND spell_school = "fire"

// Enhanced Ignite: Extra damage at high stacks
IF proc_stacks >= 3 AND target_health_below 35

// Clearcasting Chain: Higher chance after consecutive procs
IF consecutive_procs >= 2 OR proc_rate >= 15
```

### **Multi-Condition Proc Logic**
```javascript
// Combustion: Multiple fire proc conditions
IF (proc_triggered = "Hot Streak" OR proc_triggered = "Ignite") 
   AND caster_has_buff = "Combustion"

// Arcane Power: Proc chance increases with mana
IF proc_chance <= 20 AND caster_mana_above 80

// Execute Range: Proc enhances low-health damage
IF proc_triggered = "Sudden Death" AND target_health_below 20
```

## 🎯 **Building Proc-Based Spells**

### **Step 1: Choose Base Effect**
- **Proc Effect**: For passive procs that trigger other effects
- **Conditional Effect**: For spells enhanced by procs
- **Stacking Effect**: For procs that build up over time

### **Step 2: Set Proc Conditions**
- **Primary Trigger**: What causes the proc to activate
- **Secondary Conditions**: Additional requirements
- **Logic Operator**: AND/OR for multiple conditions

### **Step 3: Configure Properties**
- **Proc Chance**: Probability of triggering
- **Internal Cooldown**: Minimum time between procs
- **Duration**: How long proc effects last
- **Stacks**: Maximum number of stacks

## 🔧 **Practical Examples**

### **🎲 Random Proc (Clearcasting)**
```
Effect Type: Proc Effect
Conditions:
- proc_chance <= 10 (10% chance)
Properties:
- Buff: "Clearcasting"
- Duration: 15 seconds
- Effect: Next spell costs 0 mana
```

### **🔗 Proc Chain (Impact)**
```
Effect Type: Proc Effect
Conditions:
- proc_triggered = "Hot Streak" AND
- spell_school = "fire"
Properties:
- Spread Radius: 8 yards
- Max Targets: 3
- Effect: Copy DoTs to nearby enemies
```

### **📚 Stacking Proc (Ignite)**
```
Effect Type: Stacking Effect
Conditions:
- spell_critical = "fire"
Properties:
- Max Stacks: 5
- Duration: 4 seconds
- Tick Interval: 2 seconds
- Stack Value: 40% of crit damage
```

## 🎨 **Design Patterns**

### **🔄 Proc Chains**
Create sequences where one proc triggers another:
```
Fireball Crit → Hot Streak → Impact → DoT Spread
```

### **🎯 Conditional Enhancement**
Procs that enhance other spells:
```
Clearcasting Active → Next Spell Free
Hot Streak Active → Pyroblast Instant
```

### **📈 Stacking Systems**
Procs that build up power:
```
Ignite Stacks → More DoT Damage
Arcane Blast Stacks → Higher Damage + Cost
```

### **🎲 Random Events**
Chance-based procs for unpredictability:
```
10% Chance → Clearcasting
5% Chance → Critical Strike Bonus
```

## 🚀 **Advanced Techniques**

### **Proc Rate Scaling**
```javascript
// Higher proc chance when fighting multiple enemies
IF enemies_nearby >= 3 AND proc_chance <= 25

// Proc chance scales with missing health
IF caster_health_below 50 AND proc_chance <= (100 - health_percentage)
```

### **Conditional Proc Chains**
```javascript
// Only chain procs during specific buffs
IF proc_triggered = "Hot Streak" AND caster_has_buff = "Combustion"

// Chain procs based on target state
IF proc_triggered = "Ignite" AND target_has_debuff = "Scorch"
```

### **Resource-Based Procs**
```javascript
// Mana efficiency procs
IF caster_mana_below 25 AND proc_chance <= 50

// Health emergency procs
IF caster_health_below 30 AND proc_cooldown_ready = "Last Stand"
```

## 🎉 **Benefits of Proc Conditions**

### **🎮 Authentic WoW Feel**
- **Real Mechanics**: Matches actual game proc systems
- **Complex Interactions**: Multi-layered spell relationships
- **Unpredictable Gameplay**: RNG creates excitement

### **🧠 Strategic Depth**
- **Proc Management**: Players must track and utilize procs
- **Timing Decisions**: When to use proc-enhanced abilities
- **Build Synergies**: Combining procs for maximum effect

### **🎨 Creative Freedom**
- **Custom Proc Chains**: Design unique spell interactions
- **Balanced Randomness**: Control proc rates and conditions
- **Scalable Complexity**: From simple to advanced proc systems

## 🔮 **What You Can Build**

With proc conditions, you can create:

- **🔥 Fire Mage Combos**: Hot Streak → Pyroblast → Ignite chains
- **✨ Arcane Proc Systems**: Clearcasting → Free spells
- **⚡ Chain Reactions**: One proc triggering multiple effects
- **📚 Stacking Mechanics**: Building power through repeated procs
- **🎯 Conditional Bonuses**: Enhanced effects under specific conditions
- **🎲 RNG Systems**: Chance-based gameplay elements

Proc conditions transform the spell builder from simple effects into a sophisticated system that captures the complexity and excitement of real WoW spell mechanics! 🎯✨
