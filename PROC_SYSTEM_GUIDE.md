# 🔥 WoW Simulator Proc System Guide

## What Are Procs?

**Procs** (short for "Programmed Random OCcurrence" or "Special Procedure") are passive effects that trigger automatically based on certain conditions. Unlike regular spells that you cast manually, procs activate in response to other actions.

## 🎯 How Procs Work in WoW Simulator

### **Hot Streak - The Perfect Example**

Hot Streak is a **passive proc** that demonstrates the system perfectly:

#### **What It Does:**
- **Passive Monitoring**: Constantly watches for fire spell critical hits
- **Automatic Trigger**: When any fire spell crits, Hot Streak activates
- **Buff Application**: Grants a 10-second "Hot Streak" buff
- **Spell Enhancement**: Makes the next Pyroblast instant cast

#### **The Proc Chain:**
```
Fire Spell Cast → Critical Hit → Hot Streak Proc Triggers → 
Hot Streak Buff Applied → Pyroblast Becomes Instant → 
Buff Consumed on Pyroblast Cast
```

## 🔧 Technical Implementation

### **Proc vs Regular Spell**

| Aspect | Regular Spell | Proc |
|--------|---------------|------|
| **Activation** | Manual cast | Automatic trigger |
| **Target Type** | Enemy/Self | Passive |
| **Cast Time** | Variable | 0 (instant) |
| **Mana Cost** | Usually has cost | Usually 0 |
| **Trigger** | Player action | Game event |

### **Hot Streak Configuration**

```javascript
{
    name: "Hot Streak (Proc)",
    target_type: "passive",        // Indicates it's a proc
    is_passive_proc: true,         // Marks as passive effect
    advanced_effects: [
        {
            effect_type: "proc_effect",
            conditions: [
                {
                    condition_type: "spell_critical",
                    operator: "==",
                    value: "fire"          // Only fire spell crits
                }
            ],
            trigger_spell_schools: ["fire"]   // Which spells can trigger it
        }
    ]
}
```

## 🎮 How to Use Procs in WoW Simulator

### **Step 1: Create the Proc**
1. Go to **Spells** tab → **Advanced Mode**
2. Load the "Hot Streak (Proc)" template
3. Customize if desired and create the spell

### **Step 2: Create Trigger Spells**
1. Create fire spells that can trigger the proc:
   - Fireball
   - Fire Blast
   - Scorch
   - Any fire school spell

### **Step 3: Create Beneficiary Spells**
1. Create or load "Pyroblast" template
2. This spell benefits from the Hot Streak buff
3. Becomes instant cast when buff is active

### **Step 4: Test the Interaction**
1. Go to **Testing** tab
2. Select a fire spell and test it
3. Watch for Hot Streak procs on critical hits
4. Use **Simulate Advanced Effects** to see the full interaction

## 🔥 Real WoW Hot Streak Mechanics

### **How It Works in Game:**
1. **Heating Up**: First fire spell crit gives "Heating Up" buff
2. **Hot Streak**: Second consecutive fire spell crit upgrades to "Hot Streak"
3. **Instant Pyroblast**: Hot Streak makes next Pyroblast instant
4. **Consumption**: Casting Pyroblast consumes the Hot Streak buff

### **Our Simplified Version:**
- Any fire spell crit directly triggers Hot Streak (simplified for demo)
- Hot Streak makes next Pyroblast instant
- Buff lasts 10 seconds or until consumed

## 🎯 Other Proc Examples in WoW

### **Ignite** (Also Implemented)
- **Trigger**: Fire spell critical hits
- **Effect**: Adds DoT equal to 40% of crit damage
- **Stacking**: Multiple ignites stack up to 5 times

### **Clearcasting** (Arcane Mage)
- **Trigger**: Spell casts
- **Effect**: Next spell costs no mana
- **Chance**: 10% proc chance

### **Impact** (Fire Mage)
- **Trigger**: Fire spell hits
- **Effect**: Next Fire Blast spreads DoTs to nearby enemies
- **Chance**: Variable based on spell

## 🔧 Creating Custom Procs

### **Basic Proc Structure:**
```javascript
{
    name: "My Custom Proc",
    target_type: "passive",
    is_passive_proc: true,
    advanced_effects: [
        {
            effect_type: "proc_effect",
            name: "Proc Trigger",
            conditions: [
                {
                    condition_type: "spell_critical",  // What triggers it
                    operator: "==",
                    value: "frost"                     // Condition value
                }
            ],
            value: {
                buff_name: "My Buff",               // What buff to apply
                duration: 15.0                      // How long it lasts
            },
            chance: 0.3                             // 30% proc chance
        }
    ]
}
```

### **Available Trigger Conditions:**
- `spell_critical`: Spell critical hits
- `spell_hit`: Any spell hit
- `target_health_below`: Target health percentage
- `buff_present`: When specific buff exists
- `debuff_present`: When specific debuff exists

## 🎭 Advanced Proc Interactions

### **Proc Chains:**
Procs can trigger other procs, creating complex chains:
```
Fireball Crit → Hot Streak Proc → Instant Pyroblast → 
Pyroblast Crit → Ignite Proc → DoT Applied
```

### **Conditional Procs:**
Procs that only work under specific conditions:
- Only when target is below 35% health
- Only when specific buff is active
- Only during certain phases of combat

### **Stacking Procs:**
Some procs can stack multiple times:
- Ignite stacks up to 5 times
- Arcane Blast stacks up to 4 times
- Each stack increases the effect

## 🎉 Why Procs Are Exciting

### **Unpredictability:**
- Adds RNG excitement to rotations
- Creates "lucky streaks" of procs
- Makes each fight feel different

### **Skill Expression:**
- Good players maximize proc usage
- Timing becomes crucial
- Resource management around procs

### **Build Diversity:**
- Different proc chances create different playstyles
- Synergies between multiple procs
- Customizable trigger conditions

## 🚀 Next Steps

1. **Experiment**: Try the Hot Streak + Pyroblast combo
2. **Create**: Build your own custom procs
3. **Test**: Use the advanced simulation to see proc interactions
4. **Optimize**: Find the best proc-based rotations

The proc system in WoW Simulator brings the excitement and complexity of real WoW spell interactions to life! 🔥✨
