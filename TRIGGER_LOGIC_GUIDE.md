# ⚡ **Spell Trigger Logic System**

## ✅ **New Feature: Smart Spell Triggers!**

I've added a comprehensive trigger logic system to the spell builder. Now your spells can automatically react to game events, conditions, and situations - just like real WoW spells!

---

## 🎯 **4 Types of Trigger Logic**

### **🖱️ Manual Cast**
**For spells you control directly**
- **What it does**: You decide when to cast the spell
- **Best for**: Main rotation spells, situational abilities
- **Configuration**:
  - Keybind suggestion (1, 2, F1, etc.)
  - Show in action bar (Yes/No)
- **Examples**: Fireball, Heal, Polymorph

### **🤖 Auto Rotation**
**For spells the AI should cast automatically**
- **What it does**: AI casts the spell based on priority and conditions
- **Best for**: Rotation optimization, passive abilities
- **Configuration**:
  - Priority: High/Medium/Low
  - Conditions: Always, Low health target, High/Low mana, Buff active, With cooldowns
- **Examples**: <PERSON><PERSON> (filler), Execute (low health), Emergency heal

### **⚡ Event Trigger**
**For spells that react to specific events**
- **What it does**: Automatically triggers when events happen
- **Best for**: Proc effects, reactive abilities
- **Configuration**:
  - Select events: Spell Crit, Take Damage, Kill Enemy, Buff Expires, Cast Spell, Enter Combat
  - Trigger chance: 10-100%
- **Examples**: Hot Streak proc, Missile proc, Ignite spread

### **📋 Condition Trigger**
**For spells with complex condition requirements**
- **What it does**: Triggers when all/any conditions are met
- **Best for**: Advanced spell logic, situational abilities
- **Configuration**:
  - Add multiple conditions
  - Logic operator: ALL (AND) or ANY (OR)
  - Condition types: Health, Mana, Buffs, Debuffs, Cooldowns, Distance, Time
- **Examples**: Burst combos, Emergency abilities, Phase-specific spells

---

## 🚀 **How to Use Trigger Logic**

### **Step 1: Open Spell Builder**
1. Go to **Spells** → **Advanced Mode**
2. You'll see the **"Super Simple Spell Builder"**

### **Step 2: Configure Your Spell**
1. Set spell name, type, power, mana cost, cast speed
2. **Choose trigger type** from the dropdown

### **Step 3: Configure Trigger Logic**
Based on your choice, you'll see different options:

#### **🖱️ For Manual Cast:**
- **Keybind**: Suggest which key to bind (1, 2, F1, etc.)
- **Action Bar**: Whether to show in UI

#### **🤖 For Auto Rotation:**
- **Priority**: How often AI should cast it
  - High = Cast often (main spells)
  - Medium = Normal use (balanced)
  - Low = Filler only (when nothing else)
- **Condition**: When to use it
  - Always available
  - Target below 35% health
  - Mana above 80% / below 20%
  - When buff active
  - With major cooldowns

#### **⚡ For Event Trigger:**
- **Select Events**: Click the event buttons
  - Spell Crit (when you crit)
  - Take Damage (when you're hit)
  - Kill Enemy (when target dies)
  - Buff Expires (when buff ends)
  - Cast Spell (when you cast anything)
  - Enter Combat (when fight starts)
- **Trigger Chance**: 10-100% chance to trigger

#### **📋 For Condition Trigger:**
- **Add Conditions**: Click "Add Condition" button
- **Choose Condition Type**:
  - Health percentage (yours or target's)
  - Mana percentage
  - Buff active (name and stacks)
  - Debuff active (name and target)
  - Cooldown ready (spell name and status)
  - Distance to target
  - Time in combat
- **Logic Operator**: ALL conditions or ANY condition

### **Step 4: See Live Preview**
- **Spell description updates** to show trigger logic
- **Preview shows** how the spell will behave
- **Create the spell** when you're happy

---

## 💡 **Real Examples**

### **🔥 Fire Mage Hot Streak Proc**
- **Type**: Event Trigger
- **Events**: Spell Crit
- **Chance**: 100%
- **Result**: Triggers every time you crit, just like real Hot Streak

### **❄️ Frost Mage Emergency Heal**
- **Type**: Condition Trigger
- **Conditions**: My health < 30% AND Mana > 20%
- **Logic**: ALL (AND)
- **Result**: Only heals when you're low health and have mana

### **🔮 Arcane Mage Burn Phase**
- **Type**: Auto Rotation
- **Priority**: High
- **Condition**: Mana above 80%
- **Result**: AI casts this often when you have lots of mana

### **⚔️ Execute Finisher**
- **Type**: Auto Rotation
- **Priority**: High
- **Condition**: Target below 35% health
- **Result**: AI automatically uses this on low-health enemies

### **🛡️ Defensive Cooldown**
- **Type**: Event Trigger
- **Events**: Take Damage
- **Chance**: 25%
- **Result**: 25% chance to trigger when you take damage

### **💥 Combustion Combo**
- **Type**: Condition Trigger
- **Conditions**: Buff active "Combustion" AND Cooldown ready "Pyroblast"
- **Logic**: ALL (AND)
- **Result**: Only triggers during Combustion when Pyroblast is ready

---

## 🎯 **Trigger Logic Benefits**

### **🤖 Smart Automation**
- **Auto rotation spells** cast themselves at the right time
- **Event triggers** react instantly to game events
- **Condition triggers** only fire when it makes sense

### **🎮 Realistic Gameplay**
- **Proc effects** work like real WoW spells
- **Situational abilities** only trigger when appropriate
- **Emergency spells** activate when you need them

### **⚡ Performance Optimization**
- **Priority system** ensures important spells cast first
- **Condition checking** prevents wasted casts
- **Event-driven** system is efficient and responsive

### **🔧 Flexible Configuration**
- **Mix trigger types** for different spells
- **Complex conditions** for advanced users
- **Simple options** for beginners

---

## 🔧 **Advanced Trigger Examples**

### **🔥 Fire Mage Ignite Spread**
```
Type: Event Trigger
Events: Spell Crit + Kill Enemy
Chance: 100%
Description: Spreads Ignite when you crit or kill
```

### **❄️ Frost Mage Shatter Combo**
```
Type: Condition Trigger
Conditions: 
  - Buff active "Fingers of Frost" (1+ stacks)
  - Target debuff "Frozen"
Logic: ANY (OR)
Description: Triggers when target is frozen or you have FoF
```

### **🔮 Arcane Mage Mana Management**
```
Type: Condition Trigger
Conditions:
  - Mana below 20%
  - Time in combat > 30 seconds
Logic: ALL (AND)
Description: Emergency mana spell after 30s if low mana
```

### **⚔️ Warrior Execute Chain**
```
Type: Auto Rotation
Priority: High
Condition: Target below 35% health
Description: High priority execute when target is low
```

---

## 🎉 **Result: Intelligent Spell System**

### **✅ What You Get:**
- **Smart spells** that know when to trigger
- **Realistic proc effects** like real WoW spells
- **Automated rotation** optimization
- **Event-driven** reactive gameplay
- **Complex condition** support for advanced users

### **✅ Perfect For:**
- **Rotation helpers** that cast spells automatically
- **Proc tracking** and reactive abilities
- **Emergency systems** that activate when needed
- **Burst combos** that sync with cooldowns
- **Situational spells** that only fire when appropriate

**Your spells now have real intelligence and can react to the game world just like authentic WoW abilities!** ⚡✨

---

*Trigger logic system added to spell builder*
*From static spells to intelligent, reactive abilities*
*Perfect for creating authentic WoW-style spell behavior*
