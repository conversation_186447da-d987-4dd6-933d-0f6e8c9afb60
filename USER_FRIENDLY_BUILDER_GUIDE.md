# 🎨 User-Friendly Spell Builder Guide

## 🌟 **What's New?**

The WoW Simulator now features a completely redesigned, user-friendly spell builder that makes creating complex spells as easy as clicking buttons! No more confusing forms or technical jargon.

## 🚀 **How to Access**

1. Go to **Spells** tab
2. Click **"Enable Advanced Mode"** 
3. The new **Advanced Spell Builder** will appear
4. Follow the 3-step guided process!

## 📋 **3-Step Building Process**

### **Step 1: Effects** 🎯
**"What should this spell do?"**

Choose from intuitive effect categories:

#### **🗡️ Damage Effects**
- **Instant Damage**: Deal immediate damage
- **Damage Over Time**: Burn/poison target  
- **Chain Damage**: Jump between enemies
- **Area Damage**: Hit multiple targets

#### **✨ Special Effects**
- **Proc Effect**: Trigger other spells
- **Stacking Effect**: Build up power
- **Buff**: Enhance abilities
- **Debuff**: Weaken enemies

#### **🛡️ Control Effects**
- **Stun**: Disable target
- **Slow**: Reduce movement
- **Transform**: Change target form (Polymorph!)
- **Silence**: Prevent spellcasting

**How it works:**
- Click any effect button to add it
- Click again to remove it
- Selected effects show in the list below
- Add multiple effects for complex spells!

### **Step 2: Conditions** 🤔
**"When should these effects trigger?"**

Set up smart conditions with logical operators:

#### **Condition Types:**
- **Spell Conditions**: Critical hits, spell school, etc.
- **Target Conditions**: Health percentage, buffs/debuffs
- **Caster Conditions**: Your health, mana, buffs
- **Combat Conditions**: Duration, nearby enemies

#### **Logic Operators:**
- **AND**: ALL conditions must be true
- **OR**: ANY condition can be true

#### **Examples:**
```
IF spell_critical AND target_health_below 35%
IF buff_present "Hot Streak" OR caster_mana_below 20%
IF spell_school = "fire" AND target_has_debuff "Ignite"
```

**Live Preview**: See your logic in real-time!

### **Step 3: Properties** ⚙️
**"Fine-tune your effects"**

Adjust specific values for each effect:

#### **Common Properties:**
- **Damage/Value**: How much damage or effect strength
- **Duration**: How long effects last
- **Spell Power Scaling**: How much spell power affects it

#### **Specialized Properties:**
- **DoT Effects**: Tick interval (how often damage occurs)
- **Chain Effects**: Max targets, damage reduction per jump
- **Stacking Effects**: Maximum stacks allowed
- **Area Effects**: Radius, max targets

## 🎯 **Example Builds**

### **🔥 Living Bomb Recreation**
**Step 1**: Add "Damage Over Time" + "Area Damage"
**Step 2**: Set condition "effect_expires" for the DoT
**Step 3**: Configure DoT (50 damage, 12s duration, 3s ticks) + Explosion (300 damage)

### **⚡ Chain Lightning Recreation**  
**Step 1**: Add "Chain Damage"
**Step 2**: No conditions (always triggers)
**Step 3**: Configure 3 max targets, 30% damage reduction per jump

### **🔮 Hot Streak Proc Recreation**
**Step 1**: Add "Proc Effect"
**Step 2**: Set condition "spell_critical" = "fire"
**Step 3**: Configure buff name "Hot Streak", 10s duration

## 🧠 **Smart Features**

### **Visual Feedback**
- **Selected Effects**: Buttons highlight when chosen
- **Progress Tracking**: See which step you're on
- **Live Preview**: Conditions show in readable format
- **Error Prevention**: Can't proceed without required selections

### **Intelligent Defaults**
- **Reasonable Values**: Pre-filled with balanced numbers
- **Type-Specific Fields**: Only shows relevant properties
- **Auto-Conversion**: Handles percentages and decimals

### **User Experience**
- **Step-by-Step**: Never overwhelming
- **Back/Forward**: Navigate freely between steps
- **Edit Anytime**: Click edit to modify effects
- **Clear Descriptions**: Every option explained

## 🔧 **Advanced Logic System**

### **Condition Logic**
The new system supports complex logical expressions:

```javascript
// Simple condition
IF spell_critical

// Multiple conditions with AND
IF spell_critical AND target_health_below 35%

// Multiple conditions with OR  
IF buff_present "Hot Streak" OR caster_mana_below 20%

// Complex combinations
IF (spell_school = "fire" AND spell_critical) OR buff_present "Combustion"
```

### **Condition Types Explained**

#### **Spell Conditions**
- `spell_critical`: When spell crits
- `spell_hit`: When spell hits (not misses)
- `spell_school`: Based on spell's magic school

#### **Target Conditions**
- `target_health_below`: Target HP < X%
- `target_health_above`: Target HP > X%
- `target_has_buff`: Target has specific buff
- `target_has_debuff`: Target has specific debuff

#### **Caster Conditions**
- `caster_health_below`: Your HP < X%
- `caster_mana_below`: Your mana < X%
- `buff_present`: You have specific buff
- `buff_stacks`: Buff has X stacks

#### **Combat Conditions**
- `combat_time`: How long you've been fighting
- `enemies_nearby`: Number of enemies in range
- `effect_expires`: When another effect ends

## 🎨 **Design Philosophy**

### **User-Centric**
- **No Technical Jargon**: Plain English descriptions
- **Visual Learning**: Icons and colors for everything
- **Progressive Disclosure**: Show complexity gradually
- **Immediate Feedback**: See results instantly

### **Powerful Yet Simple**
- **One-Click Effects**: Add complex mechanics easily
- **Smart Defaults**: Works out of the box
- **Infinite Customization**: Tweak every detail
- **Professional Results**: Creates real WoW-like spells

## 🚀 **Getting Started**

### **For Beginners**
1. Start with **Step 1**: Pick one simple effect
2. Skip **Step 2**: Leave conditions empty (always triggers)
3. **Step 3**: Use default values
4. Click **"Create Spell"**

### **For Advanced Users**
1. **Step 1**: Combine multiple effects
2. **Step 2**: Set up complex conditional logic
3. **Step 3**: Fine-tune every parameter
4. Create professional-grade spell mechanics

## 🎉 **Benefits**

### **Before (Old Builder)**
- ❌ Confusing technical forms
- ❌ Required JSON knowledge
- ❌ Easy to make mistakes
- ❌ No guidance or help

### **After (New Builder)**
- ✅ Intuitive click-to-add interface
- ✅ Plain English descriptions
- ✅ Error prevention and validation
- ✅ Step-by-step guidance
- ✅ Live preview of logic
- ✅ Professional results

## 🔮 **What You Can Build**

With the new builder, you can easily create:

- **🔥 DoT Spells**: Like Ignite, Living Bomb
- **⚡ Chain Spells**: Like Chain Lightning, Chain Heal
- **🎯 Proc Systems**: Like Hot Streak, Clearcasting
- **📚 Stacking Mechanics**: Like Arcane Blast, Seals
- **🛡️ Control Spells**: Like Polymorph, Fear
- **💥 Combo Systems**: Multiple effects with conditions
- **🔄 Synergy Mechanics**: Spells that enhance each other

The user-friendly spell builder transforms spell creation from a technical challenge into a creative, enjoyable experience! 🎨✨
