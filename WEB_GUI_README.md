# 🧙‍♂️ WoW Simulator - Web GUI

A user-friendly web interface for the World of Warcraft Spell Rotation Helper. This GUI provides an intuitive way to create characters, build spells, optimize rotations, and test spell performance.

## 🌟 Features

### 📊 Character Management
- **Character Creation**: Set up your character with custom stats
- **Stat Configuration**: Health, Mana, Spell Power, Crit Chance, <PERSON><PERSON>, Hit Chance
- **Real-time Updates**: See character changes reflected immediately
- **Character Summary**: Quick overview of your character's current stats

### 🔥 Spell Builder
- **Template System**: Choose from pre-built spell templates
- **Custom Spells**: Create spells from scratch with full customization
- **Spell Schools**: Fire, Frost, Arcane, Shadow, Nature, Holy
- **Advanced Configuration**: Cast time, cooldown, mana cost, damage ranges
- **Validation**: Real-time spell validation with error feedback
- **Spell Library**: View and manage all created spells

### ⚡ Rotation Optimizer
- **Multiple Goals**: Optimize for Maximum DPS, Mana Efficiency, or Burst Damage
- **Flexible Parameters**: Set fight duration, target count, movement time
- **Spell Selection**: Choose which spells to include in optimization
- **Results Analysis**: Detailed DPS, damage, and efficiency metrics
- **Timeline View**: See the optimal spell rotation step-by-step

### 🧪 Spell Testing
- **Performance Testing**: Test spells with multiple iterations
- **Statistical Analysis**: Average, min, max damage, crit rate
- **DPS/DPM Calculations**: Damage per second and damage per mana
- **Test History**: Track all your spell tests over time
- **Batch Testing**: Test all spells at once

## 🚀 Quick Start

### Prerequisites
Make sure you have Python 3.7+ installed and the WoW Simulator dependencies:

```bash
# Install required packages
pip install flask flask-cors jsonschema
```

### Option 1: Easy Launch (Recommended)
```bash
# Run the launcher script
python run_web_gui.py
```

This will:
- Check dependencies
- Start the backend server
- Open the web interface in your browser
- Provide helpful instructions

### Option 2: Manual Setup
```bash
# Terminal 1: Start the backend
python web_backend.py

# Terminal 2: Open the frontend
# Open index.html in your web browser
```

## 📖 How to Use

### 1. Character Setup
1. Navigate to the **Character** tab
2. Enter your character name and select class
3. Configure core stats (Health, Mana, Spell Power, etc.)
4. Click **Update Character** to save changes
5. Review the character summary

### 2. Creating Spells
1. Go to the **Spells** tab
2. **Option A - Use Template**:
   - Select a spell template from the dropdown
   - Customize the loaded template as needed
3. **Option B - Create from Scratch**:
   - Fill in all spell configuration fields
   - Choose spell school, target type, damage, etc.
4. Click **Create Spell** to add to your spell library
5. Use **Validate** to check spell configuration

### 3. Optimizing Rotations
1. Switch to the **Rotation** tab
2. Configure optimization settings:
   - Choose goal (Max DPS, Mana Efficient, Burst)
   - Set fight duration and target count
   - Adjust movement time if needed
3. Select which spells to include in optimization
4. Click **Optimize Rotation**
5. Review results and timeline

### 4. Testing Spells
1. Open the **Testing** tab
2. Select a spell to test
3. Set number of iterations and target armor
4. Click **Run Test** for individual testing
5. Use **Test All Spells** for batch testing
6. Review results and test history

## 🎨 Interface Features

### 🎯 WoW-Themed Design
- **Color Scheme**: Dark theme with gold accents inspired by WoW
- **Typography**: Cinzel font for headers, clean sans-serif for content
- **Icons**: Font Awesome icons for intuitive navigation
- **Responsive**: Works on desktop, tablet, and mobile devices

### 🔔 User Feedback
- **Notifications**: Success, warning, and error messages
- **Loading States**: Visual feedback during API calls
- **Form Validation**: Real-time validation with helpful error messages
- **Empty States**: Helpful messages when no data is available

### 📱 Responsive Design
- **Mobile-Friendly**: Optimized for all screen sizes
- **Touch-Friendly**: Large buttons and touch targets
- **Flexible Layout**: Grid system adapts to different devices

## 🔧 Technical Details

### Frontend Stack
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **JavaScript**: Vanilla JS with async/await for API calls
- **Fonts**: Google Fonts (Cinzel, Open Sans)
- **Icons**: Font Awesome 6

### Backend Stack
- **Flask**: Python web framework
- **Flask-CORS**: Cross-origin resource sharing
- **WoW Simulator**: Integration with existing Python modules

### API Endpoints
- `GET /api/health` - Health check
- `POST /api/character` - Create/update character
- `GET /api/spell-templates` - Get spell templates
- `POST /api/spell` - Create new spell
- `POST /api/spell/validate` - Validate spell configuration
- `GET /api/spells` - Get all created spells
- `POST /api/optimize-rotation` - Optimize spell rotation
- `POST /api/test-spell` - Test spell performance

## 🐛 Troubleshooting

### Backend Won't Start
- Check if all dependencies are installed: `pip install flask flask-cors jsonschema`
- Ensure port 5000 is not in use by another application
- Check the terminal for error messages

### Frontend Not Loading
- Make sure you're opening `index.html` in a modern web browser
- Check browser console for JavaScript errors
- Ensure the backend is running on `http://localhost:5000`

### API Connection Issues
- Verify the backend is running and accessible
- Check browser network tab for failed requests
- The frontend will fall back to local storage if backend is unavailable

### Spell Creation Errors
- Ensure all required fields are filled
- Check that damage min ≤ damage max
- Verify spell name is unique
- Review validation error messages

## 🔮 Future Enhancements

- **Advanced Spell Effects**: DoT, HoT, buffs, debuffs
- **Gear Integration**: Equipment and stat bonuses
- **Talent Trees**: Character specialization options
- **Combat Logs**: Detailed fight analysis
- **Export/Import**: Save and share configurations
- **Multi-Character**: Manage multiple characters
- **Raid Optimization**: Multi-character rotation planning

## 📞 Support

If you encounter any issues or have suggestions:
1. Check this README for troubleshooting tips
2. Review the console logs for error details
3. Ensure all dependencies are properly installed
4. Try restarting both frontend and backend

---

**Enjoy optimizing your WoW spell rotations!** 🎮✨
