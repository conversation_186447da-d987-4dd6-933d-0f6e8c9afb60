# 🔥 **WoW-Style Spell Builder Guide**

## ✅ **Problem Solved: Real WoW Spell Logic!**

I've completely redesigned the spell builder with **authentic WoW spell mechanics and conditions** that make sense for mage rotation optimization.

---

## 🎯 **New WoW-Authentic Condition System**

### **🔥 Always Available**
**For basic rotation spells that you cast regularly**
- **Examples**: Frostbolt, Fireball, Arcane Missiles
- **Configuration**: Set rotation priority (High/Medium/Low)
- **Use Case**: Your bread-and-butter spells

### **🛡️ Buff-Dependent Spells**
**For spells that require specific buffs to be effective**
- **Examples**: 
  - Pyroblast (requires Hot Streak)
  - Ice Lance (requires Fingers of Frost)
  - Enhanced spells (requires Arcane Power)
- **Configuration**: 
  - Choose required buff (Hot Streak, Brain Freeze, etc.)
  - Set minimum stack requirements
- **Use Case**: Proc-based instant casts and enhanced abilities

### **⚡ Resource-Based Spells**
**For spells that depend on resource management**
- **Examples**:
  - Arcane Blast (cast at 4 charges)
  - Emergency spells (cast at low mana)
  - Combo point finishers
- **Configuration**:
  - Resource type (Mana, Arcane Charges, Spell Stacks)
  - Threshold (At least/At most/Exactly X)
- **Use Case**: Resource management and optimization

### **💔 Target Health Conditions**
**For spells that work best at specific health ranges**
- **Examples**:
  - Execute phase abilities (below 35%)
  - Burn phase spells (above 80%)
  - Finishing moves
- **Configuration**:
  - Health operator (Below/Above/Between)
  - Health percentage threshold
- **Use Case**: Phase-specific abilities

### **⏰ Cooldown Synchronization**
**For spells that should be used with major cooldowns**
- **Examples**:
  - Pyroblast with Combustion
  - Frostbolt with Icy Veins
  - Burst spells with Arcane Power
- **Configuration**:
  - Sync spell (Combustion, Icy Veins, etc.)
  - Timing (Cast together/before/after)
- **Use Case**: Cooldown optimization and burst windows

### **🎲 Proc/Trigger Conditions**
**For spells that trigger from other events**
- **Examples**:
  - Ignite spread on critical hits
  - Missile procs on spell casts
  - Reactive abilities
- **Configuration**:
  - Trigger event (Spell crit, cast, damage taken, etc.)
  - Proc chance percentage
- **Use Case**: Reactive spells and proc chains

---

## 🚀 **How to Use the WoW Spell Builder**

### **Step 1: Navigate to Spell Builder**
1. Go to **Spells** section
2. Click **"Advanced Mode"** button
3. You'll see the **"Easy Spell Builder"**

### **Step 2: Choose Spell Type**
Pick what your spell does:
- **🔥 Deal Damage**: Fireball, Frostbolt, Arcane Missiles
- **💚 Heal & Support**: Heal, Shield, Buff spells
- **✋ Control Enemies**: Stun, Slow, Polymorph
- **✨ Special Effects**: Teleport, Invisibility, Utility

### **Step 3: Select WoW Condition**
Choose the **authentic WoW condition** that matches your spell:

#### **For Basic Rotation Spells:**
- Choose **"Always Available"**
- Set priority: High (cast often) / Medium (normal) / Low (filler)

#### **For Proc-Based Spells:**
- Choose **"Buff-Dependent"**
- Select buff: Hot Streak, Brain Freeze, Fingers of Frost, etc.
- Set minimum stacks if needed

#### **For Resource Management:**
- Choose **"Resource-Based"**
- Select resource: Mana, Arcane Charges, Spell Stacks
- Set threshold: "At least 4 charges" or "Below 20% mana"

#### **For Execute/Burn Phases:**
- Choose **"Target Health"**
- Set condition: "Below 35%" for execute, "Above 80%" for burn

#### **For Cooldown Windows:**
- Choose **"Cooldown Sync"**
- Select major cooldown: Combustion, Icy Veins, Arcane Power
- Set timing: Cast together/before/after

#### **For Reactive Spells:**
- Choose **"Proc/Trigger"**
- Select trigger: Spell crit, any cast, damage taken
- Set proc chance: 15-30% typically

### **Step 4: Configure Power Level**
- Set damage, mana cost, cooldown
- Use the defaults for balanced spells
- Click **"Create Spell"**

---

## 🎯 **Real WoW Examples**

### **🔥 Fire Mage Rotation**

#### **Fireball (Basic Spell)**
- **Condition**: Always Available
- **Priority**: Medium
- **Use**: Filler spell, generates Hot Streak

#### **Pyroblast (Proc Spell)**
- **Condition**: Buff-Dependent
- **Required Buff**: Hot Streak
- **Use**: Instant cast with Hot Streak proc

#### **Combustion Pyroblast (Cooldown Sync)**
- **Condition**: Cooldown Sync
- **Sync With**: Combustion
- **Timing**: Cast together
- **Use**: Burst window opener

### **❄️ Frost Mage Rotation**

#### **Frostbolt (Basic Spell)**
- **Condition**: Always Available
- **Priority**: High
- **Use**: Main nuke, generates procs

#### **Ice Lance (Proc Spell)**
- **Condition**: Buff-Dependent
- **Required Buff**: Fingers of Frost
- **Use**: Instant cast with proc

#### **Frozen Orb (Cooldown)**
- **Condition**: Always Available
- **Priority**: High
- **Use**: Cast on cooldown for procs

### **🔮 Arcane Mage Rotation**

#### **Arcane Blast (Resource Management)**
- **Condition**: Resource-Based
- **Resource**: Arcane Charges
- **Threshold**: "At most 3 charges"
- **Use**: Build charges

#### **Arcane Barrage (Resource Dump)**
- **Condition**: Resource-Based
- **Resource**: Arcane Charges
- **Threshold**: "At least 4 charges"
- **Use**: Spend charges

#### **Arcane Power Blast (Cooldown Sync)**
- **Condition**: Cooldown Sync
- **Sync With**: Arcane Power
- **Use**: Enhanced damage window

---

## 💡 **Pro Tips for WoW Spell Building**

### **🎯 Rotation Optimization:**
1. **Always Available** spells = your filler/builder spells
2. **Buff-Dependent** spells = your proc consumers
3. **Resource-Based** spells = your resource management
4. **Cooldown Sync** spells = your burst windows

### **🔥 Fire Mage Focus:**
- Use **Buff-Dependent** for Hot Streak consumers
- Use **Cooldown Sync** for Combustion windows
- Use **Proc/Trigger** for Ignite spread mechanics

### **❄️ Frost Mage Focus:**
- Use **Buff-Dependent** for Fingers of Frost/Brain Freeze
- Use **Target Health** for Shatter combos
- Use **Always Available** for consistent damage

### **🔮 Arcane Mage Focus:**
- Use **Resource-Based** for charge management
- Use **Cooldown Sync** for Arcane Power windows
- Use **Target Health** for burn/conserve phases

---

## 🎉 **Result: Authentic WoW Spell Logic**

### **✅ What You Get:**
- **Real WoW conditions** that make sense for mage rotations
- **Authentic spell mechanics** like buff requirements and resource management
- **Proper cooldown synchronization** for burst windows
- **Realistic proc systems** for reactive gameplay
- **Phase-based casting** for execute and burn phases

### **✅ Perfect For:**
- **Rotation optimization** and spell priority
- **Cooldown management** and burst windows
- **Resource management** and efficiency
- **Proc tracking** and reactive casting
- **Phase transitions** and situational spells

**The spell builder now uses real WoW spell logic that will help you create authentic rotation helpers!** 🔥✨

---

*Spell builder redesigned with authentic WoW mechanics*
*From generic conditions to real mage spell logic*
*Perfect for creating rotation optimization tools*
