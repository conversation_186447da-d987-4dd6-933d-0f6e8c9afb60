<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WoW Simulator - Advanced Effects Demo</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-hero {
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--card-bg) 50%, var(--darker-bg) 100%);
            padding: 4rem 0;
            text-align: center;
            border-bottom: 3px solid var(--primary-gold);
        }
        
        .demo-title {
            font-family: 'Cinzel', serif;
            font-size: 3rem;
            color: var(--primary-gold);
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: var(--text-muted);
            margin-bottom: 2rem;
        }
        
        .spell-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .spell-demo-card {
            background: var(--card-bg);
            border-radius: 15px;
            border: 2px solid var(--border-color);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .spell-demo-card:hover {
            transform: translateY(-8px);
            border-color: var(--primary-gold);
            box-shadow: 0 15px 40px rgba(244, 208, 63, 0.3);
        }
        
        .spell-demo-header {
            padding: 1.5rem;
            background: linear-gradient(45deg, var(--primary-purple), var(--primary-blue));
            position: relative;
            overflow: hidden;
        }
        
        .spell-demo-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }
        
        .spell-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .spell-demo-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            color: white;
            margin: 0;
            position: relative;
            z-index: 1;
        }
        
        .spell-demo-body {
            padding: 1.5rem;
        }
        
        .effect-preview {
            background: var(--darker-bg);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary-gold);
        }
        
        .effect-name {
            font-weight: 700;
            color: var(--primary-gold);
            margin-bottom: 0.5rem;
        }
        
        .effect-description {
            color: var(--text-muted);
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .demo-button {
            width: 100%;
            margin-top: 1rem;
            background: linear-gradient(45deg, var(--primary-gold), #f1c40f);
            color: var(--dark-bg);
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(244, 208, 63, 0.4);
        }
        
        .back-link {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: var(--card-bg);
            color: var(--primary-gold);
            padding: 1rem;
            border-radius: 50%;
            text-decoration: none;
            font-size: 1.5rem;
            border: 2px solid var(--primary-gold);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-link:hover {
            background: var(--primary-gold);
            color: var(--dark-bg);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link" title="Back to Main Interface">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="demo-hero">
        <div class="container">
            <h1 class="demo-title">🔥 Advanced Spell Effects</h1>
            <p class="demo-subtitle">Experience the power of complex spell mechanics and interactions</p>
        </div>
    </div>

    <main class="main">
        <div class="container">
            <div class="spell-showcase">
                <!-- Living Bomb Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">💣</div>
                        <h3 class="spell-demo-title">Living Bomb</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>A devastating DoT that explodes when it expires, dealing massive AoE damage.</p>
                        
                        <div class="effect-preview">
                            <div class="effect-name">🔥 Burning DoT</div>
                            <div class="effect-description">
                                Burns target for 92 damage every 3 seconds for 12 seconds
                            </div>
                        </div>
                        
                        <div class="effect-preview">
                            <div class="effect-name">💥 Explosive Finale</div>
                            <div class="effect-description">
                                When DoT expires, explodes for 690 damage to all enemies within 10 yards
                            </div>
                        </div>
                        
                        <button class="demo-button" onclick="loadSpellDemo('living_bomb')">
                            <i class="fas fa-magic"></i> Try Living Bomb
                        </button>
                    </div>
                </div>

                <!-- Chain Lightning Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">⚡</div>
                        <h3 class="spell-demo-title">Chain Lightning</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Lightning that jumps between enemies, losing power with each jump.</p>
                        
                        <div class="effect-preview">
                            <div class="effect-name">⚡ Lightning Chain</div>
                            <div class="effect-description">
                                Jumps to up to 3 nearby enemies, losing 30% damage per jump
                            </div>
                        </div>
                        
                        <div class="effect-preview">
                            <div class="effect-name">🎯 Smart Targeting</div>
                            <div class="effect-description">
                                Automatically finds the closest enemies within 8 yards
                            </div>
                        </div>
                        
                        <button class="demo-button" onclick="loadSpellDemo('chain_lightning')">
                            <i class="fas fa-bolt"></i> Try Chain Lightning
                        </button>
                    </div>
                </div>

                <!-- Hot Streak Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">🔥</div>
                        <h3 class="spell-demo-title">Hot Streak (Proc)</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Passive proc that triggers from fire spell crits, enabling instant Pyroblast.</p>

                        <div class="effect-preview">
                            <div class="effect-name">🎯 Passive Proc</div>
                            <div class="effect-description">
                                Automatically triggers when any fire spell critically hits
                            </div>
                        </div>

                        <div class="effect-preview">
                            <div class="effect-name">⚡ Instant Pyroblast</div>
                            <div class="effect-description">
                                Grants 10-second buff making next Pyroblast instant cast
                            </div>
                        </div>

                        <button class="demo-button" onclick="loadSpellDemo('hot_streak_proc')">
                            <i class="fas fa-fire"></i> Try Hot Streak Proc
                        </button>
                    </div>
                </div>

                <!-- Pyroblast Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">💥</div>
                        <h3 class="spell-demo-title">Pyroblast</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Devastating fire spell that becomes instant with Hot Streak proc.</p>

                        <div class="effect-preview">
                            <div class="effect-name">🔥 Massive Damage</div>
                            <div class="effect-description">
                                664-874 base damage with 6-second cast time
                            </div>
                        </div>

                        <div class="effect-preview">
                            <div class="effect-name">⚡ Hot Streak Synergy</div>
                            <div class="effect-description">
                                Becomes instant cast when Hot Streak buff is active
                            </div>
                        </div>

                        <div class="effect-preview">
                            <div class="effect-name">🔥 Burning DoT</div>
                            <div class="effect-description">
                                Adds 56 damage every 3 seconds for 12 seconds
                            </div>
                        </div>

                        <button class="demo-button" onclick="loadSpellDemo('pyroblast')">
                            <i class="fas fa-fire-alt"></i> Try Pyroblast
                        </button>
                    </div>
                </div>

                <!-- Arcane Blast Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">🔮</div>
                        <h3 class="spell-demo-title">Arcane Blast</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Stacking spell that grows more powerful and expensive with each cast.</p>
                        
                        <div class="effect-preview">
                            <div class="effect-name">📈 Stacking Power</div>
                            <div class="effect-description">
                                Each stack increases damage by 75% (up to 4 stacks)
                            </div>
                        </div>
                        
                        <div class="effect-preview">
                            <div class="effect-name">💰 Escalating Cost</div>
                            <div class="effect-description">
                                Mana cost increases by 175% per stack
                            </div>
                        </div>
                        
                        <button class="demo-button" onclick="loadSpellDemo('arcane_blast')">
                            <i class="fas fa-gem"></i> Try Arcane Blast
                        </button>
                    </div>
                </div>

                <!-- Ignite Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">🔥</div>
                        <h3 class="spell-demo-title">Ignite</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Fire mastery that causes critical strikes to burn targets over time.</p>
                        
                        <div class="effect-preview">
                            <div class="effect-name">🔥 Burning Crits</div>
                            <div class="effect-description">
                                Fire spell crits cause 40% of damage as DoT over 4 seconds
                            </div>
                        </div>
                        
                        <div class="effect-preview">
                            <div class="effect-name">📚 Stacking Burns</div>
                            <div class="effect-description">
                                Multiple ignites stack up to 5 times for massive damage
                            </div>
                        </div>
                        
                        <button class="demo-button" onclick="loadSpellDemo('ignite')">
                            <i class="fas fa-fire-alt"></i> Try Ignite
                        </button>
                    </div>
                </div>

                <!-- Polymorph Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">🐑</div>
                        <h3 class="spell-demo-title">Polymorph</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Transform enemies into harmless sheep, removing them from combat.</p>
                        
                        <div class="effect-preview">
                            <div class="effect-name">🐑 Transformation</div>
                            <div class="effect-description">
                                Target becomes a sheep for 8 seconds, unable to act
                            </div>
                        </div>
                        
                        <div class="effect-preview">
                            <div class="effect-name">🎯 Humanoid Only</div>
                            <div class="effect-description">
                                Only works on humanoid targets
                            </div>
                        </div>
                        
                        <button class="demo-button" onclick="loadSpellDemo('polymorph')">
                            <i class="fas fa-magic"></i> Try Polymorph
                        </button>
                    </div>
                </div>

                <!-- Clearcasting Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">✨</div>
                        <h3 class="spell-demo-title">Clearcasting (Proc)</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Arcane mastery that makes spells occasionally cost no mana.</p>

                        <div class="effect-preview">
                            <div class="effect-name">🎲 Random Proc</div>
                            <div class="effect-description">
                                10% chance to trigger on any spell cast
                            </div>
                        </div>

                        <div class="effect-preview">
                            <div class="effect-name">💙 Free Spell</div>
                            <div class="effect-description">
                                Next spell costs 0 mana for 15 seconds
                            </div>
                        </div>

                        <button class="demo-button" onclick="loadSpellDemo('clearcasting')">
                            <i class="fas fa-gem"></i> Try Clearcasting
                        </button>
                    </div>
                </div>

                <!-- Impact Demo -->
                <div class="spell-demo-card">
                    <div class="spell-demo-header">
                        <div class="spell-icon">💥</div>
                        <h3 class="spell-demo-title">Impact (Proc Chain)</h3>
                    </div>
                    <div class="spell-demo-body">
                        <p>Advanced proc that triggers when other procs activate - proc chains!</p>

                        <div class="effect-preview">
                            <div class="effect-name">🔗 Proc Chain</div>
                            <div class="effect-description">
                                Triggers when Hot Streak proc activates
                            </div>
                        </div>

                        <div class="effect-preview">
                            <div class="effect-name">🌊 DoT Spread</div>
                            <div class="effect-description">
                                Spreads burning effects to nearby enemies
                            </div>
                        </div>

                        <button class="demo-button" onclick="loadSpellDemo('impact')">
                            <i class="fas fa-fire-alt"></i> Try Impact
                        </button>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin: 3rem 0;">
                <a href="index.html" class="btn btn-primary" style="font-size: 1.2rem; padding: 1rem 2rem;">
                    <i class="fas fa-arrow-left"></i> Return to Main Interface
                </a>
            </div>
        </div>
    </main>

    <script>
        function loadSpellDemo(templateName) {
            // Store the template name and redirect to main interface
            localStorage.setItem('demoTemplate', templateName);
            window.location.href = 'index.html#spells';
        }
        
        // Add some magical particle effects
        function createMagicalParticles() {
            const particles = document.createElement('div');
            particles.style.position = 'fixed';
            particles.style.top = '0';
            particles.style.left = '0';
            particles.style.width = '100%';
            particles.style.height = '100%';
            particles.style.pointerEvents = 'none';
            particles.style.zIndex = '-1';
            
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.background = '#f4d03f';
                particle.style.borderRadius = '50%';
                particle.style.opacity = Math.random();
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animation = `float ${3 + Math.random() * 4}s ease-in-out infinite`;
                particles.appendChild(particle);
            }
            
            document.body.appendChild(particles);
        }
        
        // Add CSS for floating animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
                50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // Initialize particles when page loads
        document.addEventListener('DOMContentLoaded', createMagicalParticles);
    </script>
</body>
</html>
