"""
Advanced Spell Effects Demo
Shows complex WoW-like spell mechanics in action.
"""

from src.character import Modu<PERSON><PERSON><PERSON><PERSON>
from src.spells.spell_builder import Spell<PERSON>uilder
try:
    from src.spells.spell_builder import AdvancedSpellTemplateLibrary
except ImportError:
    # Fallback if AdvancedSpellTemplateLibrary is not available
    class AdvancedSpellTemplateLibrary:
        @staticmethod
        def get_all_advanced_templates():
            return {}

from src.spells.advanced_effects import AdvancedEffectProcessor, EffectCondition, ConditionType
from src.stats.standalone_stats import StatType
import time


def create_test_characters():
    """Create characters for testing advanced effects."""
    # Fire Mage
    mage = ModularCharacter("Fire Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 350)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.25)
    mage.current_mana = 5000
    mage.current_health = mage.get_max_health()
    
    # Target dummy
    target = ModularCharacter("Target Dummy")
    target.stats.set_stat(StatType.HEALTH, 5000)
    target.current_health = target.get_max_health()
    
    # Low health target for Execute testing
    low_hp_target = ModularCharacter("Low HP Target")
    low_hp_target.stats.set_stat(StatType.HEALTH, 2000)
    low_hp_target.current_health = 300  # 15% health
    
    return mage, target, low_hp_target


def demo_living_bomb():
    """Demonstrate Living Bomb with conditional explosion."""
    print("=== Living Bomb Demo ===")
    print("DoT that explodes when it expires")
    print()
    
    mage, target, _ = create_test_characters()
    builder = SpellBuilder()
    effect_processor = AdvancedEffectProcessor()
    
    # Get Living Bomb template
    templates = AdvancedSpellTemplateLibrary.get_all_advanced_templates()
    living_bomb_data = templates['living_bomb']
    
    print(f"Casting {living_bomb_data['name']} on {target.name}")
    print(f"Target health: {target.current_health}/{target.get_max_health()}")
    print()
    
    # Simulate the DoT ticking
    dot_damage = 92
    ticks = 4  # 12 seconds / 3 second intervals
    
    for tick in range(ticks):
        target.current_health -= dot_damage
        target.current_health = max(0, target.current_health)
        print(f"Tick {tick + 1}: {dot_damage} damage, target health: {target.current_health}")
        time.sleep(0.5)
    
    # Simulate explosion when DoT expires
    explosion_context = {
        'caster': mage,
        'target': target,
        'spell_critical': False,
        'damage': 690
    }
    
    print(f"\n💥 Living Bomb explodes!")
    target.current_health -= 690
    target.current_health = max(0, target.current_health)
    print(f"Explosion damage: 690, target health: {target.current_health}")
    
    print(f"Total damage: {(dot_damage * ticks) + 690}")


def demo_chain_lightning():
    """Demonstrate Chain Lightning effect."""
    print("\n=== Chain Lightning Demo ===")
    print("Lightning that jumps between targets")
    print()
    
    mage, primary_target, _ = create_test_characters()
    effect_processor = AdvancedEffectProcessor()
    
    # Create additional targets for chaining
    targets = [primary_target]
    for i in range(3):
        chain_target = ModularCharacter(f"Chain Target {i + 1}")
        chain_target.stats.set_stat(StatType.HEALTH, 2000)
        chain_target.current_health = chain_target.get_max_health()
        targets.append(chain_target)
    
    print(f"Casting Chain Lightning on {primary_target.name}")
    print(f"Available targets: {[t.name for t in targets]}")
    print()
    
    # Simulate chain effect
    initial_damage = 500
    current_damage = initial_damage
    damage_reduction = 0.15  # 15% reduction per jump
    
    for i, target in enumerate(targets):
        if i > 0:  # Reduce damage for chain jumps
            current_damage *= (1.0 - damage_reduction)
        
        target.current_health -= current_damage
        target.current_health = max(0, target.current_health)
        
        jump_text = f"Jump {i + 1}" if i > 0 else "Primary target"
        print(f"{jump_text}: {current_damage:.0f} damage to {target.name}")
        print(f"  {target.name} health: {target.current_health:.0f}/{target.get_max_health()}")
        
        time.sleep(0.3)
    
    total_damage = sum([initial_damage * ((1.0 - damage_reduction) ** i) for i in range(len(targets))])
    print(f"\nTotal chain damage: {total_damage:.0f}")


def demo_ignite_proc():
    """Demonstrate Ignite proc on critical hits."""
    print("\n=== Ignite Proc Demo ===")
    print("Fireball that procs Ignite on critical hits")
    print()
    
    mage, target, _ = create_test_characters()
    effect_processor = AdvancedEffectProcessor()
    
    print(f"Mage crit chance: {mage.get_crit_chance() * 100:.1f}%")
    print(f"Casting Fireball on {target.name}")
    print()
    
    # Simulate multiple Fireball casts
    for cast in range(5):
        fireball_damage = 550
        is_crit = mage.get_crit_chance() > 0.2  # Simulate crit for demo
        
        if is_crit:
            fireball_damage *= 2  # Crit multiplier
            ignite_damage = int(fireball_damage * 0.4)  # 40% of crit damage
            
            print(f"Cast {cast + 1}: {fireball_damage} CRITICAL damage!")
            print(f"  💥 Ignite proc! {ignite_damage} damage over 4 seconds")
            
            # Apply Ignite DoT
            target.current_health -= fireball_damage
            for tick in range(2):  # 4 seconds / 2 second ticks
                target.current_health -= (ignite_damage // 2)
                target.current_health = max(0, target.current_health)
                print(f"    Ignite tick: {ignite_damage // 2} damage")
                time.sleep(0.2)
        else:
            print(f"Cast {cast + 1}: {fireball_damage} damage")
            target.current_health -= fireball_damage
        
        target.current_health = max(0, target.current_health)
        print(f"  Target health: {target.current_health}")
        print()
        
        if target.current_health <= 0:
            break


def demo_execute_conditional():
    """Demonstrate Execute with conditional damage bonus."""
    print("\n=== Execute Conditional Demo ===")
    print("Deals massive damage to low health targets")
    print()
    
    mage, normal_target, low_hp_target = create_test_characters()
    
    execute_damage = 225  # Base execute damage
    
    # Test on normal health target
    print(f"Execute vs {normal_target.name} (full health):")
    health_percent = (normal_target.current_health / normal_target.get_max_health()) * 100
    print(f"  Target health: {health_percent:.1f}%")
    print(f"  Execute damage: {execute_damage} (normal)")
    normal_target.current_health -= execute_damage
    print(f"  New health: {normal_target.current_health}")
    print()
    
    # Test on low health target
    print(f"Execute vs {low_hp_target.name} (low health):")
    health_percent = (low_hp_target.current_health / low_hp_target.get_max_health()) * 100
    print(f"  Target health: {health_percent:.1f}%")
    
    # Apply conditional bonus (300% damage if health < 20%)
    if health_percent <= 20:
        execute_damage_bonus = execute_damage * 3  # 300% damage
        print(f"  💀 Execute bonus triggered! {execute_damage_bonus} damage!")
        low_hp_target.current_health -= execute_damage_bonus
    else:
        print(f"  Execute damage: {execute_damage} (normal)")
        low_hp_target.current_health -= execute_damage
    
    low_hp_target.current_health = max(0, low_hp_target.current_health)
    print(f"  New health: {low_hp_target.current_health}")


def demo_arcane_blast_stacking():
    """Demonstrate Arcane Blast stacking mechanic."""
    print("\n=== Arcane Blast Stacking Demo ===")
    print("Spell that gets stronger with each cast")
    print()
    
    mage, target, _ = create_test_characters()
    
    base_damage = 400
    base_mana_cost = 195
    damage_bonus_per_stack = 0.75  # 75% more damage per stack
    mana_multiplier_per_stack = 1.75  # 175% more mana per stack
    
    print(f"Casting Arcane Blast repeatedly on {target.name}")
    print(f"Base damage: {base_damage}, Base mana: {base_mana_cost}")
    print()
    
    stacks = 0
    max_stacks = 4
    
    for cast in range(6):  # Cast more than max stacks to show reset
        if stacks < max_stacks:
            stacks += 1
        
        # Calculate damage and mana with stacks
        damage_multiplier = 1.0 + (damage_bonus_per_stack * (stacks - 1))
        mana_multiplier = 1.0 + ((mana_multiplier_per_stack - 1.0) * (stacks - 1))
        
        current_damage = int(base_damage * damage_multiplier)
        current_mana_cost = int(base_mana_cost * mana_multiplier)
        
        print(f"Cast {cast + 1} (Stack {stacks}):")
        print(f"  Damage: {current_damage} ({damage_multiplier:.1f}x)")
        print(f"  Mana cost: {current_mana_cost} ({mana_multiplier:.1f}x)")
        
        # Apply damage and mana cost
        target.current_health -= current_damage
        mage.current_mana -= current_mana_cost
        
        print(f"  Target health: {target.current_health}")
        print(f"  Mage mana: {mage.current_mana}")
        print()
        
        # Simulate stack expiration after 4 casts
        if cast == 3:
            print("  ⏰ Stacks expire after 6 seconds!")
            stacks = 0
        
        time.sleep(0.3)


def demo_spell_synergies():
    """Demonstrate spell synergies and combinations."""
    print("\n=== Spell Synergies Demo ===")
    print("Spells that work better together")
    print()
    
    mage, target, _ = create_test_characters()
    
    print("Hot Streak combo demonstration:")
    print("Two critical hits in a row make next Pyroblast instant")
    print()
    
    # Simulate Hot Streak proc
    hot_streak_active = False
    
    spells_cast = [
        ("Fireball", 550, True),   # Crit
        ("Fireball", 550, True),   # Crit - triggers Hot Streak
        ("Pyroblast", 950, False)  # Instant cast due to Hot Streak
    ]
    
    consecutive_crits = 0
    
    for spell_name, damage, is_crit in spells_cast:
        if is_crit:
            damage *= 2  # Crit multiplier
            consecutive_crits += 1
            crit_text = " (CRITICAL!)"
        else:
            consecutive_crits = 0
            crit_text = ""
        
        # Check for Hot Streak proc
        if consecutive_crits >= 2 and not hot_streak_active:
            hot_streak_active = True
            print("🔥 HOT STREAK! Next Pyroblast is instant!")
        
        # Apply Hot Streak benefit
        if spell_name == "Pyroblast" and hot_streak_active:
            print(f"⚡ Instant {spell_name}: {damage} damage{crit_text}")
            hot_streak_active = False
        else:
            cast_time = 2.5 if spell_name == "Fireball" else 6.0
            print(f"{spell_name} ({cast_time}s cast): {damage} damage{crit_text}")
        
        target.current_health -= damage
        target.current_health = max(0, target.current_health)
        print(f"  Target health: {target.current_health}")
        print()
        
        time.sleep(0.4)


def main():
    """Run all advanced effects demonstrations."""
    print("🔮 Advanced Spell Effects System Demo")
    print("=" * 60)
    print("Experience complex WoW-like spell mechanics!")
    print("=" * 60)
    
    demo_living_bomb()
    demo_chain_lightning()
    demo_ignite_proc()
    demo_execute_conditional()
    demo_arcane_blast_stacking()
    demo_spell_synergies()
    
    print("=" * 60)
    print("✅ Advanced Effects Demo Complete!")
    print("\nComplex mechanics demonstrated:")
    print("🔥 Conditional explosions (Living Bomb)")
    print("⚡ Chain effects (Chain Lightning)")
    print("💥 Proc-based effects (Ignite)")
    print("💀 Conditional damage (Execute)")
    print("📈 Stacking mechanics (Arcane Blast)")
    print("🔗 Spell synergies (Hot Streak)")
    print("\nYour spell creation system now supports:")
    print("• Complex conditional logic")
    print("• Multi-target chain effects")
    print("• Proc-based spell interactions")
    print("• Stacking and synergy mechanics")
    print("• Real WoW-like spell behavior!")


if __name__ == "__main__":
    main()
