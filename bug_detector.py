#!/usr/bin/env python3
"""
Comprehensive Bug Detector for WoW Simulator
Scans for common issues, potential bugs, and inconsistencies.
"""

import os
import re
import json
from pathlib import Path

class BugDetector:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.base_dir = Path(__file__).parent
    
    def add_issue(self, severity, category, file_path, line_num, description):
        """Add an issue to the list."""
        self.issues.append({
            'severity': severity,
            'category': category,
            'file': file_path,
            'line': line_num,
            'description': description
        })
    
    def add_warning(self, category, file_path, description):
        """Add a warning to the list."""
        self.warnings.append({
            'category': category,
            'file': file_path,
            'description': description
        })
    
    def check_javascript_issues(self):
        """Check for common JavaScript issues."""
        js_file = self.base_dir / 'static/js/app.js'
        
        if not js_file.exists():
            self.add_issue('HIGH', 'Missing File', 'static/js/app.js', 0, 'Main JavaScript file missing')
            return
        
        with open(js_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            # Check for potential null reference issues
            if re.search(r'document\.getElementById\([^)]+\)\.[^?]', line) and 'if (' not in line and 'safeGet' not in line:
                if 'console.warn' not in line and 'try {' not in lines[max(0, i-3):i]:
                    self.add_warning('Null Reference', f'static/js/app.js:{i}', 
                                   'Potential null reference without safety check')
            
            # Check for undefined variables
            if re.search(r'let\s+(\w+);', line):
                var_name = re.search(r'let\s+(\w+);', line).group(1)
                # Check if variable is used before assignment in next few lines
                for j in range(i, min(i+10, len(lines))):
                    if var_name in lines[j] and '=' not in lines[j]:
                        self.add_warning('Undefined Variable', f'static/js/app.js:{j+1}', 
                                       f'Variable {var_name} may be used before assignment')
                        break
            
            # Check for missing error handling in async functions
            if 'async function' in line and i < len(lines) - 5:
                has_try_catch = any('try {' in lines[j] for j in range(i, min(i+10, len(lines))))
                if not has_try_catch:
                    self.add_warning('Missing Error Handling', f'static/js/app.js:{i}', 
                                   'Async function without try-catch block')
    
    def check_html_issues(self):
        """Check for HTML issues."""
        html_file = self.base_dir / 'index.html'
        
        if not html_file.exists():
            self.add_issue('HIGH', 'Missing File', 'index.html', 0, 'Main HTML file missing')
            return
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # Check for missing required elements
        required_elements = [
            ('loading-overlay', 'Loading overlay for JavaScript compatibility'),
            ('notifications', 'Notifications container for error messages'),
            ('spell-name', 'Spell name input field'),
            ('spell-description', 'Spell description field')
        ]
        
        for element_id, description in required_elements:
            if f'id="{element_id}"' not in content:
                self.add_warning('Missing Element', 'index.html', 
                               f'Missing element: {element_id} - {description}')
        
        # Check for unclosed tags
        for i, line in enumerate(lines, 1):
            # Simple check for unclosed div tags
            open_divs = line.count('<div')
            close_divs = line.count('</div>')
            if open_divs > close_divs + 1:  # Allow for multi-line divs
                self.add_warning('Unclosed Tags', f'index.html:{i}', 
                               'Potential unclosed div tags')
    
    def check_css_issues(self):
        """Check for CSS issues."""
        css_file = self.base_dir / 'static/css/style.css'
        
        if not css_file.exists():
            self.add_issue('HIGH', 'Missing File', 'static/css/style.css', 0, 'Main CSS file missing')
            return
        
        with open(css_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            # Check for missing semicolons
            if ':' in line and ';' not in line and '{' not in line and '}' not in line and line.strip():
                if not line.strip().endswith(',') and not line.strip().startswith('/*'):
                    self.add_warning('Missing Semicolon', f'static/css/style.css:{i}', 
                                   'CSS property may be missing semicolon')
            
            # Check for undefined CSS variables
            if 'var(--' in line:
                var_match = re.search(r'var\(--([\w-]+)\)', line)
                if var_match:
                    var_name = var_match.group(1)
                    # This is a simplified check - in a real app you'd maintain a list of defined variables
                    common_vars = ['primary-color', 'secondary-color', 'text-primary', 'text-secondary', 
                                 'card-bg', 'border-color', 'darker-bg']
                    if var_name not in common_vars:
                        self.add_warning('Undefined CSS Variable', f'static/css/style.css:{i}', 
                                       f'CSS variable --{var_name} may not be defined')
    
    def check_backend_issues(self):
        """Check for backend issues."""
        backend_file = self.base_dir / 'web_backend.py'
        
        if not backend_file.exists():
            self.add_warning('Missing File', 'web_backend.py', 'Backend file missing - frontend-only mode')
            return
        
        try:
            with open(backend_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for common Python issues
            if 'import flask' not in content.lower() and 'from flask' not in content.lower():
                self.add_warning('Missing Import', 'web_backend.py', 'Flask import may be missing')
            
            # Check for error handling in routes
            if '@app.route' in content:
                routes = re.findall(r'@app\.route.*?\ndef\s+(\w+)', content, re.DOTALL)
                for route in routes:
                    # Simple check for try-catch in route functions
                    route_pattern = rf'def\s+{route}.*?(?=def|\Z)'
                    route_content = re.search(route_pattern, content, re.DOTALL)
                    if route_content and 'try:' not in route_content.group(0):
                        self.add_warning('Missing Error Handling', 'web_backend.py', 
                                       f'Route {route} may need error handling')
        
        except Exception as e:
            self.add_issue('MEDIUM', 'File Read Error', 'web_backend.py', 0, f'Could not read backend file: {e}')
    
    def check_file_consistency(self):
        """Check for consistency between files."""
        # Check if JavaScript functions referenced in HTML exist
        html_file = self.base_dir / 'index.html'
        js_file = self.base_dir / 'static/js/app.js'
        
        if not html_file.exists() or not js_file.exists():
            return
        
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Find onclick handlers in HTML
        onclick_functions = re.findall(r'onclick="([^"(]+)', html_content)
        
        for func_name in onclick_functions:
            if f'function {func_name}' not in js_content and f'{func_name} =' not in js_content:
                self.add_warning('Missing Function', 'index.html', 
                               f'HTML references function {func_name} that may not exist in JavaScript')
    
    def run_all_checks(self):
        """Run all bug detection checks."""
        print("🔍 Comprehensive Bug Detection")
        print("=" * 50)
        
        checks = [
            ("JavaScript Issues", self.check_javascript_issues),
            ("HTML Issues", self.check_html_issues),
            ("CSS Issues", self.check_css_issues),
            ("Backend Issues", self.check_backend_issues),
            ("File Consistency", self.check_file_consistency)
        ]
        
        for check_name, check_func in checks:
            print(f"\n🔎 Checking {check_name}...")
            try:
                check_func()
                print(f"✅ {check_name} check completed")
            except Exception as e:
                print(f"❌ {check_name} check failed: {e}")
                self.add_issue('HIGH', 'Check Error', check_name, 0, f'Bug check failed: {e}')
    
    def generate_report(self):
        """Generate a comprehensive bug report."""
        print("\n" + "=" * 50)
        print("📊 Bug Detection Report")
        print("=" * 50)
        
        # Count issues by severity
        high_issues = [i for i in self.issues if i['severity'] == 'HIGH']
        medium_issues = [i for i in self.issues if i['severity'] == 'MEDIUM']
        low_issues = [i for i in self.issues if i['severity'] == 'LOW']
        
        print(f"\n📈 Summary:")
        print(f"   High Priority Issues: {len(high_issues)}")
        print(f"   Medium Priority Issues: {len(medium_issues)}")
        print(f"   Low Priority Issues: {len(low_issues)}")
        print(f"   Warnings: {len(self.warnings)}")
        
        # Display high priority issues
        if high_issues:
            print(f"\n🚨 High Priority Issues:")
            for issue in high_issues:
                print(f"   ❌ {issue['category']} in {issue['file']}:{issue['line']}")
                print(f"      {issue['description']}")
        
        # Display medium priority issues
        if medium_issues:
            print(f"\n⚠️  Medium Priority Issues:")
            for issue in medium_issues:
                print(f"   ⚠️  {issue['category']} in {issue['file']}:{issue['line']}")
                print(f"      {issue['description']}")
        
        # Display warnings (limited to first 10)
        if self.warnings:
            print(f"\n💡 Warnings (showing first 10):")
            for warning in self.warnings[:10]:
                print(f"   💡 {warning['category']} in {warning['file']}")
                print(f"      {warning['description']}")
            
            if len(self.warnings) > 10:
                print(f"   ... and {len(self.warnings) - 10} more warnings")
        
        # Overall assessment
        total_critical = len(high_issues) + len(medium_issues)
        
        print(f"\n🎯 Overall Assessment:")
        if total_critical == 0:
            print("   ✅ No critical issues found!")
            print("   🎉 Application appears to be in good condition")
        elif total_critical <= 3:
            print("   ⚠️  Few minor issues found")
            print("   👍 Application is mostly stable")
        else:
            print("   ❌ Multiple issues found")
            print("   🔧 Recommend addressing high priority issues")
        
        return total_critical == 0

def main():
    """Run the bug detector."""
    detector = BugDetector()
    detector.run_all_checks()
    is_clean = detector.generate_report()
    
    return is_clean

if __name__ == "__main__":
    main()
