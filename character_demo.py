"""
Demonstration of the improved modular character system.
Shows how the stats system integrates with character mechanics.
"""

from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.stats.standalone_stats import StatType


def demo_basic_character():
    """Demonstrate basic character creation and stats."""
    print("=== Basic Character Demo ===")
    
    # Create a character
    mage = ModularCharacter("Gandalf")
    
    print(f"Character: {mage.name}")
    print("Base stats:")
    stats = mage.get_stat_summary()
    for stat_name, value in stats.items():
        if "Chance" in stat_name:
            print(f"  {stat_name}: {value:.1f}%")
        else:
            print(f"  {stat_name}: {value:.0f}")


def demo_equipment_system():
    """Demonstrate equipment and stat bonuses."""
    print("\n=== Equipment System Demo ===")
    
    mage = ModularCharacter("Merlin")
    
    print("Before equipment:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    
    # Equip a staff
    mage.equip_item("Staff_of_Power", {
        StatType.SPELL_POWER: 75,
        StatType.MANA: 200,
        StatType.CRIT_CHANCE: 0.02
    })
    
    print("\nAfter equipping Staff of Power:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    print(f"  Crit Chance: {mage.get_crit_chance() * 100:.1f}%")
    
    # Equip robes
    mage.equip_item("Archmage_Robes", {
        StatType.SPELL_POWER: 45,
        StatType.MANA: 300,
        StatType.RESISTANCE_FIRE: 25
    })
    
    print("\nAfter equipping Archmage Robes:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    print(f"  Fire Resistance: {mage.get_resistance('fire'):.0f}")
    
    # Unequip the staff
    mage.unequip_item("Staff_of_Power")
    
    print("\nAfter unequipping staff:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Mana: {mage.get_max_mana():.0f}")


def demo_talent_system():
    """Demonstrate talent system with percentage bonuses."""
    print("\n=== Talent System Demo ===")
    
    mage = ModularCharacter("Saruman")
    
    print("Base character:")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    
    # Learn some talents
    mage.learn_talent("Arcane_Mind", {
        StatType.MANA: 30.0  # 30% more mana
    }, is_percentage=True)
    
    print("\nAfter learning Arcane Mind (30% more mana):")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    
    mage.learn_talent("Arcane_Power", {
        StatType.SPELL_POWER: 10.0  # 10% more spell power
    }, is_percentage=True)
    
    print("\nAfter learning Arcane Power (10% more spell power):")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    
    # Add flat talent bonuses
    mage.learn_talent("Mental_Strength", {
        StatType.MANA: 150  # Flat 150 mana
    }, is_percentage=False)
    
    print("\nAfter learning Mental Strength (+150 mana):")
    print(f"  Mana: {mage.get_max_mana():.0f}")


def demo_complete_character_build():
    """Demonstrate a complete character build."""
    print("\n=== Complete Character Build Demo ===")
    
    # Create a level 60 fire mage
    mage = ModularCharacter("Pyromancer")
    
    # Set higher base stats for level 60
    mage.stats.set_stat(StatType.HEALTH, 3200)
    mage.stats.set_stat(StatType.MANA, 4800)
    mage.stats.set_stat(StatType.SPELL_POWER, 180)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.10)
    
    print("Level 60 Fire Mage Build")
    print("Base stats:")
    stats = mage.get_stat_summary()
    for stat_name, value in stats.items():
        if "Chance" in stat_name:
            print(f"  {stat_name}: {value:.1f}%")
        else:
            print(f"  {stat_name}: {value:.0f}")
    
    # Equipment set
    print("\nEquipping gear...")
    
    # Weapon
    mage.equip_item("Staff_of_Dominance", {
        StatType.SPELL_POWER: 95,
        StatType.CRIT_CHANCE: 0.02,
        StatType.HIT_CHANCE: 0.01
    })
    
    # Armor pieces
    mage.equip_item("Netherwind_Crown", {
        StatType.SPELL_POWER: 35,
        StatType.MANA: 180
    })
    
    mage.equip_item("Netherwind_Robes", {
        StatType.SPELL_POWER: 42,
        StatType.MANA: 220
    })
    
    # Accessories
    mage.equip_item("Ring_of_Spell_Power", {
        StatType.SPELL_POWER: 28
    })
    
    mage.equip_item("Talisman_of_Ephemeral_Power", {
        StatType.SPELL_POWER: 40,
        StatType.CRIT_CHANCE: 0.015
    })
    
    # Talents
    print("Learning talents...")
    
    # Fire tree talents
    mage.learn_talent("Fire_Power", {
        StatType.SPELL_POWER: 10.0  # 10% fire damage
    }, is_percentage=True)
    
    mage.learn_talent("Critical_Mass", {
        StatType.CRIT_CHANCE: 0.06  # 6% crit
    }, is_percentage=False)
    
    # Arcane tree talents
    mage.learn_talent("Arcane_Mind", {
        StatType.MANA: 30.0  # 30% mana
    }, is_percentage=True)
    
    mage.learn_talent("Arcane_Meditation", {
        StatType.MANA: 15.0  # 15% mana regen
    }, is_percentage=True)
    
    print("\nFinal character stats:")
    final_stats = mage.get_stat_summary()
    for stat_name, value in final_stats.items():
        base_value = stats[stat_name]
        bonus = value - base_value
        if "Chance" in stat_name:
            print(f"  {stat_name}: {value:.1f}% (base: {base_value:.1f}%, bonus: +{bonus:.1f}%)")
        else:
            print(f"  {stat_name}: {value:.0f} (base: {base_value:.0f}, bonus: +{bonus:.0f})")
    
    # Show stat breakdown for spell power
    print(f"\nSpell Power breakdown:")
    breakdown = mage.stats.get_stat_breakdown(StatType.SPELL_POWER)
    for key, value in breakdown.items():
        print(f"  {key}: {value}")


def demo_buff_integration():
    """Demonstrate how buffs integrate with the modular stats system."""
    print("\n=== Buff Integration Demo ===")
    
    # This would require updating the Buff class to work with the new system
    # For now, we'll show how it could work conceptually
    
    mage = ModularCharacter("Buffed_Mage")
    
    print("Before buffs:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Crit Chance: {mage.get_crit_chance() * 100:.1f}%")
    
    # Simulate temporary buffs using the stats system directly
    mage.stats.add_flat_bonus("arcane_intellect", {
        StatType.MANA: 400
    })
    
    mage.stats.add_stacking_modifier("arcane_power", {
        StatType.SPELL_POWER: 25
    }, max_stacks=4)
    
    # Add stacks
    mage.stats.add_stack("arcane_power")
    mage.stats.add_stack("arcane_power")
    mage.stats.add_stack("arcane_power")
    
    print("\nAfter buffs (Arcane Intellect + 3 stacks of Arcane Power):")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    
    # Remove a stack
    mage.stats.remove_stack("arcane_power")
    
    print("\nAfter losing 1 stack of Arcane Power:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")


def main():
    """Run all character demonstrations."""
    print("WoW Simulator - Modular Character System Demo")
    print("=" * 55)
    
    demo_basic_character()
    demo_equipment_system()
    demo_talent_system()
    demo_complete_character_build()
    demo_buff_integration()
    
    print("\n" + "=" * 55)
    print("✅ Character system demo complete!")
    print("\nKey benefits of the modular character system:")
    print("✓ Easy equipment management")
    print("✓ Flexible talent system with percentage bonuses")
    print("✓ Automatic stat calculations")
    print("✓ Detailed stat breakdowns for debugging")
    print("✓ Backward compatibility with existing code")
    print("✓ Easy integration with buff/debuff systems")


if __name__ == "__main__":
    main()
