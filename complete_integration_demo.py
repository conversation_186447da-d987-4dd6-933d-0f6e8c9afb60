"""
Complete integration demo showing the modular stats system,
character system, and spell system working together.
"""

from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.stats.standalone_stats import StatType
from src.spells.modular_spells import Spell<PERSON><PERSON><PERSON>, CombatSimulator, SpellSchool


def create_fire_mage() -> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:
    """Create a well-equipped fire mage."""
    mage = Modular<PERSON><PERSON><PERSON>("Pyromancer")
    
    # Set level 60 base stats
    mage.stats.set_stat(StatType.HEALTH, 3200)
    mage.stats.set_stat(StatType.MANA, 4800)
    mage.stats.set_stat(StatType.SPELL_POWER, 180)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.12)
    mage.stats.set_stat(StatType.HIT_CHANCE, 0.85)
    
    # Equipment
    mage.equip_item("Staff_of_Dominance", {
        StatType.SPELL_POWER: 95,
        StatType.CRIT_CHANCE: 0.02
    })
    
    mage.equip_item("Netherwind_Robes", {
        StatType.SPELL_POWER: 42,
        StatType.MANA: 220
    })
    
    mage.equip_item("Ring_of_Spell_Power", {
        StatType.SPELL_POWER: 28
    })
    
    # Talents
    mage.learn_talent("Fire_Power", {
        StatType.SPELL_POWER: 10.0  # 10% more fire damage
    }, is_percentage=True)
    
    mage.learn_talent("Critical_Mass", {
        StatType.CRIT_CHANCE: 0.06  # 6% more crit
    })
    
    mage.learn_talent("Arcane_Mind", {
        StatType.MANA: 30.0  # 30% more mana
    }, is_percentage=True)
    
    # Set current resources
    mage.current_health = mage.get_max_health()
    mage.current_mana = mage.get_max_mana()
    
    return mage


def create_target_dummy() -> ModularCharacter:
    """Create a target dummy for testing."""
    dummy = ModularCharacter("Target Dummy")
    
    # High health, no resistances
    dummy.stats.set_stat(StatType.HEALTH, 10000)
    dummy.stats.set_stat(StatType.ARMOR, 0)
    dummy.current_health = dummy.get_max_health()
    dummy.current_mana = 0
    
    return dummy


def demo_spell_damage_scaling():
    """Demonstrate how spell damage scales with character stats."""
    print("=== Spell Damage Scaling Demo ===")
    
    # Create characters with different gear levels
    base_mage = ModularCharacter("Base Mage")
    base_mage.current_mana = 5000
    
    geared_mage = create_fire_mage()
    
    target = create_target_dummy()
    simulator = CombatSimulator()
    
    # Test fireball damage
    fireball = SpellLibrary.fireball()
    
    print("Fireball damage comparison:")
    print(f"Base Mage stats:")
    print(f"  Spell Power: {base_mage.get_spell_power():.0f}")
    print(f"  Crit Chance: {base_mage.get_crit_chance() * 100:.1f}%")
    
    # Calculate theoretical damage
    base_damage = fireball.calculate_damage(base_mage, target)
    print(f"  Theoretical Fireball damage: {base_damage}")
    
    print(f"\nGeared Mage stats:")
    print(f"  Spell Power: {geared_mage.get_spell_power():.0f}")
    print(f"  Crit Chance: {geared_mage.get_crit_chance() * 100:.1f}%")
    
    geared_damage = fireball.calculate_damage(geared_mage, target)
    print(f"  Theoretical Fireball damage: {geared_damage}")
    
    damage_increase = ((geared_damage - base_damage) / base_damage) * 100
    print(f"  Damage increase: {damage_increase:.1f}%")


def demo_combat_simulation():
    """Demonstrate a combat simulation."""
    print("\n=== Combat Simulation Demo ===")
    
    mage = create_fire_mage()
    target = create_target_dummy()
    simulator = CombatSimulator()
    
    print(f"Combat: {mage.name} vs {target.name}")
    print(f"Mage Health: {mage.current_health:.0f}/{mage.get_max_health():.0f}")
    print(f"Mage Mana: {mage.current_mana:.0f}/{mage.get_max_mana():.0f}")
    print(f"Target Health: {target.current_health:.0f}/{target.get_max_health():.0f}")
    print()
    
    # Cast some spells
    spells_to_cast = [
        SpellLibrary.fireball(),
        SpellLibrary.fireball(),
        SpellLibrary.frostbolt(),
        SpellLibrary.arcane_missiles(),
        SpellLibrary.fireball()
    ]
    
    total_damage = 0
    
    for spell in spells_to_cast:
        if mage.current_mana >= spell.mana_cost:
            result = simulator.simulate_spell_cast(mage, spell, target)
            if result.success and result.damage > 0:
                total_damage += result.damage
            print(f"  Mage Mana: {mage.current_mana:.0f}, Target Health: {target.current_health:.0f}")
        else:
            simulator.log(f"{mage.name} is out of mana!")
            break
        print()
    
    print(f"Total damage dealt: {total_damage}")
    print(f"Target health remaining: {target.current_health:.0f}/{target.get_max_health():.0f}")


def demo_buff_effects_on_spells():
    """Demonstrate how buffs affect spell performance."""
    print("\n=== Buff Effects on Spells Demo ===")
    
    mage = create_fire_mage()
    target = create_target_dummy()
    simulator = CombatSimulator()
    
    fireball = SpellLibrary.fireball()
    
    print("Fireball damage without buffs:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Crit Chance: {mage.get_crit_chance() * 100:.1f}%")
    
    base_damage = fireball.calculate_damage(mage, target)
    print(f"  Theoretical damage: {base_damage}")
    
    # Add temporary buffs
    print("\nApplying buffs...")
    
    # Arcane Intellect
    mage.stats.add_flat_bonus("arcane_intellect", {
        StatType.MANA: 400
    })
    
    # Arcane Power (stacking buff)
    mage.stats.add_stacking_modifier("arcane_power", {
        StatType.SPELL_POWER: 30
    }, max_stacks=4)
    
    # Add 3 stacks
    mage.stats.add_stack("arcane_power")
    mage.stats.add_stack("arcane_power")
    mage.stats.add_stack("arcane_power")
    
    # Greater Blessing of Wisdom (percentage buff)
    mage.stats.add_percentage_bonus("blessing_of_wisdom", {
        StatType.MANA: 10.0
    })
    
    print("Fireball damage with buffs:")
    print(f"  Spell Power: {mage.get_spell_power():.0f}")
    print(f"  Crit Chance: {mage.get_crit_chance() * 100:.1f}%")
    print(f"  Mana: {mage.get_max_mana():.0f}")
    
    buffed_damage = fireball.calculate_damage(mage, target)
    print(f"  Theoretical damage: {buffed_damage}")
    
    damage_increase = ((buffed_damage - base_damage) / base_damage) * 100
    print(f"  Damage increase from buffs: {damage_increase:.1f}%")
    
    # Show detailed breakdown
    print("\nSpell Power breakdown:")
    breakdown = mage.stats.get_stat_breakdown(StatType.SPELL_POWER)
    for key, value in breakdown.items():
        print(f"  {key}: {value}")


def demo_different_spell_schools():
    """Demonstrate different spell schools and resistances."""
    print("\n=== Spell Schools and Resistances Demo ===")
    
    mage = create_fire_mage()
    
    # Create a target with fire resistance
    fire_resistant_target = ModularCharacter("Fire Elemental")
    fire_resistant_target.stats.set_stat(StatType.HEALTH, 5000)
    fire_resistant_target.stats.set_stat(StatType.RESISTANCE_FIRE, 150)  # High fire resistance
    fire_resistant_target.current_health = fire_resistant_target.get_max_health()
    
    # Create a normal target
    normal_target = create_target_dummy()
    
    simulator = CombatSimulator()
    
    spells = [
        SpellLibrary.fireball(),
        SpellLibrary.frostbolt(),
        SpellLibrary.arcane_missiles()
    ]
    
    print("Damage against normal target:")
    for spell in spells:
        damage = spell.calculate_damage(mage, normal_target)
        print(f"  {spell.name} ({spell.school.value}): {damage} damage")
    
    print(f"\nDamage against fire-resistant target (Fire Resistance: {fire_resistant_target.get_resistance('fire')}):")
    for spell in spells:
        damage = spell.calculate_damage(mage, fire_resistant_target)
        print(f"  {spell.name} ({spell.school.value}): {damage} damage")


def main():
    """Run the complete integration demo."""
    print("WoW Simulator - Complete Integration Demo")
    print("=" * 60)
    print("This demo shows the modular stats system, character system,")
    print("and spell system working together seamlessly.")
    print("=" * 60)
    
    demo_spell_damage_scaling()
    demo_combat_simulation()
    demo_buff_effects_on_spells()
    demo_different_spell_schools()
    
    print("\n" + "=" * 60)
    print("✅ Complete integration demo finished!")
    print("\nKey achievements:")
    print("✓ Modular stats system working with character builds")
    print("✓ Spell damage scaling with character stats")
    print("✓ Equipment and talent effects on spell performance")
    print("✓ Buff system integration")
    print("✓ Resistance calculations")
    print("✓ Combat simulation with resource management")
    print("✓ Detailed logging and analysis")
    print("\nThe system is now ready for:")
    print("• Adding new spells easily")
    print("• Creating complex character builds")
    print("• Implementing rotation optimization")
    print("• Adding new stat types without code changes")
    print("• Building a complete WoW combat simulator")


if __name__ == "__main__":
    main()
