"""
Comprehensive test to verify all systems work together without errors.
"""

def test_imports():
    """Test that all imports work correctly."""
    print("Testing imports...")
    
    try:
        # Test core imports
        from src.core.effects import Effect, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, EffectManager
        from src.core.proc import Proc, ProcManager, TriggerCondition, ProcEvent
        from src.core.spell import School, Spell
        print("✓ Core imports successful")
        
        # Test interface imports
        from src.interfaces import StatType, IStatContainer, IStatModifier
        print("✓ Interface imports successful")
        
        # Test stats imports
        from src.stats.standalone_stats import SimpleStatManager, StatType, AdditiveModifier
        print("✓ Stats imports successful")
        
        # Test character imports
        from src.character import Character, ModularCharacter
        print("✓ Character imports successful")
        
        # Test spell imports
        from src.spells.modular_spells import SpellLibrary, CombatSimulator
        print("✓ Spell imports successful")
        
        # Test damage calculator
        from src.damage_calculator import DamageCalculator
        print("✓ Damage calculator import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality of each system."""
    print("\nTesting basic functionality...")
    
    try:
        from src.stats.standalone_stats import SimpleStatManager, StatType
        from src.character import ModularCharacter
        from src.core.effects import Buff, EffectManager
        from src.spells.modular_spells import SpellLibrary
        
        # Test stats system
        stats = SimpleStatManager()
        stats.set_stat(StatType.HEALTH, 1000)
        assert stats.get_stat(StatType.HEALTH) == 1000
        print("✓ Stats system working")
        
        # Test character system
        character = ModularCharacter("Test Character")
        assert character.name == "Test Character"
        assert character.get_max_health() > 0
        print("✓ Character system working")
        
        # Test effects system
        effect_manager = EffectManager()
        buff = Buff("Test Buff", 30.0, {"spell_power": 50})
        effect_manager.add_effect(buff)
        assert effect_manager.get_effect("Test Buff") is not None
        print("✓ Effects system working")
        
        # Test spell system
        fireball = SpellLibrary.fireball()
        assert fireball.name == "Fireball"
        assert fireball.base_damage > 0
        print("✓ Spell system working")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """Test integration between systems."""
    print("\nTesting system integration...")
    
    try:
        from src.character import ModularCharacter
        from src.stats.standalone_stats import StatType
        from src.spells.modular_spells import SpellLibrary, CombatSimulator
        
        # Create characters
        mage = ModularCharacter("Test Mage")
        target = ModularCharacter("Test Target")
        
        # Set up mage
        mage.stats.set_stat(StatType.SPELL_POWER, 200)
        mage.current_mana = 5000
        
        # Set up target
        target.stats.set_stat(StatType.HEALTH, 5000)
        target.current_health = target.get_max_health()
        
        # Test spell casting
        fireball = SpellLibrary.fireball()
        simulator = CombatSimulator()
        
        # Check if spell can be cast
        can_cast, reason = fireball.can_cast(mage, target)
        assert can_cast, f"Should be able to cast: {reason}"
        
        # Cast the spell
        result = fireball.cast(mage, target)
        assert result.success, "Spell cast should succeed"
        assert result.damage > 0, "Should deal damage"
        
        print("✓ System integration working")
        print(f"  Spell damage: {result.damage}")
        print(f"  Was critical: {result.was_critical}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_equipment_and_buffs():
    """Test equipment and buff systems."""
    print("\nTesting equipment and buffs...")
    
    try:
        from src.character import ModularCharacter
        from src.stats.standalone_stats import StatType
        
        character = ModularCharacter("Geared Character")
        
        # Test base stats
        base_spell_power = character.get_spell_power()
        
        # Test equipment
        character.equip_item("Test Staff", {
            StatType.SPELL_POWER: 75,
            StatType.CRIT_CHANCE: 0.02
        })
        
        equipped_spell_power = character.get_spell_power()
        assert equipped_spell_power > base_spell_power, "Equipment should increase stats"
        
        # Test buffs
        character.stats.add_flat_bonus("test_buff", {
            StatType.SPELL_POWER: 50
        })
        
        buffed_spell_power = character.get_spell_power()
        assert buffed_spell_power > equipped_spell_power, "Buffs should increase stats"
        
        # Test stacking buffs
        character.stats.add_stacking_modifier("stacking_buff", {
            StatType.SPELL_POWER: 20
        }, max_stacks=3)
        
        character.stats.add_stack("stacking_buff")
        character.stats.add_stack("stacking_buff")
        
        final_spell_power = character.get_spell_power()
        assert final_spell_power > buffed_spell_power, "Stacking buffs should increase stats"
        
        print("✓ Equipment and buffs working")
        print(f"  Base spell power: {base_spell_power}")
        print(f"  With equipment: {equipped_spell_power}")
        print(f"  With buffs: {buffed_spell_power}")
        print(f"  With stacking buffs: {final_spell_power}")
        
        return True
        
    except Exception as e:
        print(f"❌ Equipment/buffs error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_damage_calculation():
    """Test damage calculation with different stats."""
    print("\nTesting damage calculations...")
    
    try:
        from src.character import ModularCharacter
        from src.stats.standalone_stats import StatType
        from src.spells.modular_spells import SpellLibrary
        
        # Create low-power mage
        weak_mage = ModularCharacter("Weak Mage")
        weak_mage.stats.set_stat(StatType.SPELL_POWER, 50)
        
        # Create high-power mage
        strong_mage = ModularCharacter("Strong Mage")
        strong_mage.stats.set_stat(StatType.SPELL_POWER, 300)
        
        # Create target
        target = ModularCharacter("Target")
        
        # Test damage scaling
        fireball = SpellLibrary.fireball()
        
        weak_damage = fireball.calculate_damage(weak_mage, target)
        strong_damage = fireball.calculate_damage(strong_mage, target)
        
        assert strong_damage > weak_damage, "Higher spell power should deal more damage"
        
        print("✓ Damage calculations working")
        print(f"  Weak mage damage: {weak_damage}")
        print(f"  Strong mage damage: {strong_damage}")
        print(f"  Damage increase: {((strong_damage - weak_damage) / weak_damage * 100):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Damage calculation error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("WoW Simulator - Comprehensive Error Check")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_integration,
        test_equipment_and_buffs,
        test_damage_calculation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is working correctly.")
        print("\nThe WoW Simulator is ready for:")
        print("• Character creation and customization")
        print("• Equipment and talent systems")
        print("• Spell casting and damage calculations")
        print("• Buff/debuff management")
        print("• Combat simulations")
        print("• Rotation optimization")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
