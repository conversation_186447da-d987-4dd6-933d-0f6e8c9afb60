#!/usr/bin/env python3
"""
Demonstration of the comprehensive condition system in WoW Simulator.
Shows examples of all condition categories with realistic spell scenarios.
"""

def print_condition_examples():
    """Print examples of all condition categories."""
    
    print("🎯 WoW Simulator - Comprehensive Conditions Demo")
    print("=" * 60)
    
    examples = {
        "⚡ Spell Conditions": [
            "spell_critical = 'fire' → Fire spell crits trigger Ignite",
            "spell_cast_time >= 2.5 → Long cast spells get interrupt protection",
            "spell_mana_cost >= 300 → Expensive spells trigger Clearcasting",
            "spell_recently_cast = 'Fireball' → Fireball → Fire Blast combo"
        ],
        
        "🎯 Target Conditions": [
            "target_health_below <= 20 → Execute abilities activate",
            "target_casting = true → Interrupt spells become available",
            "target_stunned = true → Bonus damage while target stunned",
            "target_distance <= 5 → Melee range abilities",
            "target_polymorphed = true → Can't target polymorphed enemies"
        ],
        
        "🧙 Caster Conditions": [
            "caster_stealthed = true → Stealth attacks like Cheap Shot",
            "caster_race = 'night elf' → Racial abilities",
            "caster_stance = 'bear' → Druid bear form abilities",
            "caster_weapon_equipped = 'staff' → Caster weapon bonuses",
            "caster_mana_below <= 25 → Mana efficiency procs"
        ],
        
        "🎲 Proc Conditions": [
            "proc_triggered = 'Hot Streak' → Impact spreads DoTs",
            "proc_chance <= 10 → 10% Clearcasting chance",
            "consecutive_procs >= 2 → Bonus after multiple procs",
            "proc_stacks >= 5 → Ignite explosion at max stacks"
        ],
        
        "⚔️ Combat Conditions": [
            "enemies_nearby >= 3 → AoE spells become efficient",
            "combo_points >= 5 → Rogue finisher abilities",
            "threat_level = 'high' → Tank defensive abilities",
            "boss_encounter = true → Raid-specific mechanics"
        ],
        
        "🌍 Environmental Conditions": [
            "time_of_day = 'night' → Night Elf Shadowmeld bonus",
            "weather = 'storm' → Enhanced lightning spells",
            "pvp_zone = true → PvP-specific abilities",
            "instance_type = 'raid' → Raid mechanics"
        ],
        
        "🔧 Advanced Conditions": [
            "damage_taken_recently >= 500 → Defensive cooldowns",
            "spell_reflect_active = true → Avoid casting spells",
            "line_of_sight = false → Can't cast targeted spells",
            "behind_target = true → Backstab and stealth attacks"
        ]
    }
    
    for category, condition_list in examples.items():
        print(f"\n{category}")
        print("-" * 40)
        for condition in condition_list:
            print(f"  • {condition}")
    
    print("\n" + "=" * 60)
    print("🎮 Real WoW Spell Examples")
    print("=" * 60)
    
    spell_examples = [
        {
            "name": "🔥 Combustion",
            "description": "Spreads DoTs when multiple fire effects are active",
            "conditions": [
                "target_has_debuff = 'Ignite' AND",
                "target_has_debuff = 'Living Bomb' AND", 
                "spell_school = 'fire' AND",
                "caster_has_buff = 'Combustion'"
            ]
        },
        {
            "name": "🗡️ Cheap Shot",
            "description": "Rogue stealth attack that only works on humanoids",
            "conditions": [
                "caster_stealthed = true AND",
                "target_type = 'humanoid' AND",
                "target_distance <= 5"
            ]
        },
        {
            "name": "🌙 Shadowmeld Ambush",
            "description": "Night Elf racial with bonus damage at night",
            "conditions": [
                "time_of_day = 'night' AND",
                "caster_race = 'night elf' AND",
                "caster_stealthed = true"
            ]
        },
        {
            "name": "⚡ Storm Strike",
            "description": "Shaman spell enhanced by weather and enemies",
            "conditions": [
                "weather = 'storm' AND",
                "enemies_nearby >= 2 AND",
                "zone_type = 'outdoor'"
            ]
        },
        {
            "name": "🛡️ Lay on Hands",
            "description": "Paladin emergency heal for dire situations",
            "conditions": [
                "target_health_below <= 20 AND",
                "caster_class = 'paladin' AND",
                "holy_power >= 3"
            ]
        }
    ]
    
    for spell in spell_examples:
        print(f"\n{spell['name']}")
        print(f"Description: {spell['description']}")
        print("Conditions:")
        for condition in spell['conditions']:
            print(f"  {condition}")
    
    print("\n" + "=" * 60)
    print("🧠 Advanced Logic Examples")
    print("=" * 60)
    
    logic_examples = [
        {
            "name": "Mana Efficiency",
            "logic": "IF (caster_mana_below <= 25) OR (proc_triggered = 'Clearcasting') OR (time_of_day = 'night' AND caster_race = 'night elf')"
        },
        {
            "name": "PvP Burst Combo", 
            "logic": "IF (player_target = true) AND (target_health_below <= 30) AND (interrupt_available = true) AND (pvp_zone = true)"
        },
        {
            "name": "Raid Healing Priority",
            "logic": "IF (boss_encounter = true) AND (allies_nearby >= 3) AND (group_size >= 20) AND (healing_received <= 100)"
        },
        {
            "name": "Stealth Positioning",
            "logic": "IF (behind_target = true) AND (target_distance <= 5) AND (caster_stealthed = true) AND (weapon_equipped = 'dagger')"
        }
    ]
    
    for example in logic_examples:
        print(f"\n{example['name']}:")
        print(f"  {example['logic']}")
    
    print("\n" + "=" * 60)
    print("🎉 Total Condition Types: 100+")
    print("✨ Create authentic WoW-like spell mechanics!")
    print("🎯 Build complex spell interactions with ease!")
    print("=" * 60)

if __name__ == '__main__':
    print_condition_examples()
