"""
Cooldown and GCD Mechanics Demo
Shows how cooldowns and Global Cooldown work in the WoW Simulator.
"""

import time
from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.spells.spell_builder import SpellBuilder
from src.stats.standalone_stats import StatType


def create_test_spells():
    """Create spells with different cooldown patterns."""
    builder = SpellBuilder()
    
    # Instant spell with no cooldown (GCD limited)
    frostbolt = {
        "name": "Frostbolt",
        "description": "Quick frost spell limited by GCD",
        "school": "frost",
        "cast_time": 0.0,  # Instant
        "cooldown": 0.0,   # No individual cooldown
        "mana_cost": 150,
        "target_type": "single_enemy",
        "base_damage": [200, 250],
        "spell_power_coefficient": 0.8,
        "can_crit": True
    }
    
    # Spell with moderate cooldown
    fireball = {
        "name": "Fireball",
        "description": "Powerful spell with moderate cooldown",
        "school": "fire",
        "cast_time": 2.5,
        "cooldown": 6.0,   # 6 second cooldown
        "mana_cost": 300,
        "target_type": "single_enemy",
        "base_damage": [400, 500],
        "spell_power_coefficient": 1.2,
        "can_crit": True
    }
    
    # Powerful spell with long cooldown
    meteor = {
        "name": "Meteor",
        "description": "Devastating spell with long cooldown",
        "school": "fire",
        "cast_time": 4.0,
        "cooldown": 45.0,  # 45 second cooldown
        "mana_cost": 500,
        "target_type": "ground_target",
        "aoe_radius": 12.0,
        "base_damage": [800, 1000],
        "spell_power_coefficient": 1.5,
        "can_crit": True
    }
    
    # Instant spell with short cooldown
    arcane_orb = {
        "name": "Arcane Orb",
        "description": "Instant spell with short cooldown",
        "school": "arcane",
        "cast_time": 0.0,  # Instant
        "cooldown": 8.0,   # 8 second cooldown
        "mana_cost": 200,
        "target_type": "single_enemy",
        "base_damage": [300, 350],
        "spell_power_coefficient": 1.0,
        "can_crit": True
    }
    
    spells = {}
    for spell_data in [frostbolt, fireball, meteor, arcane_orb]:
        spell = builder.build_spell_from_dict(spell_data)
        spells[spell.name] = spell
    
    return spells


def demo_gcd_mechanics():
    """Demonstrate Global Cooldown mechanics."""
    print("=== Global Cooldown (GCD) Demo ===")
    print("GCD prevents rapid-fire spell casting")
    print()
    
    # Create character and spells
    mage = ModularCharacter("GCD Test Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 200)
    mage.current_mana = 5000
    
    target = ModularCharacter("Target")
    target.stats.set_stat(StatType.HEALTH, 5000)
    target.current_health = target.get_max_health()
    
    spells = create_test_spells()
    frostbolt = spells["Frostbolt"]
    
    print(f"Attempting to cast {frostbolt.name} multiple times rapidly...")
    print(f"Character GCD: {mage.global_cooldown} seconds")
    print()
    
    # Try to cast the same instant spell multiple times
    for attempt in range(5):
        can_cast, reason = frostbolt.can_cast(mage, target)
        
        if can_cast:
            result = frostbolt.cast(mage, target)
            print(f"Cast {attempt + 1}: ✅ Success - {result.damage} damage")
            
            # Show remaining GCD
            current_time = time.time()
            gcd_remaining = (mage.last_cast_time + mage.global_cooldown) - current_time
            if gcd_remaining > 0:
                print(f"         GCD active for {gcd_remaining:.2f} more seconds")
        else:
            print(f"Cast {attempt + 1}: ❌ Failed - {reason}")
        
        # Small delay to show GCD in action
        time.sleep(0.5)
    
    print()


def demo_individual_cooldowns():
    """Demonstrate individual spell cooldowns."""
    print("=== Individual Spell Cooldowns Demo ===")
    print("Each spell has its own cooldown timer")
    print()
    
    mage = ModularCharacter("Cooldown Test Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 250)
    mage.current_mana = 5000
    
    target = ModularCharacter("Target")
    target.stats.set_stat(StatType.HEALTH, 8000)
    target.current_health = target.get_max_health()
    
    spells = create_test_spells()
    
    # Show cooldown information
    print("Spell Cooldowns:")
    for name, spell in spells.items():
        print(f"  {name}: {spell.cooldown}s cooldown, {spell.cast_time}s cast time")
    print()
    
    # Test casting spells with different cooldowns
    spell_order = ["Frostbolt", "Arcane Orb", "Fireball", "Meteor"]
    
    for spell_name in spell_order:
        spell = spells[spell_name]
        
        print(f"Attempting to cast {spell_name}...")
        can_cast, reason = spell.can_cast(mage, target)
        
        if can_cast:
            result = spell.cast(mage, target)
            print(f"  ✅ Cast successful: {result.damage} damage")
            print(f"  ⏰ Spell on cooldown for {spell.cooldown} seconds")
            
            # Try to cast again immediately
            can_cast_again, reason_again = spell.can_cast(mage, target)
            if not can_cast_again:
                print(f"  ❌ Immediate recast failed: {reason_again}")
        else:
            print(f"  ❌ Cast failed: {reason}")
        
        print()


def demo_cooldown_vs_gcd():
    """Demonstrate the interaction between cooldowns and GCD."""
    print("=== Cooldown vs GCD Interaction Demo ===")
    print("Shows how GCD and individual cooldowns work together")
    print()
    
    mage = ModularCharacter("Interaction Test Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 200)
    mage.current_mana = 5000
    
    target = ModularCharacter("Target")
    target.stats.set_stat(StatType.HEALTH, 10000)
    target.current_health = target.get_max_health()
    
    spells = create_test_spells()
    
    # Create a rotation scenario
    rotation = [
        ("Frostbolt", "Instant, no cooldown - limited by GCD"),
        ("Arcane Orb", "Instant, 8s cooldown"),
        ("Frostbolt", "Should work - different spell"),
        ("Arcane Orb", "Should fail - still on cooldown"),
        ("Fireball", "2.5s cast, 6s cooldown")
    ]
    
    print("Simulating a spell rotation:")
    print()
    
    for i, (spell_name, description) in enumerate(rotation):
        spell = spells[spell_name]
        
        print(f"Step {i + 1}: {spell_name} - {description}")
        
        # Check if we can cast
        can_cast, reason = spell.can_cast(mage, target)
        
        if can_cast:
            # Show cast time
            if spell.cast_time > 0:
                print(f"  🕐 Casting for {spell.cast_time} seconds...")
                time.sleep(min(spell.cast_time, 1.0))  # Simulate cast time (max 1s for demo)
            
            result = spell.cast(mage, target)
            print(f"  ✅ Success: {result.damage} damage")
            
            # Show what's on cooldown
            cooldown_status = []
            for check_name, check_spell in spells.items():
                if check_spell.last_cast_time > 0:
                    remaining = (check_spell.last_cast_time + check_spell.cooldown) - time.time()
                    if remaining > 0:
                        cooldown_status.append(f"{check_name}({remaining:.1f}s)")
            
            if cooldown_status:
                print(f"  ⏰ On cooldown: {', '.join(cooldown_status)}")
            
            # Show GCD status
            gcd_remaining = (mage.last_cast_time + mage.global_cooldown) - time.time()
            if gcd_remaining > 0:
                print(f"  🌐 GCD: {gcd_remaining:.1f}s remaining")
        else:
            print(f"  ❌ Failed: {reason}")
        
        print()
        time.sleep(0.5)  # Small delay between casts


def demo_optimal_rotation():
    """Demonstrate an optimal spell rotation considering cooldowns."""
    print("=== Optimal Rotation Demo ===")
    print("Shows how to maximize DPS with cooldown management")
    print()
    
    mage = ModularCharacter("Rotation Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 300)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.15)
    mage.current_mana = 5000
    
    target = ModularCharacter("Boss")
    target.stats.set_stat(StatType.HEALTH, 15000)
    target.current_health = target.get_max_health()
    
    spells = create_test_spells()
    
    print("Optimal rotation strategy:")
    print("1. Use high-damage cooldowns when available")
    print("2. Fill with instant spells during GCD")
    print("3. Use cast-time spells when nothing else available")
    print()
    
    # Priority system
    priority_order = ["Meteor", "Fireball", "Arcane Orb", "Frostbolt"]
    
    total_damage = 0
    cast_count = 0
    simulation_time = 20  # 20 second simulation
    start_time = time.time()
    
    print("Rotation simulation (20 seconds):")
    
    while time.time() - start_time < simulation_time and target.current_health > 0:
        cast_this_gcd = None
        
        # Find highest priority spell we can cast
        for spell_name in priority_order:
            spell = spells[spell_name]
            can_cast, reason = spell.can_cast(mage, target)
            
            if can_cast:
                cast_this_gcd = spell
                break
        
        if cast_this_gcd:
            result = cast_this_gcd.cast(mage, target)
            total_damage += result.damage
            cast_count += 1
            
            crit_indicator = " (CRIT!)" if result.was_critical else ""
            print(f"  {cast_this_gcd.name}: {result.damage} damage{crit_indicator}")
            
            # Simulate cast time
            if cast_this_gcd.cast_time > 0:
                time.sleep(min(cast_this_gcd.cast_time, 0.5))
        else:
            # No spells available, wait for GCD
            time.sleep(0.1)
    
    elapsed_time = time.time() - start_time
    dps = total_damage / elapsed_time
    
    print()
    print(f"Rotation Results:")
    print(f"  Total damage: {total_damage}")
    print(f"  Time elapsed: {elapsed_time:.1f}s")
    print(f"  Casts: {cast_count}")
    print(f"  DPS: {dps:.1f}")
    print(f"  Target health: {target.current_health}/{target.get_max_health()}")


def main():
    """Run all cooldown and GCD demonstrations."""
    print("🕐 WoW Simulator - Cooldown & GCD Mechanics Demo")
    print("=" * 60)
    print("This demo shows how cooldowns and Global Cooldown work")
    print("in your spell creation system.")
    print("=" * 60)
    
    demo_gcd_mechanics()
    print("\n" + "="*60 + "\n")
    
    demo_individual_cooldowns()
    print("\n" + "="*60 + "\n")
    
    demo_cooldown_vs_gcd()
    print("\n" + "="*60 + "\n")
    
    demo_optimal_rotation()
    
    print("\n" + "=" * 60)
    print("✅ Cooldown & GCD Demo Complete!")
    print("\nKey takeaways:")
    print("• GCD (1.5s) prevents rapid-fire casting")
    print("• Individual cooldowns balance powerful abilities")
    print("• Optimal rotations use priority systems")
    print("• Instant spells fill GCD gaps efficiently")
    print("• Cast-time spells can overlap with cooldowns")
    print("\nYour spell creation system handles all these mechanics automatically!")


if __name__ == "__main__":
    main()
