"""
Debug script to test imports step by step.
"""

print("Testing imports...")

try:
    print("1. Testing basic imports...")
    from typing import Dict, List
    print("   ✓ typing imports work")
    
    print("2. Testing enum import...")
    from enum import Enum
    print("   ✓ enum import works")
    
    print("3. Testing interfaces import...")
    from src.interfaces.base import StatType
    print("   ✓ StatType import works")
    
    print("4. Testing full interfaces import...")
    from src.interfaces import StatType, IStatContainer, IStatModifier
    print("   ✓ Full interfaces import works")
    
    print("5. Testing base_stats import...")
    from src.stats.base_stats import BaseStatContainer
    print("   ✓ BaseStatContainer import works")
    
    print("6. Testing modifiers import...")
    from src.stats.modifiers import AdditiveModifier
    print("   ✓ AdditiveModifier import works")
    
    print("7. Testing stat_calculator import...")
    from src.stats.stat_calculator import StatCalculator
    print("   ✓ StatCalculator import works")
    
    print("8. Testing stat_manager import...")
    from src.stats.stat_manager import StatManager
    print("   ✓ StatManager import works")
    
    print("9. Testing stats package import...")
    from src.stats import StatManager as SM
    print("   ✓ Stats package import works")
    
    print("\nAll imports successful! Testing basic functionality...")
    
    # Test basic functionality
    stats = SM()
    stats.set_stat(StatType.HEALTH, 1000)
    health = stats.get_stat(StatType.HEALTH)
    print(f"   ✓ Basic stat operations work: Health = {health}")
    
    print("\n✅ All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
