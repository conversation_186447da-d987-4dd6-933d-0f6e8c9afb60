<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Spell Builder</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            padding: 2rem;
            background: var(--bg-color);
            color: var(--text-primary);
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .debug-section {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .debug-result {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .debug-result.success {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .debug-result.error {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
        }
        
        .debug-result.info {
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Spell Builder Debug Tool</h1>
        <p>This tool will help identify what's not working in the spell builder.</p>
        
        <div class="debug-section">
            <h2>Step 1: Test Advanced Mode Toggle</h2>
            <button class="btn btn-primary" onclick="testAdvancedMode()">
                <i class="fas fa-cog"></i> Test Advanced Mode
            </button>
            <div id="advanced-mode-results"></div>
        </div>
        
        <div class="debug-section">
            <h2>Step 2: Test Spell Type Selection</h2>
            <button class="btn btn-primary" onclick="testSpellTypeSelection()">
                <i class="fas fa-fire"></i> Test Spell Type
            </button>
            <div id="spell-type-results"></div>
        </div>
        
        <div class="debug-section">
            <h2>Step 3: Test Condition Selection</h2>
            <button class="btn btn-primary" onclick="testConditionSelection()">
                <i class="fas fa-check"></i> Test Conditions
            </button>
            <div id="condition-results"></div>
        </div>
        
        <div class="debug-section">
            <h2>Step 4: Check Required Elements</h2>
            <button class="btn btn-primary" onclick="checkElements()">
                <i class="fas fa-search"></i> Check Elements
            </button>
            <div id="element-results"></div>
        </div>
        
        <div class="debug-section">
            <h2>Step 5: Check JavaScript Functions</h2>
            <button class="btn btn-primary" onclick="checkFunctions()">
                <i class="fas fa-code"></i> Check Functions
            </button>
            <div id="function-results"></div>
        </div>
        
        <div class="debug-section">
            <h2>Console Errors</h2>
            <p>Check your browser console (F12) for any red error messages and copy them here:</p>
            <div id="console-instructions" class="debug-result info">
                1. Press F12 to open developer tools<br>
                2. Click the "Console" tab<br>
                3. Look for red error messages<br>
                4. Copy any errors you see
            </div>
        </div>
    </div>

    <!-- Required elements -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>
    <div id="notifications" class="notifications"></div>

    <script src="static/js/app.js"></script>
    <script>
        function addDebugResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `debug-result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
        }
        
        function testAdvancedMode() {
            const container = 'advanced-mode-results';
            document.getElementById(container).innerHTML = '';
            
            addDebugResult(container, '🔍 Testing Advanced Mode Toggle...', 'info');
            
            // Check if function exists
            if (typeof toggleAdvancedMode === 'function') {
                addDebugResult(container, '✅ toggleAdvancedMode function exists', 'success');
                
                // Check if required elements exist
                const advancedConfig = document.getElementById('advanced-spell-config');
                const toggleBtn = document.querySelector('button[onclick="toggleAdvancedMode()"]');
                
                if (advancedConfig) {
                    addDebugResult(container, '✅ advanced-spell-config element found', 'success');
                } else {
                    addDebugResult(container, '❌ advanced-spell-config element missing', 'error');
                }
                
                if (toggleBtn) {
                    addDebugResult(container, '✅ toggle button found', 'success');
                } else {
                    addDebugResult(container, '❌ toggle button missing', 'error');
                }
                
                // Test the function
                try {
                    const initialState = advancedModeActive;
                    addDebugResult(container, `📊 Initial advancedModeActive: ${initialState}`, 'info');
                    
                    toggleAdvancedMode();
                    addDebugResult(container, `📊 After toggle: ${advancedModeActive}`, 'info');
                    
                    if (advancedConfig) {
                        const display = window.getComputedStyle(advancedConfig).display;
                        addDebugResult(container, `📊 advanced-spell-config display: ${display}`, 'info');
                    }
                    
                    addDebugResult(container, '✅ toggleAdvancedMode executed successfully', 'success');
                } catch (error) {
                    addDebugResult(container, `❌ Error calling toggleAdvancedMode: ${error.message}`, 'error');
                }
            } else {
                addDebugResult(container, '❌ toggleAdvancedMode function not found', 'error');
            }
        }
        
        function testSpellTypeSelection() {
            const container = 'spell-type-results';
            document.getElementById(container).innerHTML = '';
            
            addDebugResult(container, '🔍 Testing Spell Type Selection...', 'info');
            
            // Check if function exists
            if (typeof selectSpellType === 'function') {
                addDebugResult(container, '✅ selectSpellType function exists', 'success');
                
                try {
                    selectSpellType('damage');
                    addDebugResult(container, '✅ selectSpellType executed successfully', 'success');
                } catch (error) {
                    addDebugResult(container, `❌ Error calling selectSpellType: ${error.message}`, 'error');
                }
            } else {
                addDebugResult(container, '❌ selectSpellType function not found', 'error');
            }
        }
        
        function testConditionSelection() {
            const container = 'condition-results';
            document.getElementById(container).innerHTML = '';
            
            addDebugResult(container, '🔍 Testing Condition Selection...', 'info');
            
            // Check if function exists
            if (typeof selectSimpleCondition === 'function') {
                addDebugResult(container, '✅ selectSimpleCondition function exists', 'success');
                
                try {
                    selectSimpleCondition('normal');
                    addDebugResult(container, '✅ selectSimpleCondition executed successfully', 'success');
                } catch (error) {
                    addDebugResult(container, `❌ Error calling selectSimpleCondition: ${error.message}`, 'error');
                }
            } else {
                addDebugResult(container, '❌ selectSimpleCondition function not found', 'error');
            }
        }
        
        function checkElements() {
            const container = 'element-results';
            document.getElementById(container).innerHTML = '';
            
            addDebugResult(container, '🔍 Checking Required Elements...', 'info');
            
            const requiredElements = [
                'advanced-spell-config',
                'spell-type-details',
                'spell-effect-options',
                'simple-condition-config',
                'config-title',
                'config-content'
            ];
            
            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    addDebugResult(container, `✅ Element #${elementId} found`, 'success');
                } else {
                    addDebugResult(container, `❌ Element #${elementId} missing`, 'error');
                }
            });
        }
        
        function checkFunctions() {
            const container = 'function-results';
            document.getElementById(container).innerHTML = '';
            
            addDebugResult(container, '🔍 Checking JavaScript Functions...', 'info');
            
            const requiredFunctions = [
                'toggleAdvancedMode',
                'selectSpellType',
                'selectSimpleCondition',
                'showSpellEffectOptions',
                'showSimpleConditionConfig',
                'getEffectOptionsForType',
                'getSimpleConditionConfig'
            ];
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addDebugResult(container, `✅ Function ${funcName} exists`, 'success');
                } else {
                    addDebugResult(container, `❌ Function ${funcName} missing`, 'error');
                }
            });
        }
        
        // Auto-run basic checks on load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addDebugResult('function-results', '🚀 Auto-running basic checks...', 'info');
                checkElements();
                checkFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
