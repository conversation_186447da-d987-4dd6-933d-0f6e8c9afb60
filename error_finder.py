"""
Error finder - systematically test imports to find issues.
"""

def test_basic_imports():
    """Test basic Python imports."""
    try:
        import sys
        import os
        from typing import Dict, List, Any
        from dataclasses import dataclass
        from enum import Enum
        print("✓ Basic imports work")
        return True
    except Exception as e:
        print(f"✗ Basic imports failed: {e}")
        return False

def test_stats_system():
    """Test stats system imports."""
    try:
        from src.stats.standalone_stats import StatType
        print("✓ StatType import works")
        
        from src.character import ModularCharacter
        print("✓ ModularCharacter import works")
        
        # Test creating a character
        char = ModularCharacter("Test")
        print(f"✓ Character creation works: {char.name}")
        return True
    except Exception as e:
        print(f"✗ Stats system failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_spell_system():
    """Test spell system imports."""
    try:
        from src.spells.spell_schema import SpellConfig
        print("✓ SpellConfig import works")
        
        from src.spells.spell_validator import SpellValidator
        print("✓ SpellValidator import works")
        
        from src.spells.spell_builder import SpellBuilder
        print("✓ SpellBuilder import works")
        
        # Test creating a spell builder
        builder = SpellBuilder()
        print("✓ SpellBuilder creation works")
        return True
    except Exception as e:
        print(f"✗ Spell system failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_effects():
    """Test advanced effects system."""
    try:
        from src.spells.advanced_effects import AdvancedEffectProcessor
        print("✓ AdvancedEffectProcessor import works")
        
        processor = AdvancedEffectProcessor()
        print("✓ AdvancedEffectProcessor creation works")
        return True
    except Exception as e:
        print(f"✗ Advanced effects failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimization_system():
    """Test optimization system."""
    try:
        from src.optimization.rotation_optimizer import RotationOptimizer
        print("✓ RotationOptimizer import works")
        
        from src.optimization.timeline_visualizer import TimelineVisualizer
        print("✓ TimelineVisualizer import works")
        
        optimizer = RotationOptimizer()
        visualizer = TimelineVisualizer()
        print("✓ Optimization system creation works")
        return True
    except Exception as e:
        print(f"✗ Optimization system failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_spell_creation():
    """Test creating a simple spell."""
    try:
        from src.spells.spell_builder import SpellBuilder
        from src.character import ModularCharacter
        
        builder = SpellBuilder()
        char = ModularCharacter("Test Mage")
        
        # Simple spell data
        spell_data = {
            "name": "Test Fireball",
            "description": "A test fireball",
            "school": "fire",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 200,
            "target_type": "single_enemy",
            "base_damage": 400,
            "spell_power_coefficient": 1.0,
            "can_crit": True
        }
        
        spell = builder.build_spell_from_dict(spell_data)
        print(f"✓ Spell creation works: {spell.name}")
        
        # Test damage calculation
        damage = spell.calculate_damage(char)
        print(f"✓ Damage calculation works: {damage}")
        return True
    except Exception as e:
        print(f"✗ Spell creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all error tests."""
    print("🔍 Error Finder - Systematic Testing")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Stats System", test_stats_system),
        ("Spell System", test_spell_system),
        ("Advanced Effects", test_advanced_effects),
        ("Optimization System", test_optimization_system),
        ("Spell Creation", test_spell_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- Testing {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All systems working correctly!")
    else:
        print("❌ Some systems have errors - see details above")
    
    return passed == total

if __name__ == "__main__":
    main()
