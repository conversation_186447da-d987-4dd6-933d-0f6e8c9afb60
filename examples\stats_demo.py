"""
Demonstration of the modular stats system.
Shows how easy it is to create, modify, and extend the stats system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.stats.standalone_stats import SimpleStatManager as StatManager, StatType, AdditiveModifier, PercentageModifier, StackingModifier


def demo_basic_stats():
    """Demonstrate basic stat operations."""
    print("=== Basic Stats Demo ===")
    
    # Create a stat manager
    stats = StatManager()
    
    # Set some base stats
    stats.set_stat(StatType.HEALTH, 1000)
    stats.set_stat(StatType.MANA, 500)
    stats.set_stat(StatType.SPELL_POWER, 100)
    stats.set_stat(StatType.CRIT_CHANCE, 0.05)
    
    print("Base stats:")
    for stat_type in [StatType.HEALTH, StatType.MANA, StatType.SPELL_POWER, StatType.CRIT_CHANCE]:
        print(f"  {stat_type.value}: {stats.get_stat(stat_type)}")
    
    print("\nEffective stats (same as base since no modifiers):")
    for stat_type in [StatType.HEALTH, StatType.MANA, StatType.SPELL_POWER, StatType.CRIT_CHANCE]:
        print(f"  {stat_type.value}: {stats.get_effective_stat(stat_type)}")


def demo_flat_bonuses():
    """Demonstrate flat stat bonuses."""
    print("\n=== Flat Bonuses Demo ===")
    
    stats = StatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    stats.set_stat(StatType.CRIT_CHANCE, 0.05)
    
    print(f"Base spell power: {stats.get_stat(StatType.SPELL_POWER)}")
    print(f"Base crit chance: {stats.get_stat(StatType.CRIT_CHANCE)}")
    
    # Add a flat bonus from equipment
    stats.add_flat_bonus("weapon_bonus", {
        StatType.SPELL_POWER: 50,
        StatType.CRIT_CHANCE: 0.02
    })
    
    print(f"\nAfter weapon bonus:")
    print(f"  Spell power: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    print(f"  Crit chance: {stats.get_effective_stat(StatType.CRIT_CHANCE)}")
    
    # Add another bonus from enchantment
    stats.add_flat_bonus("enchant_bonus", {
        StatType.SPELL_POWER: 25
    })
    
    print(f"\nAfter enchantment bonus:")
    print(f"  Spell power: {stats.get_effective_stat(StatType.SPELL_POWER)}")


def demo_percentage_bonuses():
    """Demonstrate percentage-based bonuses."""
    print("\n=== Percentage Bonuses Demo ===")
    
    stats = StatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    print(f"Base spell power: {stats.get_stat(StatType.SPELL_POWER)}")
    
    # Add a 20% bonus
    stats.add_percentage_bonus("talent_bonus", {
        StatType.SPELL_POWER: 20.0  # 20%
    })
    
    print(f"After 20% talent bonus: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    
    # Add another 15% bonus
    stats.add_percentage_bonus("buff_bonus", {
        StatType.SPELL_POWER: 15.0  # 15%
    })
    
    print(f"After additional 15% buff: {stats.get_effective_stat(StatType.SPELL_POWER)}")


def demo_stacking_modifiers():
    """Demonstrate stacking modifiers."""
    print("\n=== Stacking Modifiers Demo ===")
    
    stats = StatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    print(f"Base spell power: {stats.get_stat(StatType.SPELL_POWER)}")
    
    # Add a stacking buff (like Arcane Intellect stacks)
    stats.add_stacking_modifier("arcane_power", {
        StatType.SPELL_POWER: 10  # 10 spell power per stack
    }, max_stacks=5)
    
    print(f"\nAdding stacks of Arcane Power:")
    for i in range(1, 6):
        stats.add_stack("arcane_power")
        modifier = stats.get_modifier("arcane_power")
        stacks = modifier.get_stacks() if hasattr(modifier, 'get_stacks') else 0
        print(f"  Stack {stacks}: {stats.get_effective_stat(StatType.SPELL_POWER)} spell power")
    
    # Try to add more stacks (should be capped)
    stats.add_stack("arcane_power")
    modifier = stats.get_modifier("arcane_power")
    stacks = modifier.get_stacks() if hasattr(modifier, 'get_stacks') else 0
    print(f"  Trying to add 6th stack: {stacks} stacks, {stats.get_effective_stat(StatType.SPELL_POWER)} spell power")


def demo_conditional_modifiers():
    """Demonstrate conditional modifiers."""
    print("\n=== Conditional Modifiers Demo ===")
    
    stats = StatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    stats.set_stat(StatType.MANA, 500)
    
    # Create a condition: bonus only applies when mana > 250
    def high_mana_condition():
        return stats.get_effective_stat(StatType.MANA) > 250
    
    # Create base modifier
    base_modifier = AdditiveModifier("mana_shield_base", {
        StatType.SPELL_POWER: 30
    })
    
    # Add conditional modifier
    stats.add_conditional_modifier("mana_shield", base_modifier, high_mana_condition)
    
    print(f"Spell power with high mana ({stats.get_effective_stat(StatType.MANA)}): {stats.get_effective_stat(StatType.SPELL_POWER)}")
    
    # Reduce mana below threshold
    stats.set_stat(StatType.MANA, 200)
    print(f"Spell power with low mana ({stats.get_effective_stat(StatType.MANA)}): {stats.get_effective_stat(StatType.SPELL_POWER)}")


def demo_stat_breakdown():
    """Demonstrate stat breakdown analysis."""
    print("\n=== Stat Breakdown Demo ===")
    
    stats = StatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    # Add various modifiers
    stats.add_flat_bonus("equipment", {StatType.SPELL_POWER: 50})
    stats.add_percentage_bonus("talents", {StatType.SPELL_POWER: 20.0})
    stats.add_stacking_modifier("buff", {StatType.SPELL_POWER: 15}, max_stacks=3)
    stats.add_stack("buff")
    stats.add_stack("buff")
    
    print(f"Final spell power: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    
    # Get detailed breakdown
    breakdown = stats.get_stat_breakdown(StatType.SPELL_POWER)
    print("\nDetailed breakdown:")
    for key, value in breakdown.items():
        print(f"  {key}: {value}")
    
    # Show which modifiers affect spell power
    affecting_modifiers = stats.get_modifiers_affecting_stat(StatType.SPELL_POWER)
    print(f"\nModifiers affecting spell power: {list(affecting_modifiers.keys())}")


def demo_stat_change_events():
    """Demonstrate stat change event system."""
    print("\n=== Stat Change Events Demo ===")
    
    stats = StatManager()
    
    # Add a listener for stat changes
    def on_stat_change(stat_type, old_value, new_value):
        print(f"  STAT CHANGE: {stat_type.value} changed from {old_value} to {new_value}")
    
    stats.add_stat_change_listener(on_stat_change)
    
    print("Setting base stats (should trigger events):")
    stats.set_stat(StatType.HEALTH, 1000)
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    print("\nAdding modifiers (should trigger events):")
    stats.add_flat_bonus("equipment", {
        StatType.HEALTH: 200,
        StatType.SPELL_POWER: 50
    })


def main():
    """Run all demonstrations."""
    print("WoW Simulator - Modular Stats System Demo")
    print("=" * 50)
    
    demo_basic_stats()
    demo_flat_bonuses()
    demo_percentage_bonuses()
    demo_stacking_modifiers()
    demo_conditional_modifiers()
    demo_stat_breakdown()
    demo_stat_change_events()
    
    print("\n" + "=" * 50)
    print("Demo complete! The modular stats system provides:")
    print("✓ Easy addition of new stat types")
    print("✓ Flexible modifier system")
    print("✓ Complex calculations with custom rules")
    print("✓ Stacking and conditional modifiers")
    print("✓ Detailed analysis and debugging")
    print("✓ Event system for reactive programming")


if __name__ == "__main__":
    main()
