<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WoW Simulator - Spell Rotation Helper</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-magic"></i>
                    <h1>WoW Simulator</h1>
                    <span class="subtitle">Spell Rotation Helper</span>
                </div>
                <nav class="nav">
                    <button class="nav-btn active" data-section="character">
                        <i class="fas fa-user"></i>
                        Character
                    </button>
                    <button class="nav-btn" data-section="spells">
                        <i class="fas fa-fire"></i>
                        Spells
                    </button>
                    <button class="nav-btn" data-section="rotation">
                        <i class="fas fa-sync-alt"></i>
                        Rotation
                    </button>
                    <button class="nav-btn" data-section="testing">
                        <i class="fas fa-flask"></i>
                        Testing
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Character Section -->
            <section id="character-section" class="section active">
                <div class="section-header">
                    <h2><i class="fas fa-user"></i> Character Setup</h2>
                    <p>Configure your character's stats and abilities</p>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-id-card"></i> Basic Info</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="char-name">Character Name</label>
                                <input type="text" id="char-name" placeholder="Enter character name" value="Arcane Mage">
                            </div>
                            <div class="form-group">
                                <label for="char-class">Class</label>
                                <select id="char-class">
                                    <option value="mage">Mage</option>
                                    <option value="warlock">Warlock</option>
                                    <option value="priest">Priest</option>
                                    <option value="shaman">Shaman</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Core Stats</h3>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-input">
                                    <label for="health">Health</label>
                                    <input type="number" id="health" value="1000" min="100" max="10000">
                                </div>
                                <div class="stat-input">
                                    <label for="mana">Mana</label>
                                    <input type="number" id="mana" value="1000" min="100" max="10000">
                                </div>
                                <div class="stat-input">
                                    <label for="spell-power">Spell Power</label>
                                    <input type="number" id="spell-power" value="100" min="0" max="2000">
                                </div>
                                <div class="stat-input">
                                    <label for="crit-chance">Crit Chance (%)</label>
                                    <input type="number" id="crit-chance" value="5" min="0" max="100" step="0.1">
                                </div>
                                <div class="stat-input">
                                    <label for="haste">Haste (%)</label>
                                    <input type="number" id="haste" value="0" min="0" max="100" step="0.1">
                                </div>
                                <div class="stat-input">
                                    <label for="hit-chance">Hit Chance (%)</label>
                                    <input type="number" id="hit-chance" value="83" min="0" max="100" step="0.1">
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="updateCharacter()">
                                <i class="fas fa-save"></i> Update Character
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> Character Summary</h3>
                    </div>
                    <div class="card-body">
                        <div id="character-summary" class="character-summary">
                            <div class="summary-item">
                                <span class="label">Name:</span>
                                <span class="value" id="summary-name">Arcane Mage</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Health:</span>
                                <span class="value" id="summary-health">1000</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Mana:</span>
                                <span class="value" id="summary-mana">1000</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Spell Power:</span>
                                <span class="value" id="summary-spell-power">100</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Crit Chance:</span>
                                <span class="value" id="summary-crit">5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Spells Section -->
            <section id="spells-section" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-fire"></i> Spell Builder</h2>
                    <p>Create and customize spells for your character</p>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-scroll"></i> Spell Templates</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="spell-template">Choose Template</label>
                                <select id="spell-template" onchange="loadSpellTemplate()">
                                    <option value="">Select a template...</option>
                                    <option value="direct_damage">Direct Damage</option>
                                    <option value="damage_over_time">Damage Over Time</option>
                                    <option value="direct_heal">Direct Heal</option>
                                    <option value="buff_spell">Buff Spell</option>
                                    <option value="aoe_damage">AoE Damage</option>
                                    <option value="instant_nuke">Instant Nuke</option>
                                </select>
                            </div>
                            <button class="btn btn-secondary" onclick="loadAdvancedTemplates()">
                                <i class="fas fa-star"></i> Load Advanced Templates
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-edit"></i> Spell Configuration</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="spell-name">Spell Name</label>
                                <input type="text" id="spell-name" placeholder="Enter spell name">
                            </div>
                            <div class="form-group">
                                <label for="spell-description">Description</label>
                                <textarea id="spell-description" placeholder="Describe your spell"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="spell-school">School</label>
                                    <select id="spell-school">
                                        <option value="fire">Fire</option>
                                        <option value="frost">Frost</option>
                                        <option value="arcane">Arcane</option>
                                        <option value="shadow">Shadow</option>
                                        <option value="nature">Nature</option>
                                        <option value="holy">Holy</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="target-type">Target Type</label>
                                    <select id="target-type">
                                        <option value="single_enemy">Single Enemy</option>
                                        <option value="aoe_enemy">AoE Enemy</option>
                                        <option value="self">Self</option>
                                        <option value="single_ally">Single Ally</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="cast-time">Cast Time (s)</label>
                                    <input type="number" id="cast-time" value="2.5" min="0" max="10" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="cooldown">Cooldown (s)</label>
                                    <input type="number" id="cooldown" value="0" min="0" max="600" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="mana-cost">Mana Cost</label>
                                    <input type="number" id="mana-cost" value="200" min="0" max="2000">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="base-damage-min">Min Damage</label>
                                    <input type="number" id="base-damage-min" value="200" min="0" max="5000">
                                </div>
                                <div class="form-group">
                                    <label for="base-damage-max">Max Damage</label>
                                    <input type="number" id="base-damage-max" value="300" min="0" max="5000">
                                </div>
                                <div class="form-group">
                                    <label for="spell-power-coeff">SP Coefficient</label>
                                    <input type="number" id="spell-power-coeff" value="1.0" min="0" max="3" step="0.1">
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="createSpell()">
                                    <i class="fas fa-plus"></i> Create Spell
                                </button>
                                <button class="btn btn-secondary" onclick="validateSpell()">
                                    <i class="fas fa-check"></i> Validate
                                </button>
                                <button class="btn btn-secondary" onclick="toggleAdvancedMode()">
                                    <i class="fas fa-cog"></i> <span id="advanced-mode-text">Advanced Mode</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User-Friendly Advanced Spell Builder -->
                <div id="advanced-spell-config" class="card" style="display: none;">
                    <div class="card-header">
                        <h3><i class="fas fa-magic"></i> Advanced Spell Builder</h3>
                        <div class="builder-progress">
                            <div class="progress-step active" data-step="1">
                                <span class="step-number">1</span>
                                <span class="step-label">Effects</span>
                            </div>
                            <div class="progress-step" data-step="2">
                                <span class="step-number">2</span>
                                <span class="step-label">Conditions</span>
                            </div>
                            <div class="progress-step" data-step="3">
                                <span class="step-number">3</span>
                                <span class="step-label">Properties</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Step 1: Effects -->
                        <div id="builder-step-1" class="builder-step active">
                            <h4><i class="fas fa-sparkles"></i> What should this spell do?</h4>
                            <p class="step-description">Choose the main effects your spell will have. You can add multiple effects!</p>

                            <div class="effect-categories">
                                <div class="effect-category">
                                    <h5><i class="fas fa-sword"></i> Damage Effects</h5>
                                    <div class="effect-buttons">
                                        <button class="effect-btn" onclick="addQuickEffect('instant_damage')">
                                            <i class="fas fa-bolt"></i>
                                            <span>Instant Damage</span>
                                            <small>Deal immediate damage</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('damage_over_time')">
                                            <i class="fas fa-fire"></i>
                                            <span>Damage Over Time</span>
                                            <small>Burn/poison target</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('chain_damage')">
                                            <i class="fas fa-link"></i>
                                            <span>Chain Damage</span>
                                            <small>Jump between enemies</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('aoe_damage')">
                                            <i class="fas fa-explosion"></i>
                                            <span>Area Damage</span>
                                            <small>Hit multiple targets</small>
                                        </button>
                                    </div>
                                </div>

                                <div class="effect-category">
                                    <h5><i class="fas fa-magic"></i> Special Effects</h5>
                                    <div class="effect-buttons">
                                        <button class="effect-btn" onclick="addQuickEffect('proc_effect')">
                                            <i class="fas fa-dice"></i>
                                            <span>Proc Effect</span>
                                            <small>Trigger other spells</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('stacking_effect')">
                                            <i class="fas fa-layer-group"></i>
                                            <span>Stacking Effect</span>
                                            <small>Build up power</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('buff_effect')">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>Buff</span>
                                            <small>Enhance abilities</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('debuff_effect')">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>Debuff</span>
                                            <small>Weaken enemies</small>
                                        </button>
                                    </div>
                                </div>

                                <div class="effect-category">
                                    <h5><i class="fas fa-shield"></i> Control Effects</h5>
                                    <div class="effect-buttons">
                                        <button class="effect-btn" onclick="addQuickEffect('stun_effect')">
                                            <i class="fas fa-hand-paper"></i>
                                            <span>Stun</span>
                                            <small>Disable target</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('slow_effect')">
                                            <i class="fas fa-snail"></i>
                                            <span>Slow</span>
                                            <small>Reduce movement</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('transform_effect')">
                                            <i class="fas fa-paw"></i>
                                            <span>Transform</span>
                                            <small>Change target form</small>
                                        </button>
                                        <button class="effect-btn" onclick="addQuickEffect('silence_effect')">
                                            <i class="fas fa-volume-mute"></i>
                                            <span>Silence</span>
                                            <small>Prevent spellcasting</small>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="selected-effects" class="selected-effects">
                                <h5><i class="fas fa-list"></i> Selected Effects</h5>
                                <div id="effects-list" class="effects-list">
                                    <p class="empty-state">No effects selected. Choose effects above to add them.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Conditions -->
                        <div id="builder-step-2" class="builder-step">
                            <h4><i class="fas fa-question-circle"></i> When should these effects trigger?</h4>
                            <p class="step-description">Set conditions that determine when your spell effects activate. Use logical operators to create complex rules!</p>

                            <div class="conditions-builder">
                                <div class="condition-group" id="main-condition-group">
                                    <div class="condition-header">
                                        <span class="condition-label">Main Conditions</span>
                                        <select class="logic-operator" onchange="updateConditionLogic()">
                                            <option value="AND">ALL conditions must be true (AND)</option>
                                            <option value="OR">ANY condition can be true (OR)</option>
                                        </select>
                                    </div>

                                    <div class="conditions-list" id="conditions-list">
                                        <div class="condition-item">
                                            <div class="condition-row">
                                                <select class="condition-type" onchange="updateConditionOptions(this)">
                                                    <option value="">Always trigger (no conditions)</option>
                                                    <optgroup label="Spell Conditions">
                                                        <option value="spell_critical">Spell critically hits</option>
                                                        <option value="spell_hit">Spell hits target</option>
                                                        <option value="spell_miss">Spell misses target</option>
                                                        <option value="spell_school">Spell is specific school</option>
                                                        <option value="spell_type">Spell is specific type</option>
                                                        <option value="spell_damage_type">Damage type (physical/magical)</option>
                                                        <option value="spell_cast_time">Spell cast time</option>
                                                        <option value="spell_mana_cost">Spell mana cost</option>
                                                        <option value="spell_on_cooldown">Spell is on cooldown</option>
                                                        <option value="spell_recently_cast">Spell cast recently</option>
                                                        <option value="spell_power_above">Spell power above threshold</option>
                                                        <option value="spell_interrupted">Spell was interrupted</option>
                                                    </optgroup>
                                                    <optgroup label="Target Conditions">
                                                        <option value="target_health_below">Target health below %</option>
                                                        <option value="target_health_above">Target health above %</option>
                                                        <option value="target_type">Target is specific type</option>
                                                        <option value="target_has_buff">Target has buff</option>
                                                        <option value="target_has_debuff">Target has debuff</option>
                                                        <option value="target_level">Target level</option>
                                                        <option value="target_armor">Target armor value</option>
                                                        <option value="target_resistance">Target resistance</option>
                                                        <option value="target_moving">Target is moving</option>
                                                        <option value="target_casting">Target is casting</option>
                                                        <option value="target_stunned">Target is stunned</option>
                                                        <option value="target_silenced">Target is silenced</option>
                                                        <option value="target_feared">Target is feared</option>
                                                        <option value="target_rooted">Target is rooted</option>
                                                        <option value="target_polymorphed">Target is polymorphed</option>
                                                        <option value="target_distance">Distance to target</option>
                                                        <option value="target_behind">Target is behind caster</option>
                                                        <option value="target_facing">Target is facing caster</option>
                                                    </optgroup>
                                                    <optgroup label="Caster Conditions">
                                                        <option value="caster_health_below">Caster health below %</option>
                                                        <option value="caster_health_above">Caster health above %</option>
                                                        <option value="caster_mana_below">Caster mana below %</option>
                                                        <option value="caster_mana_above">Caster mana above %</option>
                                                        <option value="caster_has_buff">Caster has buff</option>
                                                        <option value="caster_has_debuff">Caster has debuff</option>
                                                        <option value="buff_present">Specific buff is active</option>
                                                        <option value="buff_stacks">Buff has X stacks</option>
                                                        <option value="caster_level">Caster level</option>
                                                        <option value="caster_class">Caster class</option>
                                                        <option value="caster_race">Caster race</option>
                                                        <option value="caster_moving">Caster is moving</option>
                                                        <option value="caster_casting">Caster is casting</option>
                                                        <option value="caster_in_combat">Caster is in combat</option>
                                                        <option value="caster_stealthed">Caster is stealthed</option>
                                                        <option value="caster_mounted">Caster is mounted</option>
                                                        <option value="caster_weapon_equipped">Weapon equipped</option>
                                                        <option value="caster_stance">Caster stance/form</option>
                                                    </optgroup>
                                                    <optgroup label="Proc Conditions">
                                                        <option value="proc_triggered">When specific proc triggers</option>
                                                        <option value="proc_chance">Random proc chance</option>
                                                        <option value="proc_cooldown_ready">Proc cooldown is ready</option>
                                                        <option value="consecutive_procs">Consecutive proc triggers</option>
                                                        <option value="proc_stacks">Proc effect has stacks</option>
                                                        <option value="proc_rate">Proc trigger rate</option>
                                                    </optgroup>
                                                    <optgroup label="Combat Conditions">
                                                        <option value="combat_time">Combat duration</option>
                                                        <option value="enemies_nearby">Enemies within range</option>
                                                        <option value="allies_nearby">Allies within range</option>
                                                        <option value="effect_expires">When effect expires</option>
                                                        <option value="spell_combo">Spell cast in sequence</option>
                                                        <option value="resource_threshold">Mana/Health threshold</option>
                                                        <option value="threat_level">Threat/Aggro level</option>
                                                        <option value="group_size">Party/Raid size</option>
                                                        <option value="combat_rating">Combat rating (hit/crit/haste)</option>
                                                        <option value="weapon_skill">Weapon skill level</option>
                                                        <option value="combo_points">Combo points (rogue/druid)</option>
                                                        <option value="rage_energy">Rage/Energy amount</option>
                                                        <option value="holy_power">Holy Power (paladin)</option>
                                                        <option value="soul_shards">Soul Shards (warlock)</option>
                                                    </optgroup>
                                                    <optgroup label="Environmental Conditions">
                                                        <option value="time_of_day">Time of day</option>
                                                        <option value="zone_type">Zone type (indoor/outdoor)</option>
                                                        <option value="weather">Weather conditions</option>
                                                        <option value="pvp_zone">PvP zone active</option>
                                                        <option value="sanctuary_zone">Sanctuary zone</option>
                                                        <option value="instance_type">Instance type (dungeon/raid)</option>
                                                        <option value="boss_encounter">Boss encounter active</option>
                                                        <option value="elite_target">Target is elite</option>
                                                        <option value="player_target">Target is player</option>
                                                        <option value="npc_target">Target is NPC</option>
                                                    </optgroup>
                                                    <optgroup label="Advanced Conditions">
                                                        <option value="damage_taken_recently">Damage taken recently</option>
                                                        <option value="healing_received">Healing received recently</option>
                                                        <option value="spell_reflect_active">Spell reflect active</option>
                                                        <option value="magic_immunity">Magic immunity active</option>
                                                        <option value="physical_immunity">Physical immunity active</option>
                                                        <option value="crowd_control_active">Crowd control active</option>
                                                        <option value="dispel_available">Dispel effect available</option>
                                                        <option value="interrupt_available">Interrupt available</option>
                                                        <option value="line_of_sight">Line of sight to target</option>
                                                        <option value="facing_target">Facing target</option>
                                                        <option value="behind_target">Behind target</option>
                                                        <option value="flanking_target">Flanking target</option>
                                                    </optgroup>
                                                </select>

                                                <select class="condition-operator" style="display: none;">
                                                    <option value="==">=</option>
                                                    <option value="!=">=</option>
                                                    <option value="<"><</option>
                                                    <option value="<=">≤</option>
                                                    <option value=">">></option>
                                                    <option value=">=">≥</option>
                                                </select>

                                                <input type="text" class="condition-value" placeholder="Value" style="display: none;">

                                                <button type="button" class="btn-icon btn-danger" onclick="removeCondition(this)" style="display: none;">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>

                                            <div class="condition-description" id="condition-description">
                                                <i class="fas fa-info-circle"></i>
                                                <span>This effect will always trigger when the spell is cast.</span>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="button" class="btn btn-secondary btn-sm" onclick="addCondition()">
                                        <i class="fas fa-plus"></i> Add Another Condition
                                    </button>
                                </div>

                                <div class="condition-preview">
                                    <h6><i class="fas fa-eye"></i> Logic Preview</h6>
                                    <div id="logic-preview" class="logic-preview">
                                        <span class="logic-text">Always trigger</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Properties -->
                        <div id="builder-step-3" class="builder-step">
                            <h4><i class="fas fa-cogs"></i> Fine-tune your effects</h4>
                            <p class="step-description">Adjust the specific values and properties for each effect you've added.</p>

                            <div id="effect-properties" class="effect-properties">
                                <p class="empty-state">Add effects in Step 1 to configure their properties here.</p>
                            </div>
                        </div>

                        <!-- Builder Navigation -->
                        <div class="builder-navigation">
                            <button type="button" class="btn btn-secondary" id="prev-step" onclick="previousStep()" style="display: none;">
                                <i class="fas fa-arrow-left"></i> Previous
                            </button>
                            <button type="button" class="btn btn-primary" id="next-step" onclick="nextStep()">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                            <button type="button" class="btn btn-success" id="finish-builder" onclick="finishBuilder()" style="display: none;">
                                <i class="fas fa-check"></i> Create Spell
                            </button>
                        </div>

                        <div class="form-group">
                            <label for="spell-conditions">Spell Conditions</label>
                            <div id="spell-conditions-container">
                                <div class="condition-item">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>Condition Type</label>
                                            <select class="condition-type">
                                                <option value="">Select condition...</option>
                                                <option value="target_health_below">Target Health Below</option>
                                                <option value="target_health_above">Target Health Above</option>
                                                <option value="caster_mana_below">Caster Mana Below</option>
                                                <option value="buff_present">Buff Present</option>
                                                <option value="debuff_present">Debuff Present</option>
                                                <option value="spell_critical">Spell Critical</option>
                                                <option value="combo_points">Combo Points</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Operator</label>
                                            <select class="condition-operator">
                                                <option value="==">=</option>
                                                <option value="!=">=</option>
                                                <option value="<"><</option>
                                                <option value="<="><=</option>
                                                <option value=">">></option>
                                                <option value=">=">>=</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Value</label>
                                            <input type="text" class="condition-value" placeholder="Condition value">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn-icon btn-danger" onclick="removeCondition(this)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addCondition()">
                                <i class="fas fa-plus"></i> Add Condition
                            </button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="spell-channeled">
                                    <input type="checkbox" id="spell-channeled"> Channeled Spell
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="channel-ticks">Channel Ticks</label>
                                <input type="number" id="channel-ticks" value="1" min="1" max="20">
                            </div>
                            <div class="form-group">
                                <label for="spell-interruptible">
                                    <input type="checkbox" id="spell-interruptible" checked> Interruptible
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Templates -->
                <div id="advanced-templates" class="card" style="display: none;">
                    <div class="card-header">
                        <h3><i class="fas fa-star"></i> Advanced Spell Templates</h3>
                    </div>
                    <div class="card-body">
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <a href="advanced_effects_demo.html" class="btn btn-special" style="font-size: 1.1rem; padding: 1rem 2rem;">
                                <i class="fas fa-magic"></i> 🎭 View Advanced Effects Demo
                            </a>
                        </div>

                        <div class="advanced-templates-grid">
                            <div class="template-card" onclick="loadAdvancedTemplate('living_bomb')">
                                <div class="template-icon fire">🔥</div>
                                <h4>Living Bomb</h4>
                                <p>DoT that explodes when it expires</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('chain_lightning')">
                                <div class="template-icon nature">⚡</div>
                                <h4>Chain Lightning</h4>
                                <p>Jumps between multiple targets</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('hot_streak_proc')">
                                <div class="template-icon fire">🔥</div>
                                <h4>Hot Streak (Proc)</h4>
                                <p>Passive proc for fire spell crits</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('pyroblast')">
                                <div class="template-icon fire">💥</div>
                                <h4>Pyroblast</h4>
                                <p>Devastating fire spell with DoT</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('arcane_blast')">
                                <div class="template-icon arcane">🔮</div>
                                <h4>Arcane Blast</h4>
                                <p>Stacking spell with increasing power</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('execute')">
                                <div class="template-icon physical">⚔️</div>
                                <h4>Execute</h4>
                                <p>More damage on low health targets</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('ignite')">
                                <div class="template-icon fire">🔥</div>
                                <h4>Ignite</h4>
                                <p>Critical strike mastery</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('clearcasting')">
                                <div class="template-icon arcane">✨</div>
                                <h4>Clearcasting</h4>
                                <p>Random mana-free spells</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('impact')">
                                <div class="template-icon fire">💥</div>
                                <h4>Impact</h4>
                                <p>Proc chain reactions</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('cheap_shot')">
                                <div class="template-icon physical">🗡️</div>
                                <h4>Cheap Shot</h4>
                                <p>Stealth attack with conditions</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('shadowmeld_ambush')">
                                <div class="template-icon shadow">🌙</div>
                                <h4>Shadowmeld Ambush</h4>
                                <p>Night Elf racial ability</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('polymorph')">
                                <div class="template-icon arcane">🐑</div>
                                <h4>Polymorph</h4>
                                <p>Transform target into harmless creature</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> Created Spells</h3>
                        <button class="btn-icon btn-danger" onclick="clearAllSpells()" title="Clear all spells" style="margin-left: auto;">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="spell-list" class="spell-list">
                            <p class="empty-state">No spells created yet. Use the form above to create your first spell!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Rotation Section -->
            <section id="rotation-section" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-sync-alt"></i> Rotation Optimizer</h2>
                    <p>Find the optimal spell rotation for maximum DPS</p>
                </div>

                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-cogs"></i> Optimization Settings</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="optimization-goal">Goal</label>
                                    <select id="optimization-goal">
                                        <option value="maximum_dps">Maximum DPS</option>
                                        <option value="mana_efficient">Mana Efficient</option>
                                        <option value="burst_damage">Burst Damage</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="fight-duration">Duration (seconds)</label>
                                    <input type="number" id="fight-duration" value="60" min="10" max="600">
                                </div>
                                <div class="form-group">
                                    <label for="target-count">Target Count</label>
                                    <input type="number" id="target-count" value="1" min="1" max="10">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="movement-time">Movement Time (%)</label>
                                <input type="range" id="movement-time" min="0" max="50" value="0" oninput="updateMovementDisplay()">
                                <span id="movement-display">0%</span>
                            </div>
                            <button class="btn btn-primary" onclick="optimizeRotation()">
                                <i class="fas fa-play"></i> Optimize Rotation
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-list-ol"></i> Available Spells</h3>
                        </div>
                        <div class="card-body">
                            <div id="available-spells" class="spell-selection">
                                <p class="empty-state">Create some spells first to optimize rotations!</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Optimization Results</h3>
                    </div>
                    <div class="card-body">
                        <div id="rotation-results" class="rotation-results">
                            <div class="results-summary">
                                <div class="metric">
                                    <span class="metric-label">Total DPS:</span>
                                    <span class="metric-value" id="total-dps">-</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Total Damage:</span>
                                    <span class="metric-value" id="total-damage">-</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Mana Used:</span>
                                    <span class="metric-value" id="mana-used">-</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Efficiency:</span>
                                    <span class="metric-value" id="efficiency">-</span>
                                </div>
                            </div>
                            <div id="rotation-timeline" class="rotation-timeline">
                                <p class="empty-state">Run optimization to see rotation timeline</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Testing Section -->
            <section id="testing-section" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-flask"></i> Spell Testing</h2>
                    <p>Test individual spells and see damage calculations</p>
                </div>

                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-crosshairs"></i> Test Setup</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="test-spell">Select Spell</label>
                                <select id="test-spell">
                                    <option value="">Choose a spell to test...</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="test-iterations">Test Iterations</label>
                                    <input type="number" id="test-iterations" value="100" min="1" max="1000">
                                </div>
                                <div class="form-group">
                                    <label for="target-armor">Target Armor</label>
                                    <input type="number" id="target-armor" value="0" min="0" max="10000">
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="testSpell()">
                                    <i class="fas fa-play"></i> Run Test
                                </button>
                                <button class="btn btn-secondary" onclick="testAllSpells()">
                                    <i class="fas fa-list"></i> Test All Spells
                                </button>
                                <button class="btn btn-special" onclick="simulateAdvancedEffects()">
                                    <i class="fas fa-magic"></i> Simulate Advanced Effects
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Test Results</h3>
                        </div>
                        <div class="card-body">
                            <div id="test-results" class="test-results">
                                <div class="results-grid">
                                    <div class="result-item">
                                        <span class="result-label">Average Damage:</span>
                                        <span class="result-value" id="avg-damage">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">Min Damage:</span>
                                        <span class="result-value" id="min-damage">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">Max Damage:</span>
                                        <span class="result-value" id="max-damage">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">Crit Rate:</span>
                                        <span class="result-value" id="crit-rate">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">DPS:</span>
                                        <span class="result-value" id="spell-dps">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">DPM:</span>
                                        <span class="result-value" id="spell-dpm">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-magic"></i> Advanced Effects Simulation</h3>
                    </div>
                    <div class="card-body">
                        <div id="advanced-effects-results" class="advanced-effects-results">
                            <div class="effects-summary">
                                <div class="effect-metric">
                                    <span class="effect-label">Total Damage:</span>
                                    <span class="effect-value" id="effects-total-damage">-</span>
                                </div>
                                <div class="effect-metric">
                                    <span class="effect-label">Average Damage:</span>
                                    <span class="effect-value" id="effects-avg-damage">-</span>
                                </div>
                                <div class="effect-metric">
                                    <span class="effect-label">Effects Triggered:</span>
                                    <span class="effect-value" id="effects-triggered">-</span>
                                </div>
                                <div class="effect-metric">
                                    <span class="effect-label">Most Active Effect:</span>
                                    <span class="effect-value" id="most-active-effect">-</span>
                                </div>
                            </div>
                            <div id="effects-timeline" class="effects-timeline">
                                <p class="empty-state">Run advanced effects simulation to see detailed results</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Test History</h3>
                    </div>
                    <div class="card-body">
                        <div id="test-history" class="test-history">
                            <p class="empty-state">No tests run yet. Use the form above to test spells!</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-magic fa-spin"></i>
            <p>Processing...</p>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notifications" class="notifications"></div>

    <!-- Scripts -->
    <script src="static/js/app.js"></script>
</body>
</html>
