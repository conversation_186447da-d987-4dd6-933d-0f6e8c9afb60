<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WoW Simulator - Spell Rotation Helper</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-magic"></i>
                    <h1>WoW Simulator</h1>
                    <span class="subtitle">Spell Rotation Helper</span>
                </div>
                <nav class="nav">
                    <button class="nav-btn active" data-section="character">
                        <i class="fas fa-user"></i>
                        Character
                    </button>
                    <button class="nav-btn" data-section="spells">
                        <i class="fas fa-fire"></i>
                        Spells
                    </button>
                    <button class="nav-btn" data-section="rotation">
                        <i class="fas fa-sync-alt"></i>
                        Rotation
                    </button>
                    <button class="nav-btn" data-section="testing">
                        <i class="fas fa-flask"></i>
                        Testing
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Character Section -->
            <section id="character-section" class="section active">
                <div class="section-header">
                    <h2><i class="fas fa-user"></i> Character Setup</h2>
                    <p>Configure your character's stats and abilities</p>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-id-card"></i> Basic Info</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="char-name">Character Name</label>
                                <input type="text" id="char-name" placeholder="Enter character name" value="Arcane Mage">
                            </div>
                            <div class="form-group">
                                <label for="char-class">Class</label>
                                <select id="char-class">
                                    <option value="mage">Mage</option>
                                    <option value="warlock">Warlock</option>
                                    <option value="priest">Priest</option>
                                    <option value="shaman">Shaman</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Core Stats</h3>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-input">
                                    <label for="health">Health</label>
                                    <input type="number" id="health" value="1000" min="100" max="10000">
                                </div>
                                <div class="stat-input">
                                    <label for="mana">Mana</label>
                                    <input type="number" id="mana" value="1000" min="100" max="10000">
                                </div>
                                <div class="stat-input">
                                    <label for="spell-power">Spell Power</label>
                                    <input type="number" id="spell-power" value="100" min="0" max="2000">
                                </div>
                                <div class="stat-input">
                                    <label for="crit-chance">Crit Chance (%)</label>
                                    <input type="number" id="crit-chance" value="5" min="0" max="100" step="0.1">
                                </div>
                                <div class="stat-input">
                                    <label for="haste">Haste (%)</label>
                                    <input type="number" id="haste" value="0" min="0" max="100" step="0.1">
                                </div>
                                <div class="stat-input">
                                    <label for="hit-chance">Hit Chance (%)</label>
                                    <input type="number" id="hit-chance" value="83" min="0" max="100" step="0.1">
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="updateCharacter()">
                                <i class="fas fa-save"></i> Update Character
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> Character Summary</h3>
                    </div>
                    <div class="card-body">
                        <div id="character-summary" class="character-summary">
                            <div class="summary-item">
                                <span class="label">Name:</span>
                                <span class="value" id="summary-name">Arcane Mage</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Health:</span>
                                <span class="value" id="summary-health">1000</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Mana:</span>
                                <span class="value" id="summary-mana">1000</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Spell Power:</span>
                                <span class="value" id="summary-spell-power">100</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Crit Chance:</span>
                                <span class="value" id="summary-crit">5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Spells Section -->
            <section id="spells-section" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-fire"></i> Spell Builder</h2>
                    <p>Create and customize spells for your character</p>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-scroll"></i> Spell Templates</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="spell-template">Choose Template</label>
                                <select id="spell-template" onchange="loadSpellTemplate()">
                                    <option value="">Select a template...</option>
                                    <option value="direct_damage">Direct Damage</option>
                                    <option value="damage_over_time">Damage Over Time</option>
                                    <option value="direct_heal">Direct Heal</option>
                                    <option value="buff_spell">Buff Spell</option>
                                    <option value="aoe_damage">AoE Damage</option>
                                    <option value="instant_nuke">Instant Nuke</option>
                                </select>
                            </div>
                            <button class="btn btn-secondary" onclick="loadAdvancedTemplates()">
                                <i class="fas fa-star"></i> Load Advanced Templates
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-edit"></i> Spell Configuration</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="spell-name">Spell Name</label>
                                <input type="text" id="spell-name" placeholder="Enter spell name">
                            </div>
                            <div class="form-group">
                                <label for="spell-description">Description</label>
                                <textarea id="spell-description" placeholder="Describe your spell"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="spell-school">School</label>
                                    <select id="spell-school">
                                        <option value="fire">Fire</option>
                                        <option value="frost">Frost</option>
                                        <option value="arcane">Arcane</option>
                                        <option value="shadow">Shadow</option>
                                        <option value="nature">Nature</option>
                                        <option value="holy">Holy</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="target-type">Target Type</label>
                                    <select id="target-type">
                                        <option value="single_enemy">Single Enemy</option>
                                        <option value="aoe_enemy">AoE Enemy</option>
                                        <option value="self">Self</option>
                                        <option value="single_ally">Single Ally</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="cast-time">Cast Time (s)</label>
                                    <input type="number" id="cast-time" value="2.5" min="0" max="10" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="cooldown">Cooldown (s)</label>
                                    <input type="number" id="cooldown" value="0" min="0" max="600" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="mana-cost">Mana Cost</label>
                                    <input type="number" id="mana-cost" value="200" min="0" max="2000">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="base-damage-min">Min Damage</label>
                                    <input type="number" id="base-damage-min" value="200" min="0" max="5000">
                                </div>
                                <div class="form-group">
                                    <label for="base-damage-max">Max Damage</label>
                                    <input type="number" id="base-damage-max" value="300" min="0" max="5000">
                                </div>
                                <div class="form-group">
                                    <label for="spell-power-coeff">SP Coefficient</label>
                                    <input type="number" id="spell-power-coeff" value="1.0" min="0" max="3" step="0.1">
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="createSpell()">
                                    <i class="fas fa-plus"></i> Create Spell
                                </button>
                                <button class="btn btn-secondary" onclick="validateSpell()">
                                    <i class="fas fa-check"></i> Validate
                                </button>
                                <button class="btn btn-secondary" onclick="toggleAdvancedMode()">
                                    <i class="fas fa-cog"></i> <span id="advanced-mode-text">Advanced Mode</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Spell Configuration (Hidden by default) -->
                <div id="advanced-spell-config" class="card" style="display: none;">
                    <div class="card-header">
                        <h3><i class="fas fa-magic"></i> Advanced Spell Effects</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="spell-effects">Spell Effects</label>
                            <div id="spell-effects-container">
                                <div class="effect-item">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>Effect Type</label>
                                            <select class="effect-type">
                                                <option value="">Select effect...</option>
                                                <option value="damage_over_time">Damage Over Time</option>
                                                <option value="heal_over_time">Heal Over Time</option>
                                                <option value="buff">Buff</option>
                                                <option value="debuff">Debuff</option>
                                                <option value="proc">Proc Effect</option>
                                                <option value="conditional">Conditional Effect</option>
                                                <option value="chain">Chain Effect</option>
                                                <option value="stacking">Stacking Effect</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Value</label>
                                            <input type="number" class="effect-value" placeholder="Effect value">
                                        </div>
                                        <div class="form-group">
                                            <label>Duration (s)</label>
                                            <input type="number" class="effect-duration" placeholder="0" step="0.1">
                                        </div>
                                        <div class="form-group">
                                            <label>Chance (%)</label>
                                            <input type="number" class="effect-chance" placeholder="100" min="0" max="100">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn-icon btn-danger" onclick="removeEffect(this)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Advanced Effect Properties (shown based on effect type) -->
                                    <div class="advanced-effect-properties" style="display: none;">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Tick Interval (s)</label>
                                                <input type="number" class="effect-tick-interval" placeholder="3.0" step="0.1">
                                            </div>
                                            <div class="form-group">
                                                <label>Max Stacks</label>
                                                <input type="number" class="effect-max-stacks" placeholder="1" min="1">
                                            </div>
                                            <div class="form-group">
                                                <label>Max Targets</label>
                                                <input type="number" class="effect-max-targets" placeholder="1" min="1">
                                            </div>
                                            <div class="form-group">
                                                <label>Proc Spell</label>
                                                <input type="text" class="effect-proc-spell" placeholder="Spell name">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addEffect()">
                                <i class="fas fa-plus"></i> Add Effect
                            </button>
                        </div>

                        <div class="form-group">
                            <label for="spell-conditions">Spell Conditions</label>
                            <div id="spell-conditions-container">
                                <div class="condition-item">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>Condition Type</label>
                                            <select class="condition-type">
                                                <option value="">Select condition...</option>
                                                <option value="target_health_below">Target Health Below</option>
                                                <option value="target_health_above">Target Health Above</option>
                                                <option value="caster_mana_below">Caster Mana Below</option>
                                                <option value="buff_present">Buff Present</option>
                                                <option value="debuff_present">Debuff Present</option>
                                                <option value="spell_critical">Spell Critical</option>
                                                <option value="combo_points">Combo Points</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Operator</label>
                                            <select class="condition-operator">
                                                <option value="==">=</option>
                                                <option value="!=">=</option>
                                                <option value="<"><</option>
                                                <option value="<="><=</option>
                                                <option value=">">></option>
                                                <option value=">=">>=</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Value</label>
                                            <input type="text" class="condition-value" placeholder="Condition value">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn-icon btn-danger" onclick="removeCondition(this)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addCondition()">
                                <i class="fas fa-plus"></i> Add Condition
                            </button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="spell-channeled">
                                    <input type="checkbox" id="spell-channeled"> Channeled Spell
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="channel-ticks">Channel Ticks</label>
                                <input type="number" id="channel-ticks" value="1" min="1" max="20">
                            </div>
                            <div class="form-group">
                                <label for="spell-interruptible">
                                    <input type="checkbox" id="spell-interruptible" checked> Interruptible
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Templates -->
                <div id="advanced-templates" class="card" style="display: none;">
                    <div class="card-header">
                        <h3><i class="fas fa-star"></i> Advanced Spell Templates</h3>
                    </div>
                    <div class="card-body">
                        <div class="advanced-templates-grid">
                            <div class="template-card" onclick="loadAdvancedTemplate('living_bomb')">
                                <div class="template-icon fire">🔥</div>
                                <h4>Living Bomb</h4>
                                <p>DoT that explodes when it expires</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('chain_lightning')">
                                <div class="template-icon nature">⚡</div>
                                <h4>Chain Lightning</h4>
                                <p>Jumps between multiple targets</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('hot_streak')">
                                <div class="template-icon fire">🔥</div>
                                <h4>Hot Streak</h4>
                                <p>Synergy effect for fire spells</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('arcane_blast')">
                                <div class="template-icon arcane">🔮</div>
                                <h4>Arcane Blast</h4>
                                <p>Stacking spell with increasing power</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('execute')">
                                <div class="template-icon physical">⚔️</div>
                                <h4>Execute</h4>
                                <p>More damage on low health targets</p>
                            </div>
                            <div class="template-card" onclick="loadAdvancedTemplate('polymorph')">
                                <div class="template-icon arcane">🐑</div>
                                <h4>Polymorph</h4>
                                <p>Transform target into harmless creature</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> Created Spells</h3>
                        <button class="btn-icon btn-danger" onclick="clearAllSpells()" title="Clear all spells" style="margin-left: auto;">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="spell-list" class="spell-list">
                            <p class="empty-state">No spells created yet. Use the form above to create your first spell!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Rotation Section -->
            <section id="rotation-section" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-sync-alt"></i> Rotation Optimizer</h2>
                    <p>Find the optimal spell rotation for maximum DPS</p>
                </div>

                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-cogs"></i> Optimization Settings</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="optimization-goal">Goal</label>
                                    <select id="optimization-goal">
                                        <option value="maximum_dps">Maximum DPS</option>
                                        <option value="mana_efficient">Mana Efficient</option>
                                        <option value="burst_damage">Burst Damage</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="fight-duration">Duration (seconds)</label>
                                    <input type="number" id="fight-duration" value="60" min="10" max="600">
                                </div>
                                <div class="form-group">
                                    <label for="target-count">Target Count</label>
                                    <input type="number" id="target-count" value="1" min="1" max="10">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="movement-time">Movement Time (%)</label>
                                <input type="range" id="movement-time" min="0" max="50" value="0" oninput="updateMovementDisplay()">
                                <span id="movement-display">0%</span>
                            </div>
                            <button class="btn btn-primary" onclick="optimizeRotation()">
                                <i class="fas fa-play"></i> Optimize Rotation
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-list-ol"></i> Available Spells</h3>
                        </div>
                        <div class="card-body">
                            <div id="available-spells" class="spell-selection">
                                <p class="empty-state">Create some spells first to optimize rotations!</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Optimization Results</h3>
                    </div>
                    <div class="card-body">
                        <div id="rotation-results" class="rotation-results">
                            <div class="results-summary">
                                <div class="metric">
                                    <span class="metric-label">Total DPS:</span>
                                    <span class="metric-value" id="total-dps">-</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Total Damage:</span>
                                    <span class="metric-value" id="total-damage">-</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Mana Used:</span>
                                    <span class="metric-value" id="mana-used">-</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Efficiency:</span>
                                    <span class="metric-value" id="efficiency">-</span>
                                </div>
                            </div>
                            <div id="rotation-timeline" class="rotation-timeline">
                                <p class="empty-state">Run optimization to see rotation timeline</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Testing Section -->
            <section id="testing-section" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-flask"></i> Spell Testing</h2>
                    <p>Test individual spells and see damage calculations</p>
                </div>

                <div class="grid">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-crosshairs"></i> Test Setup</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="test-spell">Select Spell</label>
                                <select id="test-spell">
                                    <option value="">Choose a spell to test...</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="test-iterations">Test Iterations</label>
                                    <input type="number" id="test-iterations" value="100" min="1" max="1000">
                                </div>
                                <div class="form-group">
                                    <label for="target-armor">Target Armor</label>
                                    <input type="number" id="target-armor" value="0" min="0" max="10000">
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="testSpell()">
                                    <i class="fas fa-play"></i> Run Test
                                </button>
                                <button class="btn btn-secondary" onclick="testAllSpells()">
                                    <i class="fas fa-list"></i> Test All Spells
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Test Results</h3>
                        </div>
                        <div class="card-body">
                            <div id="test-results" class="test-results">
                                <div class="results-grid">
                                    <div class="result-item">
                                        <span class="result-label">Average Damage:</span>
                                        <span class="result-value" id="avg-damage">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">Min Damage:</span>
                                        <span class="result-value" id="min-damage">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">Max Damage:</span>
                                        <span class="result-value" id="max-damage">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">Crit Rate:</span>
                                        <span class="result-value" id="crit-rate">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">DPS:</span>
                                        <span class="result-value" id="spell-dps">-</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">DPM:</span>
                                        <span class="result-value" id="spell-dpm">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Test History</h3>
                    </div>
                    <div class="card-body">
                        <div id="test-history" class="test-history">
                            <p class="empty-state">No tests run yet. Use the form above to test spells!</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-magic fa-spin"></i>
            <p>Processing...</p>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notifications" class="notifications"></div>

    <!-- Scripts -->
    <script src="static/js/app.js"></script>
</body>
</html>
