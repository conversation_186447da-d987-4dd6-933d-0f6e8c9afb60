"""
Minimal test to isolate import issues.
"""

print("Starting minimal test...")

try:
    print("Step 1: Import StatType")
    from src.interfaces.base import StatType
    print("✓ StatType imported")
    
    print("Step 2: Import BaseStatContainer")
    from src.stats.base_stats import BaseStatContainer
    print("✓ BaseStatContainer imported")
    
    print("Step 3: Create BaseStatContainer")
    container = BaseStatContainer()
    print("✓ BaseStatContainer created")
    
    print("Step 4: Set a stat")
    container.set_stat(StatType.HEALTH, 1000)
    print("✓ Stat set")
    
    print("Step 5: Get the stat")
    health = container.get_stat(StatType.HEALTH)
    print(f"✓ Health: {health}")
    
    print("SUCCESS: Basic functionality works!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
