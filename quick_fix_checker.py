#!/usr/bin/env python3
"""
Quick Fix Checker - Check for the most common issues that users encounter
"""

import os
import re
from pathlib import Path

def check_common_issues():
    """Check for the most common issues users encounter."""
    print("🔧 Quick Fix Checker - Common Issues")
    print("=" * 50)
    
    issues_found = []
    fixes_applied = []
    
    base_dir = Path(__file__).parent
    
    # 1. Check if main files exist
    print("\n📁 Checking file structure...")
    required_files = [
        'index.html',
        'static/js/app.js',
        'static/css/style.css'
    ]
    
    for file_path in required_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"   ✅ {file_path} exists")
        else:
            print(f"   ❌ {file_path} missing")
            issues_found.append(f"Missing file: {file_path}")
    
    # 2. Check JavaScript console errors
    print("\n🔍 Checking for common JavaScript issues...")
    js_file = base_dir / 'static/js/app.js'
    
    if js_file.exists():
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for error handling functions
        if 'safeGetValue' in js_content:
            print("   ✅ Safe DOM access functions present")
        else:
            print("   ⚠️  Safe DOM access functions missing")
            issues_found.append("Missing safe DOM access functions")
        
        if 'showNotification' in js_content and 'if (!notifications)' in js_content:
            print("   ✅ Notification error handling present")
        else:
            print("   ⚠️  Notification error handling missing")
            issues_found.append("Missing notification error handling")
        
        if 'showLoading' in js_content and 'if (!overlay)' in js_content:
            print("   ✅ Loading overlay error handling present")
        else:
            print("   ⚠️  Loading overlay error handling missing")
            issues_found.append("Missing loading overlay error handling")
    
    # 3. Check HTML for required elements
    print("\n📄 Checking HTML structure...")
    html_file = base_dir / 'index.html'
    
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        required_elements = [
            'loading-overlay',
            'notifications',
            'spell-name',
            'spell-description'
        ]
        
        for element in required_elements:
            if f'id="{element}"' in html_content:
                print(f"   ✅ Element #{element} exists")
            else:
                print(f"   ⚠️  Element #{element} missing")
                issues_found.append(f"Missing HTML element: {element}")
    
    # 4. Check CSS for basic styles
    print("\n🎨 Checking CSS structure...")
    css_file = base_dir / 'static/css/style.css'
    
    if css_file.exists():
        with open(css_file, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        required_styles = [
            '.loading-overlay',
            '.notification',
            '.spell-type-card',
            '.btn'
        ]
        
        for style in required_styles:
            if style in css_content:
                print(f"   ✅ Style {style} exists")
            else:
                print(f"   ⚠️  Style {style} missing")
                issues_found.append(f"Missing CSS style: {style}")
    
    # 5. Check for backend connectivity
    print("\n🔗 Checking backend setup...")
    backend_file = base_dir / 'web_backend.py'
    
    if backend_file.exists():
        print("   ✅ Backend file exists")
        try:
            with open(backend_file, 'r', encoding='utf-8') as f:
                backend_content = f.read()
            
            if 'from flask import' in backend_content or 'import flask' in backend_content:
                print("   ✅ Flask import found")
            else:
                print("   ⚠️  Flask import missing")
                issues_found.append("Flask import missing in backend")
            
            if '@app.route' in backend_content:
                print("   ✅ Flask routes defined")
            else:
                print("   ⚠️  No Flask routes found")
                issues_found.append("No Flask routes found")
                
        except Exception as e:
            print(f"   ❌ Error reading backend file: {e}")
            issues_found.append(f"Backend file read error: {e}")
    else:
        print("   ⚠️  Backend file missing (frontend-only mode)")
    
    # 6. Check for common browser compatibility issues
    print("\n🌐 Checking browser compatibility...")
    
    if js_file.exists():
        # Check for modern JavaScript features that might cause issues
        modern_features = [
            ('const ', 'const declarations'),
            ('let ', 'let declarations'),
            ('=>', 'arrow functions'),
            ('async ', 'async functions'),
            ('await ', 'await expressions')
        ]
        
        for feature, description in modern_features:
            if feature in js_content:
                print(f"   ✅ Uses {description} (modern browsers)")
            else:
                print(f"   ℹ️  No {description} found")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Quick Fix Summary")
    print("=" * 50)
    
    if not issues_found:
        print("🎉 No common issues found!")
        print("✅ Application appears to be properly configured")
        print("\n💡 If you're experiencing issues:")
        print("   1. Check browser console for JavaScript errors")
        print("   2. Ensure all files are saved")
        print("   3. Try refreshing the browser")
        print("   4. Check network connectivity if using backend")
    else:
        print(f"⚠️  Found {len(issues_found)} potential issues:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        print("\n🔧 Recommended fixes:")
        print("   1. Ensure all required files exist")
        print("   2. Check JavaScript console for errors")
        print("   3. Verify HTML elements have correct IDs")
        print("   4. Ensure CSS styles are properly defined")
        print("   5. Check backend setup if using server features")
    
    return len(issues_found) == 0

def check_browser_console_errors():
    """Provide guidance for checking browser console errors."""
    print("\n🌐 Browser Console Error Checking Guide")
    print("=" * 50)
    print("To check for JavaScript errors in your browser:")
    print("\n1. Open your browser (Chrome, Firefox, Edge)")
    print("2. Navigate to your WoW Simulator page")
    print("3. Press F12 or right-click and select 'Inspect'")
    print("4. Click on the 'Console' tab")
    print("5. Look for red error messages")
    print("\nCommon error types:")
    print("   🔴 TypeError: Cannot read properties of null")
    print("   🔴 ReferenceError: function is not defined")
    print("   🔴 SyntaxError: Unexpected token")
    print("   🟡 Warning messages (usually safe to ignore)")
    print("\nIf you see errors, copy them and I can help fix them!")

def main():
    """Run the quick fix checker."""
    is_clean = check_common_issues()
    check_browser_console_errors()
    
    print("\n" + "=" * 50)
    if is_clean:
        print("✅ Quick fix check completed - no issues found!")
    else:
        print("⚠️  Quick fix check found some issues to address")
    
    return is_clean

if __name__ == "__main__":
    main()
