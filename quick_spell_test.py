"""
Quick test to verify spell creation system works.
"""

def test_basic_functionality():
    """Test basic spell creation functionality."""
    try:
        print("Testing spell creation system...")
        
        # Test imports
        from src.spells.spell_builder import SpellBuilder, SpellTemplateLibrary
        from src.spells.spell_validator import SpellValidator
        print("✓ Imports successful")
        
        # Test template library
        templates = SpellTemplateLibrary.get_all_templates()
        print(f"✓ Loaded {len(templates)} templates")
        
        # Test validator
        validator = SpellValidator()
        
        # Test a simple spell
        simple_spell = {
            "name": "Test Spell",
            "description": "A simple test spell",
            "school": "fire",
            "cast_time": 2.0,
            "cooldown": 0.0,
            "mana_cost": 200,
            "target_type": "single_enemy",
            "base_damage": 400,
            "spell_power_coefficient": 1.0,
            "can_crit": True
        }
        
        is_valid, errors, config = validator.validate_spell_from_dict(simple_spell)
        print(f"✓ Validation result: {is_valid}")
        
        if is_valid:
            # Test building
            builder = SpellBuilder()
            spell = builder.build_spell_from_dict(simple_spell)
            print(f"✓ Created spell: {spell.name}")
            
            # Test with character
            from src.character import ModularCharacter
            mage = ModularCharacter("Test Mage")
            damage = spell.calculate_damage(mage)
            print(f"✓ Damage calculation: {damage}")
            
            print("\n🎉 Spell creation system is working!")
            return True
        else:
            print(f"✗ Validation failed: {errors}")
            return False
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_basic_functionality()
