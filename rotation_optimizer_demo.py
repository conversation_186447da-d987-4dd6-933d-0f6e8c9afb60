"""
Rotation Optimizer Demo
Shows how to find optimal spell rotations for maximum DPS.
"""

from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.spells.spell_builder import <PERSON>pell<PERSON><PERSON><PERSON>, SpellTemplateLibrary
from src.optimization.rotation_optimizer import RotationOptimizer, OptimizationGoal
from src.optimization.timeline_visualizer import TimelineVisualizer
from src.stats.standalone_stats import StatType


def create_fire_mage():
    """Create a well-geared fire mage for testing."""
    mage = ModularCharacter("Fire Mage")
    
    # Set level 60 stats
    mage.stats.set_stat(StatType.HEALTH, 3200)
    mage.stats.set_stat(StatType.MANA, 6000)
    mage.stats.set_stat(StatType.SPELL_POWER, 400)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.20)
    mage.stats.set_stat(StatType.HIT_CHANCE, 0.99)
    
    # Equipment bonuses
    mage.equip_item("Staff_of_Dominance", {
        StatType.SPELL_POWER: 95,
        StatType.CRIT_CHANCE: 0.02
    })
    
    mage.equip_item("Netherwind_Set", {
        StatType.SPELL_POWER: 60,
        StatType.MANA: 400
    })
    
    # Talent bonuses
    mage.learn_talent("Fire_Power", {
        StatType.SPELL_POWER: 10.0  # 10% more fire damage
    }, is_percentage=True)
    
    mage.learn_talent("Critical_Mass", {
        StatType.CRIT_CHANCE: 0.06  # 6% more crit
    })
    
    # Set current resources
    mage.current_health = mage.get_max_health()
    mage.current_mana = mage.get_max_mana()
    
    return mage


def create_spell_arsenal():
    """Create a variety of spells for rotation testing."""
    builder = SpellBuilder()
    templates = SpellTemplateLibrary.get_all_templates()
    
    spells = []
    
    # Direct damage spells
    fireball_data = templates['direct_damage'].copy()
    fireball_data.update({
        "name": "Fireball",
        "school": "fire",
        "cast_time": 2.5,
        "cooldown": 0.0,
        "mana_cost": 260,
        "base_damage": [500, 600],
        "spell_power_coefficient": 1.0
    })
    spells.append(builder.build_spell_from_dict(fireball_data))
    
    # Fast filler spell
    scorch_data = templates['direct_damage'].copy()
    scorch_data.update({
        "name": "Scorch",
        "school": "fire",
        "cast_time": 1.5,
        "cooldown": 0.0,
        "mana_cost": 180,
        "base_damage": [320, 380],
        "spell_power_coefficient": 0.8
    })
    spells.append(builder.build_spell_from_dict(scorch_data))
    
    # High damage, long cast
    pyroblast_data = templates['direct_damage'].copy()
    pyroblast_data.update({
        "name": "Pyroblast",
        "school": "fire",
        "cast_time": 6.0,
        "cooldown": 0.0,
        "mana_cost": 350,
        "base_damage": [950, 1150],
        "spell_power_coefficient": 1.5
    })
    spells.append(builder.build_spell_from_dict(pyroblast_data))
    
    # Instant nuke with cooldown
    fire_blast_data = templates['instant_nuke'].copy()
    fire_blast_data.update({
        "name": "Fire Blast",
        "school": "fire",
        "cast_time": 0.0,
        "cooldown": 8.0,
        "mana_cost": 200,
        "base_damage": [350, 420],
        "spell_power_coefficient": 0.9
    })
    spells.append(builder.build_spell_from_dict(fire_blast_data))
    
    # Major cooldown
    combustion_data = templates['instant_nuke'].copy()
    combustion_data.update({
        "name": "Combustion",
        "school": "fire",
        "cast_time": 0.0,
        "cooldown": 180.0,  # 3 minute cooldown
        "mana_cost": 0,
        "base_damage": [800, 1000],
        "spell_power_coefficient": 1.2
    })
    spells.append(builder.build_spell_from_dict(combustion_data))
    
    return spells


def demo_basic_optimization():
    """Demonstrate basic rotation optimization."""
    print("=== Basic Rotation Optimization ===")
    print("Finding optimal DPS rotation for a Fire Mage")
    print()
    
    mage = create_fire_mage()
    spells = create_spell_arsenal()
    
    print(f"Character: {mage.name}")
    print(f"Spell Power: {mage.get_spell_power():.0f}")
    print(f"Crit Chance: {mage.get_crit_chance() * 100:.1f}%")
    print(f"Mana: {mage.get_max_mana():.0f}")
    print()
    
    print("Available spells:")
    for spell in spells:
        damage = spell.calculate_damage(mage)
        cast_time = max(spell.cast_time, 1.5)  # Include GCD
        dps = damage / cast_time
        print(f"  {spell.name}: {damage} damage, {cast_time}s cast, {dps:.1f} DPS")
    print()
    
    # Optimize rotation
    optimizer = RotationOptimizer()
    result = optimizer.optimize_rotation(
        character=mage,
        available_spells=spells,
        duration=60.0,  # 1 minute fight
        goal=OptimizationGoal.MAXIMUM_DPS
    )
    
    print("Optimization Results:")
    print(f"Total Damage: {result.total_damage:,}")
    print(f"DPS: {result.dps:.1f}")
    print(f"Mana Used: {result.mana_used:,} / {mage.get_max_mana():.0f}")
    print(f"Mana Efficiency: {result.mana_efficiency:.2f} damage/mana")
    print(f"Cooldown Efficiency: {result.cooldown_efficiency:.1f}%")
    print()
    
    # Show first 10 casts
    print("First 10 casts:")
    for i, step in enumerate(result.rotation_steps[:10]):
        print(f"  {step.timestamp:5.1f}s: {step.spell_name} ({step.damage:,} damage)")
    
    if len(result.rotation_steps) > 10:
        print(f"  ... and {len(result.rotation_steps) - 10} more casts")


def demo_optimization_goals():
    """Demonstrate different optimization goals."""
    print("\n=== Different Optimization Goals ===")
    print("Comparing DPS vs Mana Efficiency vs Burst")
    print()
    
    mage = create_fire_mage()
    spells = create_spell_arsenal()
    optimizer = RotationOptimizer()
    
    # Test different goals
    goals = [
        (OptimizationGoal.MAXIMUM_DPS, "Maximum DPS"),
        (OptimizationGoal.MANA_EFFICIENT, "Mana Efficient"),
        (OptimizationGoal.BURST_DAMAGE, "Burst Damage")
    ]
    
    results = {}
    for goal, name in goals:
        result = optimizer.optimize_rotation(
            character=mage,
            available_spells=spells,
            duration=30.0,  # 30 second comparison
            goal=goal
        )
        results[name] = result
    
    # Compare results
    print(f"{'Goal':<15} {'DPS':<8} {'Damage':<10} {'Mana Used':<10} {'Efficiency':<10}")
    print("-" * 60)
    
    for name, result in results.items():
        print(f"{name:<15} {result.dps:<8.1f} {result.total_damage:<10,} {result.mana_used:<10,} {result.mana_efficiency:<10.2f}")
    
    print()
    
    # Show spell usage differences
    print("Spell usage by goal:")
    for goal_name, result in results.items():
        print(f"\n{goal_name}:")
        sorted_spells = sorted(
            result.spell_breakdown.items(),
            key=lambda x: x[1]["casts"],
            reverse=True
        )
        for spell_name, stats in sorted_spells[:3]:  # Top 3 most used
            if stats["casts"] > 0:
                print(f"  {spell_name}: {stats['casts']} casts, {stats['damage']:,} damage")


def demo_timeline_visualization():
    """Demonstrate timeline visualization."""
    print("\n=== Timeline Visualization ===")
    print("Visual representation of spell rotation")
    print()
    
    mage = create_fire_mage()
    spells = create_spell_arsenal()
    optimizer = RotationOptimizer()
    visualizer = TimelineVisualizer()
    
    # Optimize for a shorter duration for cleaner visualization
    result = optimizer.optimize_rotation(
        character=mage,
        available_spells=spells,
        duration=30.0,
        goal=OptimizationGoal.MAXIMUM_DPS
    )
    
    # Create timeline visualization
    timeline = visualizer.visualize_rotation(result, "Fire Mage DPS Rotation")
    print(timeline)


def demo_rotation_comparison():
    """Demonstrate comparing different spell sets."""
    print("\n=== Rotation Comparison ===")
    print("Comparing different spell combinations")
    print()
    
    mage = create_fire_mage()
    all_spells = create_spell_arsenal()
    optimizer = RotationOptimizer()
    visualizer = TimelineVisualizer()
    
    # Create different spell sets
    spell_sets = [
        ("All Spells", all_spells),
        ("No Cooldowns", [s for s in all_spells if s.cooldown == 0]),
        ("Fast Casts Only", [s for s in all_spells if s.cast_time <= 2.0]),
        ("High Damage Only", [s for s in all_spells if s.base_damage >= 400])
    ]
    
    # Compare rotations
    results = optimizer.compare_rotations(
        character=mage,
        spell_sets=spell_sets,
        duration=45.0,
        goal=OptimizationGoal.MAXIMUM_DPS
    )
    
    # Show comparison
    comparison_chart = visualizer.create_comparison_chart(results)
    print(comparison_chart)
    
    print("\nDetailed breakdown:")
    for name, result in results.items():
        print(f"\n{name}:")
        print(f"  DPS: {result.dps:.1f}")
        print(f"  Total Casts: {len(result.rotation_steps)}")
        print(f"  Mana Efficiency: {result.mana_efficiency:.2f}")
        
        # Most used spell
        if result.spell_breakdown:
            top_spell = max(result.spell_breakdown.items(), key=lambda x: x[1]["casts"])
            print(f"  Most Used: {top_spell[0]} ({top_spell[1]['casts']} casts)")


def demo_mana_management():
    """Demonstrate mana usage visualization."""
    print("\n=== Mana Management ===")
    print("Tracking mana usage over time")
    print()
    
    mage = create_fire_mage()
    spells = create_spell_arsenal()
    optimizer = RotationOptimizer()
    visualizer = TimelineVisualizer()
    
    starting_mana = int(mage.get_max_mana())
    
    # Optimize for longer duration to show mana constraints
    result = optimizer.optimize_rotation(
        character=mage,
        available_spells=spells,
        duration=120.0,  # 2 minutes
        goal=OptimizationGoal.MAXIMUM_DPS
    )
    
    # Show mana timeline
    mana_timeline = visualizer.create_mana_timeline(result, starting_mana)
    print(mana_timeline)
    
    # Analyze mana efficiency
    print(f"\nMana Analysis:")
    print(f"Fight Duration: {result.total_time:.1f}s")
    print(f"Mana per Second: {result.mana_used / result.total_time:.1f}")
    
    if result.mana_used >= starting_mana * 0.9:
        print("⚠️  High mana usage - consider mana efficiency optimization")
    elif result.mana_used <= starting_mana * 0.5:
        print("✅ Conservative mana usage - could increase DPS")
    else:
        print("✅ Balanced mana usage")


def main():
    """Run all rotation optimizer demonstrations."""
    print("⚡ WoW Simulator - Rotation Optimizer Demo")
    print("=" * 60)
    print("Find optimal spell rotations for maximum damage!")
    print("=" * 60)
    
    demo_basic_optimization()
    demo_optimization_goals()
    demo_timeline_visualization()
    demo_rotation_comparison()
    demo_mana_management()
    
    print("\n" + "=" * 60)
    print("✅ Rotation Optimizer Demo Complete!")
    print("\nKey features demonstrated:")
    print("⚡ Automatic DPS optimization")
    print("🎯 Multiple optimization goals")
    print("📊 Visual timeline representation")
    print("🔄 Rotation comparison tools")
    print("💧 Mana usage tracking")
    print("📈 Detailed performance analysis")
    print("\nYour WoW Simulator now provides:")
    print("• Optimal rotation finding")
    print("• Multiple optimization strategies")
    print("• Visual rotation analysis")
    print("• Mana efficiency optimization")
    print("• Performance comparison tools")
    print("• Real-time rotation adjustment")


if __name__ == "__main__":
    main()
