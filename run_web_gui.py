#!/usr/bin/env python3
"""
WoW Simulator Web GUI Launcher
Simple script to start the web interface for the WoW Simulator.
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ['flask', 'flask-cors', 'requests']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    return True

def start_backend():
    """Start the Flask backend server."""
    print("🚀 Starting WoW Simulator Backend...")
    
    # Start the backend in a subprocess
    backend_process = subprocess.Popen([
        sys.executable, 'web_backend.py'
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    return backend_process

def open_frontend():
    """Open the frontend in the default web browser."""
    frontend_path = Path('index.html').absolute()
    frontend_url = f"file://{frontend_path}"
    
    print(f"🌐 Opening frontend: {frontend_url}")
    webbrowser.open(frontend_url)

def main():
    """Main function to start the WoW Simulator Web GUI."""
    print("🧙‍♂️ WoW Simulator - Web GUI Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path('index.html').exists():
        print("❌ Error: index.html not found!")
        print("   Make sure you're running this script from the WoW Simulator directory.")
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    print("✅ All dependencies found!")
    
    try:
        # Start backend
        backend_process = start_backend()
        
        # Wait a moment for the backend to start
        print("⏳ Waiting for backend to start...")
        time.sleep(3)
        
        # Check if backend is running
        if backend_process.poll() is not None:
            print("❌ Backend failed to start!")
            stdout, stderr = backend_process.communicate()
            print("STDOUT:", stdout.decode())
            print("STDERR:", stderr.decode())
            return 1
        
        print("✅ Backend started successfully!")
        
        # Open frontend
        open_frontend()
        
        print("\n🎉 WoW Simulator Web GUI is now running!")
        print("📡 Backend API: http://localhost:5000")
        print("🌐 Frontend: file://index.html")
        print("\n💡 Instructions:")
        print("   1. The web interface should open automatically in your browser")
        print("   2. If not, manually open 'index.html' in your browser")
        print("   3. Start by configuring your character in the Character tab")
        print("   4. Create spells in the Spells tab")
        print("   5. Optimize rotations in the Rotation tab")
        print("   6. Test individual spells in the Testing tab")
        print("\n✨ Enhanced Features:")
        print("   🔐 Session Management: Your data is automatically saved per session")
        print("   💾 Real-time Sync: All changes are immediately saved to the backend")
        print("   🔄 Data Persistence: Characters and spells persist across browser refreshes")
        print("   🚀 Full Backend Integration: No more local storage fallbacks")
        print("\n⚠️  Note: Keep this terminal window open while using the web interface")
        print("   Press Ctrl+C to stop the backend server")
        
        # Wait for user to stop the server
        try:
            backend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping backend server...")
            backend_process.terminate()
            backend_process.wait()
            print("✅ Backend stopped successfully!")
        
    except Exception as e:
        print(f"❌ Error starting WoW Simulator: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
