<!-- Super Simple Spell Builder -->
<div id="advanced-spell-config" class="card" style="display: none;">
    <div class="card-header">
        <h3><i class="fas fa-magic"></i> Super Simple Spell Builder</h3>
        <p class="builder-subtitle">Create any spell in 30 seconds!</p>
    </div>
    <div class="card-body">
        
        <!-- Spell Preview -->
        <div class="spell-preview-card">
            <h4><i class="fas fa-eye"></i> Your Spell Preview</h4>
            <div class="spell-preview-content">
                <div class="spell-preview-name" id="preview-spell-name">My Awesome Spell</div>
                <div class="spell-preview-description" id="preview-spell-description">
                    A powerful spell that deals damage to enemies
                </div>
                <div class="spell-preview-stats">
                    <div class="preview-stat">
                        <span class="stat-label">Damage:</span>
                        <span class="stat-value" id="preview-damage">400</span>
                    </div>
                    <div class="preview-stat">
                        <span class="stat-label">Mana Cost:</span>
                        <span class="stat-value" id="preview-mana">200</span>
                    </div>
                    <div class="preview-stat">
                        <span class="stat-label">Cast Time:</span>
                        <span class="stat-value" id="preview-cast-time">2.5s</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Simple Controls -->
        <div class="simple-controls">
            
            <!-- Spell Name -->
            <div class="simple-control-group">
                <label><i class="fas fa-tag"></i> Spell Name:</label>
                <input type="text" id="simple-spell-name" value="My Awesome Spell" 
                       oninput="updateSpellPreview()" placeholder="Enter spell name">
            </div>

            <!-- Spell Type -->
            <div class="simple-control-group">
                <label><i class="fas fa-fire"></i> What does it do?</label>
                <div class="simple-spell-types">
                    <button class="simple-type-btn active" data-type="damage" onclick="selectSimpleType('damage')">
                        <i class="fas fa-fire"></i> Deal Damage
                    </button>
                    <button class="simple-type-btn" data-type="heal" onclick="selectSimpleType('heal')">
                        <i class="fas fa-heart"></i> Heal
                    </button>
                    <button class="simple-type-btn" data-type="shield" onclick="selectSimpleType('shield')">
                        <i class="fas fa-shield-alt"></i> Shield
                    </button>
                    <button class="simple-type-btn" data-type="buff" onclick="selectSimpleType('buff')">
                        <i class="fas fa-arrow-up"></i> Buff
                    </button>
                </div>
            </div>

            <!-- Power Level -->
            <div class="simple-control-group">
                <label><i class="fas fa-bolt"></i> How powerful?</label>
                <div class="simple-slider-control">
                    <input type="range" id="simple-power" min="100" max="1000" value="400" 
                           oninput="updateSpellPreview()">
                    <div class="slider-labels">
                        <span>Weak</span>
                        <span>Medium</span>
                        <span>Strong</span>
                    </div>
                </div>
            </div>

            <!-- Mana Cost -->
            <div class="simple-control-group">
                <label><i class="fas fa-tint"></i> Mana cost?</label>
                <div class="simple-slider-control">
                    <input type="range" id="simple-mana" min="50" max="500" value="200" 
                           oninput="updateSpellPreview()">
                    <div class="slider-labels">
                        <span>Cheap</span>
                        <span>Normal</span>
                        <span>Expensive</span>
                    </div>
                </div>
            </div>

            <!-- Cast Speed -->
            <div class="simple-control-group">
                <label><i class="fas fa-clock"></i> How fast to cast?</label>
                <div class="simple-slider-control">
                    <input type="range" id="simple-speed" min="0.5" max="5" step="0.5" value="2.5" 
                           oninput="updateSpellPreview()">
                    <div class="slider-labels">
                        <span>Instant</span>
                        <span>Normal</span>
                        <span>Slow</span>
                    </div>
                </div>
            </div>

            <!-- When to Use -->
            <div class="simple-control-group">
                <label><i class="fas fa-question-circle"></i> When to use?</label>
                <select id="simple-condition" onchange="updateSpellPreview()">
                    <option value="always">Always (most spells)</option>
                    <option value="low_health">When enemy low health</option>
                    <option value="high_mana">When I have lots of mana</option>
                    <option value="low_mana">When I'm low on mana</option>
                    <option value="critical">After critical hit</option>
                    <option value="cooldown">With other big spells</option>
                </select>
            </div>

            <!-- Create Button -->
            <div class="simple-control-group">
                <button class="btn btn-primary btn-large" onclick="createSimpleSpell()">
                    <i class="fas fa-magic"></i> Create My Spell!
                </button>
            </div>

        </div>
    </div>
</div>
