"""
Spell Creation System Demo
Shows how to create, validate, and test custom spells using the new system.
"""

import json
from src.spells.spell_builder import <PERSON>pell<PERSON><PERSON><PERSON>, SpellTemplateLibrary
from src.spells.spell_validator import <PERSON>pellValidator
from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.spells.modular_spells import CombatSimulator
from src.stats.standalone_stats import StatType


def demo_spell_templates():
    """Demonstrate the spell template library."""
    print("=== Spell Template Library Demo ===")
    
    templates = SpellTemplateLibrary.get_all_templates()
    
    print("Available spell templates:")
    for template_name, template_data in templates.items():
        print(f"  • {template_name}: {template_data['description']}")
    
    # Show a detailed template
    print(f"\nDetailed view of 'direct_damage' template:")
    direct_damage = templates['direct_damage']
    for key, value in direct_damage.items():
        print(f"  {key}: {value}")


def demo_spell_validation():
    """Demonstrate spell validation."""
    print("\n=== Spell Validation Demo ===")
    
    validator = SpellValidator()
    
    # Test a valid spell
    print("Testing valid spell...")
    valid_spell = {
        "name": "Test Fireball",
        "description": "A test fireball spell",
        "school": "fire",
        "cast_time": 2.5,
        "cooldown": 0.0,
        "mana_cost": 200,
        "target_type": "single_enemy",
        "base_damage": [400, 500],
        "spell_power_coefficient": 1.0,
        "can_crit": True
    }
    
    is_valid, errors, config = validator.validate_spell_from_dict(valid_spell)
    print(f"  Valid: {is_valid}")
    if errors:
        print(f"  Errors: {errors}")
    
    # Test an invalid spell (overpowered)
    print("\nTesting overpowered spell...")
    overpowered_spell = {
        "name": "Overpowered Nuke",
        "description": "Way too powerful",
        "school": "arcane",
        "cast_time": 0.0,  # Instant
        "cooldown": 0.0,   # No cooldown
        "mana_cost": 10,   # Very cheap
        "target_type": "single_enemy",
        "base_damage": [2000, 3000],  # Massive damage
        "spell_power_coefficient": 3.0,  # High scaling
        "can_crit": True
    }
    
    is_valid, errors, config = validator.validate_spell_from_dict(overpowered_spell)
    print(f"  Valid: {is_valid}")
    if errors:
        print("  Errors:")
        for error in errors:
            print(f"    - {error}")
    
    # Show balance suggestions
    if not is_valid and config:
        suggestions = validator.suggest_balance_fixes(config)
        if suggestions:
            print("  Balance suggestions:")
            for suggestion in suggestions:
                print(f"    - {suggestion}")


def demo_spell_creation():
    """Demonstrate creating spells from configurations."""
    print("\n=== Spell Creation Demo ===")
    
    builder = SpellBuilder()
    
    # Create a custom spell
    custom_spell_data = {
        "name": "Frost Nova",
        "description": "Freezes enemies in place and deals frost damage.",
        "school": "frost",
        "cast_time": 1.5,
        "cooldown": 25.0,
        "mana_cost": 180,
        "target_type": "aoe_enemy",
        "range": 0.0,  # Self-centered AoE
        "aoe_radius": 10.0,
        "base_damage": [200, 250],
        "spell_power_coefficient": 0.6,
        "can_crit": True,
        "effects": [
            {
                "type": "debuff",
                "value": {"frozen": True, "movement_speed": -1.0},
                "duration": 8.0,
                "chance": 1.0
            }
        ]
    }
    
    try:
        spell = builder.build_spell_from_dict(custom_spell_data)
        print(f"Created spell: {spell.name}")
        print(f"Description: {spell.get_description()}")
        print(f"School: {spell.school.value}")
        print(f"Cast time: {spell.cast_time}s")
        print(f"Cooldown: {spell.cooldown}s")
        print(f"Mana cost: {spell.mana_cost}")
        
        return spell
        
    except ValueError as e:
        print(f"Failed to create spell: {e}")
        return None


def demo_spell_testing():
    """Demonstrate testing created spells."""
    print("\n=== Spell Testing Demo ===")
    
    # Create a test character
    mage = ModularCharacter("Test Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 300)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.15)
    mage.current_mana = 5000
    
    # Create a target
    target = ModularCharacter("Test Target")
    target.stats.set_stat(StatType.HEALTH, 3000)
    target.current_health = target.get_max_health()
    
    # Create spells from templates
    builder = SpellBuilder()
    templates = SpellTemplateLibrary.get_all_templates()
    
    print(f"Testing spells with mage (Spell Power: {mage.get_spell_power()}, Crit: {mage.get_crit_chance()*100:.1f}%)")
    print(f"Target health: {target.current_health}")
    print()
    
    # Test different spell types
    test_spells = ['direct_damage', 'dot', 'instant_nuke']
    
    for template_name in test_spells:
        if template_name in templates:
            try:
                spell_data = templates[template_name].copy()
                spell_data['name'] = f"Test {template_name.replace('_', ' ').title()}"
                
                spell = builder.build_spell_from_dict(spell_data)
                
                # Calculate theoretical damage
                theoretical_damage = spell.calculate_damage(mage, target)
                
                print(f"{spell.name}:")
                print(f"  Theoretical damage: {theoretical_damage}")
                print(f"  Mana cost: {spell.mana_cost}")
                print(f"  Cast time: {spell.cast_time}s")
                print(f"  DPM (Damage per Mana): {theoretical_damage/spell.mana_cost if spell.mana_cost > 0 else 'N/A'}")
                print()
                
            except Exception as e:
                print(f"Error testing {template_name}: {e}")


def demo_spell_comparison():
    """Demonstrate comparing different spell configurations."""
    print("\n=== Spell Comparison Demo ===")
    
    builder = SpellBuilder()
    
    # Create variations of a fireball spell
    base_fireball = {
        "name": "Fireball",
        "description": "Basic fireball",
        "school": "fire",
        "cast_time": 2.5,
        "cooldown": 0.0,
        "mana_cost": 200,
        "target_type": "single_enemy",
        "base_damage": [400, 500],
        "spell_power_coefficient": 1.0,
        "can_crit": True
    }
    
    # Fast fireball (lower damage, faster cast)
    fast_fireball = base_fireball.copy()
    fast_fireball.update({
        "name": "Fast Fireball",
        "cast_time": 1.5,
        "base_damage": [300, 400],
        "mana_cost": 150
    })
    
    # Powerful fireball (higher damage, slower cast)
    powerful_fireball = base_fireball.copy()
    powerful_fireball.update({
        "name": "Powerful Fireball",
        "cast_time": 4.0,
        "base_damage": [600, 750],
        "mana_cost": 300
    })
    
    # Create test character
    mage = ModularCharacter("Comparison Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 250)
    
    target = ModularCharacter("Target")
    
    spells_data = [base_fireball, fast_fireball, powerful_fireball]
    
    print("Spell Comparison:")
    print(f"{'Spell Name':<20} {'Damage':<10} {'Cast Time':<12} {'Mana':<8} {'DPS':<8} {'DPM':<8}")
    print("-" * 70)
    
    for spell_data in spells_data:
        try:
            spell = builder.build_spell_from_dict(spell_data)
            damage = spell.calculate_damage(mage, target)
            dps = damage / max(spell.cast_time, 1.5)  # Assume 1.5s GCD minimum
            dpm = damage / spell.mana_cost if spell.mana_cost > 0 else 0
            
            print(f"{spell.name:<20} {damage:<10} {spell.cast_time:<12} {spell.mana_cost:<8} {dps:<8.1f} {dpm:<8.2f}")
            
        except Exception as e:
            print(f"Error with {spell_data['name']}: {e}")


def demo_loading_from_file():
    """Demonstrate loading spells from JSON file."""
    print("\n=== Loading Spells from File Demo ===")
    
    try:
        # Load example spells
        with open('data/spell_examples.json', 'r') as f:
            spell_data = json.load(f)
        
        builder = SpellBuilder()
        validator = SpellValidator()
        
        print(f"Loaded {len(spell_data)} spells from file:")
        
        valid_spells = 0
        invalid_spells = 0
        
        for spell_name, spell_config in spell_data.items():
            try:
                # Validate first
                is_valid, errors, config = validator.validate_spell_from_dict(spell_config)
                
                if is_valid:
                    spell = builder.build_spell_from_dict(spell_config)
                    print(f"  ✓ {spell.name} - {spell.get_description()[:50]}...")
                    valid_spells += 1
                else:
                    print(f"  ✗ {spell_name} - Validation failed: {errors[0] if errors else 'Unknown error'}")
                    invalid_spells += 1
                    
            except Exception as e:
                print(f"  ✗ {spell_name} - Error: {e}")
                invalid_spells += 1
        
        print(f"\nResults: {valid_spells} valid, {invalid_spells} invalid")
        
    except FileNotFoundError:
        print("  Could not find data/spell_examples.json file")
    except Exception as e:
        print(f"  Error loading file: {e}")


def main():
    """Run all spell creation demos."""
    print("WoW Simulator - Spell Creation System Demo")
    print("=" * 60)
    print("This demo shows how to create custom spells using the new")
    print("user-friendly spell creation system.")
    print("=" * 60)
    
    demo_spell_templates()
    demo_spell_validation()
    demo_spell_creation()
    demo_spell_testing()
    demo_spell_comparison()
    demo_loading_from_file()
    
    print("\n" + "=" * 60)
    print("✅ Spell Creation System Demo Complete!")
    print("\nKey features demonstrated:")
    print("✓ Template library with common spell patterns")
    print("✓ Comprehensive validation with balance checking")
    print("✓ Easy spell creation from JSON configurations")
    print("✓ Real-time damage calculations and testing")
    print("✓ Spell comparison and analysis tools")
    print("✓ File-based spell loading and management")
    print("\nYou can now:")
    print("• Create custom spells using templates")
    print("• Validate spell balance automatically")
    print("• Test spells against different character builds")
    print("• Compare spell effectiveness")
    print("• Save and load spell libraries")


if __name__ == "__main__":
    main()
