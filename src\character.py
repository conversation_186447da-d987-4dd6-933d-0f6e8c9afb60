from typing import Dict, List, Optional
from src.core.effects import Buff, Debuff
from src.core.proc import Proc
from src.stats.standalone_stats import SimpleStatManager, StatType, StatModifier
import time


class Mo<PERSON>larCharacter:
    """
    Improved character class using the modular stats system.
    This demonstrates how the stats system integrates with existing code.
    """

    def __init__(self, name: str):
        self.name = name

        # Use the modular stats system
        self.stats = SimpleStatManager()

        # Set default character stats
        self.stats.set_stat(StatType.HEALTH, 1000)
        self.stats.set_stat(StatType.MANA, 1000)
        self.stats.set_stat(StatType.SPELL_POWER, 100)
        self.stats.set_stat(StatType.CRIT_CHANCE, 0.05)
        self.stats.set_stat(StatType.HASTE, 0.0)
        self.stats.set_stat(StatType.HIT_CHANCE, 0.83)
        self.stats.set_stat(StatType.ARMOR, 100)

        # Effects management
        self.buffs: Dict[str, Buff] = {}
        self.debuffs: Dict[str, Debuff] = {}
        self.procs: List[Proc] = []

        # Combat mechanics
        self.global_cooldown = 1.5
        self.last_cast_time = 0

        # Current resources (can be different from max)
        self.current_health = self.get_max_health()
        self.current_mana = self.get_max_mana()

    # Stat accessors using the modular system
    def get_max_health(self) -> float:
        """Get maximum health including all modifiers."""
        return self.stats.get_effective_stat(StatType.HEALTH)

    def get_max_mana(self) -> float:
        """Get maximum mana including all modifiers."""
        return self.stats.get_effective_stat(StatType.MANA)

    def get_spell_power(self) -> float:
        """Get spell power including all modifiers."""
        return self.stats.get_effective_stat(StatType.SPELL_POWER)

    def get_crit_chance(self) -> float:
        """Get crit chance including all modifiers."""
        return self.stats.get_effective_stat(StatType.CRIT_CHANCE)

    def get_haste(self) -> float:
        """Get haste including all modifiers."""
        return self.stats.get_effective_stat(StatType.HASTE)

    def get_hit_chance(self) -> float:
        """Get hit chance including all modifiers."""
        return self.stats.get_effective_stat(StatType.HIT_CHANCE)

    def get_armor(self) -> float:
        """Get armor including all modifiers."""
        return self.stats.get_effective_stat(StatType.ARMOR)

    def get_resistance(self, school: str) -> float:
        """Get resistance for a specific school."""
        resistance_map = {
            "fire": StatType.RESISTANCE_FIRE,
            "frost": StatType.RESISTANCE_FROST,
            "arcane": StatType.RESISTANCE_ARCANE,
            "shadow": StatType.RESISTANCE_SHADOW,
            "nature": StatType.RESISTANCE_NATURE,
            "holy": StatType.RESISTANCE_HOLY
        }
        stat_type = resistance_map.get(school.lower())
        if stat_type:
            return self.stats.get_effective_stat(stat_type)
        return 0.0

    # Equipment and modifier management
    def equip_item(self, item_name: str, stat_bonuses: Dict[StatType, float]) -> None:
        """Equip an item that provides stat bonuses."""
        self.stats.add_flat_bonus(f"equipment_{item_name}", stat_bonuses)

    def unequip_item(self, item_name: str) -> None:
        """Remove an equipped item."""
        self.stats.remove_modifier(f"equipment_{item_name}")

    def learn_talent(self, talent_name: str, stat_bonuses: Dict[StatType, float], is_percentage: bool = False) -> None:
        """Learn a talent that provides stat bonuses."""
        if is_percentage:
            self.stats.add_percentage_bonus(f"talent_{talent_name}", stat_bonuses)
        else:
            self.stats.add_flat_bonus(f"talent_{talent_name}", stat_bonuses)

    # Buff/Debuff system integration
    def apply_buff(self, buff: Buff) -> None:
        """Apply a buff and its stat modifiers."""
        buff.applied_at = time.time()
        self.buffs[buff.name] = buff

        # Convert old-style stat modifiers to new system
        if hasattr(buff, 'stat_modifiers') and buff.stat_modifiers:
            stat_bonuses = {}
            for stat_name, value in buff.stat_modifiers.items():
                # Convert string stat names to StatType enum
                stat_type = self._convert_stat_name(stat_name)
                if stat_type:
                    stat_bonuses[stat_type] = value

            if stat_bonuses:
                self.stats.add_flat_bonus(f"buff_{buff.name}", stat_bonuses)

    def remove_buff(self, buff_name: str) -> None:
        """Remove a buff and its stat modifiers."""
        if buff_name in self.buffs:
            del self.buffs[buff_name]
            self.stats.remove_modifier(f"buff_{buff_name}")

    def apply_debuff(self, debuff: Debuff) -> None:
        """Apply a debuff."""
        debuff.applied_at = time.time()
        self.debuffs[debuff.name] = debuff

    def remove_debuff(self, debuff_name: str) -> None:
        """Remove a debuff."""
        if debuff_name in self.debuffs:
            del self.debuffs[debuff_name]

    # Utility methods
    def _convert_stat_name(self, stat_name: str) -> Optional[StatType]:
        """Convert old string stat names to StatType enum."""
        conversion_map = {
            "spell_power": StatType.SPELL_POWER,
            "crit": StatType.CRIT_CHANCE,
            "crit_chance": StatType.CRIT_CHANCE,
            "health": StatType.HEALTH,
            "mana": StatType.MANA,
            "haste": StatType.HASTE,
            "hit": StatType.HIT_CHANCE,
            "hit_chance": StatType.HIT_CHANCE,
            "armor": StatType.ARMOR
        }
        return conversion_map.get(stat_name.lower())

    def get_stat_summary(self) -> Dict[str, float]:
        """Get a summary of all character stats."""
        return {
            "Health": self.get_max_health(),
            "Mana": self.get_max_mana(),
            "Spell Power": self.get_spell_power(),
            "Crit Chance": self.get_crit_chance() * 100,  # Convert to percentage
            "Haste": self.get_haste(),
            "Hit Chance": self.get_hit_chance() * 100,  # Convert to percentage
            "Armor": self.get_armor()
        }

    def update_effects(self, current_time: float) -> None:
        """Update all effects and remove expired ones."""
        # Remove expired buffs
        expired_buffs = []
        for buff_name, buff in self.buffs.items():
            if buff.is_expired(current_time):
                expired_buffs.append(buff_name)

        for buff_name in expired_buffs:
            self.remove_buff(buff_name)

        # Remove expired debuffs
        expired_debuffs = []
        for debuff_name, debuff in self.debuffs.items():
            if debuff.is_expired(current_time):
                expired_debuffs.append(debuff_name)

        for debuff_name in expired_debuffs:
            self.remove_debuff(debuff_name)


# Keep the original Character class for backward compatibility
class Character:
    """Original character class for backward compatibility."""

    def __init__(self, name: str):
        self.name = name
        self.health = 1000
        self.mana = 1000
        self.spell_power = 100
        self.crit_chance = 0.05

        self.buffs: Dict[str, Buff] = {}
        self.debuffs: Dict[str, Debuff] = {}
        self.procs: List[Proc] = []

        self.global_cooldown = 1.5
        self.last_cast_time = 0

    def apply_buff(self, buff: Buff):
        buff.applied_at = time.time()
        self.buffs[buff.name] = buff

    def get_effective_spell_power(self) -> int:
        total = self.spell_power
        for buff in self.buffs.values():
            total += buff.stat_modifiers.get("spell_power", 0)
        return total