"""
Combat Simulator - Simulates WoW-style combat with spell execution
"""

import time
import random
import threading
from typing import Dict, List, Callable
from .spell_execution_engine import SpellExecutionEngine, EventType

class CombatSimulator:
    def __init__(self):
        self.spell_engine = SpellExecutionEngine()
        self.is_running = False
        self.simulation_thread = None
        self.tick_rate = 0.1  # 100ms ticks
        self.combat_log = []
        self.callbacks = {
            'spell_cast': [],
            'damage_dealt': [],
            'healing_done': [],
            'combat_start': [],
            'combat_end': [],
            'state_update': []
        }
        
        # Combat parameters
        self.enemy_ai_enabled = True
        self.auto_rotation_enabled = True
        self.simulation_speed = 1.0
        
    def add_callback(self, event_type: str, callback: Callable):
        """Add a callback for combat events"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    def log_event(self, message: str, event_type: str = 'info'):
        """Log a combat event"""
        timestamp = time.time()
        log_entry = {
            'timestamp': timestamp,
            'message': message,
            'type': event_type
        }
        self.combat_log.append(log_entry)
        
        # Trigger callbacks
        for callback in self.callbacks.get('state_update', []):
            callback(log_entry)
    
    def register_spell(self, spell_id: str, spell_data: Dict, trigger_logic: Dict):
        """Register a spell with the execution engine"""
        self.spell_engine.register_spell(spell_id, spell_data, trigger_logic)
        self.log_event(f"Registered spell: {spell_data.get('name', spell_id)}")
    
    def start_combat(self, enemy_health: int = 100, player_health: int = 100, player_mana: int = 100):
        """Start combat simulation"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Initialize combat state
        self.spell_engine.update_combat_state(
            player_health=player_health,
            player_mana=player_mana,
            target_health=enemy_health,
            active_buffs={},
            active_debuffs={},
            cooldowns={}
        )
        
        self.spell_engine.start_combat()
        self.log_event("Combat started!", 'combat')
        
        # Trigger callbacks
        for callback in self.callbacks.get('combat_start', []):
            callback()
        
        # Start simulation thread
        self.simulation_thread = threading.Thread(target=self._simulation_loop)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()
    
    def stop_combat(self):
        """Stop combat simulation"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.spell_engine.end_combat()
        self.log_event("Combat ended!", 'combat')
        
        # Trigger callbacks
        for callback in self.callbacks.get('combat_end', []):
            callback()
        
        if self.simulation_thread:
            self.simulation_thread.join(timeout=1.0)
    
    def _simulation_loop(self):
        """Main simulation loop"""
        last_tick = time.time()
        
        while self.is_running:
            current_time = time.time()
            delta_time = (current_time - last_tick) * self.simulation_speed
            
            if delta_time >= self.tick_rate:
                self._process_tick(delta_time)
                last_tick = current_time
            
            time.sleep(0.01)  # Small sleep to prevent CPU spinning
    
    def _process_tick(self, delta_time: float):
        """Process a single simulation tick"""
        combat_state = self.spell_engine.combat_state
        
        # Check if combat should end
        if combat_state['target_health'] <= 0:
            self.log_event("Enemy defeated!", 'victory')
            self.stop_combat()
            return
        
        if combat_state['player_health'] <= 0:
            self.log_event("Player defeated!", 'defeat')
            self.stop_combat()
            return
        
        # Process auto rotation
        if self.auto_rotation_enabled:
            self.spell_engine.process_auto_rotation()
        
        # Simulate enemy AI
        if self.enemy_ai_enabled:
            self._process_enemy_ai(delta_time)
        
        # Regenerate mana
        self._process_mana_regeneration(delta_time)
        
        # Update cooldowns
        self._update_cooldowns(delta_time)
        
        # Trigger state update callbacks
        for callback in self.callbacks.get('state_update', []):
            callback(combat_state)
    
    def _process_enemy_ai(self, delta_time: float):
        """Simple enemy AI"""
        combat_state = self.spell_engine.combat_state
        
        # Enemy attacks every 2-3 seconds
        if random.random() < 0.02:  # 2% chance per tick at 100ms = ~2-3 seconds
            damage = random.randint(10, 20)
            combat_state['player_health'] = max(0, combat_state['player_health'] - damage)
            
            self.log_event(f"Enemy attacks for {damage} damage!", 'damage')
            
            # Fire take damage event
            self.spell_engine.fire_event(EventType.TAKE_DAMAGE, 'enemy', 'player', {
                'damage': damage
            })
            
            # Trigger callbacks
            for callback in self.callbacks.get('damage_dealt', []):
                callback('enemy', 'player', damage)
    
    def _process_mana_regeneration(self, delta_time: float):
        """Process mana regeneration"""
        combat_state = self.spell_engine.combat_state
        
        # Regenerate 1 mana per second
        mana_regen = delta_time * 10  # 1 mana per second at 100ms ticks
        combat_state['player_mana'] = min(100, combat_state['player_mana'] + mana_regen)
    
    def _update_cooldowns(self, delta_time: float):
        """Update spell cooldowns"""
        current_time = time.time()
        cooldowns = self.spell_engine.combat_state['cooldowns']
        
        # Remove expired cooldowns
        expired = [spell for spell, expire_time in cooldowns.items() if expire_time <= current_time]
        for spell in expired:
            del cooldowns[spell]
    
    def cast_spell_manually(self, spell_id: str) -> bool:
        """Manually cast a spell"""
        if not self.is_running:
            return False
        
        if spell_id not in self.spell_engine.spell_triggers:
            self.log_event(f"Unknown spell: {spell_id}", 'error')
            return False
        
        spell_info = self.spell_engine.spell_triggers[spell_id]
        spell_data = spell_info['spell_data']
        
        # Check if spell can be cast
        current_time = time.time()
        cooldown = spell_data.get('cooldown', 0)
        if current_time - spell_info['last_cast'] < cooldown:
            remaining = cooldown - (current_time - spell_info['last_cast'])
            self.log_event(f"{spell_data.get('name', spell_id)} on cooldown ({remaining:.1f}s remaining)", 'warning')
            return False
        
        # Check mana cost
        mana_cost = spell_data.get('mana_cost', 0)
        if self.spell_engine.combat_state['player_mana'] < mana_cost:
            self.log_event(f"Not enough mana for {spell_data.get('name', spell_id)}", 'warning')
            return False
        
        # Cast the spell
        success = self.spell_engine._execute_spell(spell_id)
        if success:
            # Deduct mana
            self.spell_engine.combat_state['player_mana'] -= mana_cost
            
            # Add cooldown
            if cooldown > 0:
                self.spell_engine.combat_state['cooldowns'][spell_data.get('name', spell_id)] = current_time + cooldown
            
            self.log_event(f"Cast {spell_data.get('name', spell_id)}!", 'spell')
            
            # Trigger callbacks
            for callback in self.callbacks.get('spell_cast', []):
                callback(spell_id, spell_data)
        
        return success
    
    def add_buff(self, buff_name: str, stacks: int = 1, duration: float = 30.0):
        """Add a buff to the player"""
        buffs = self.spell_engine.combat_state['active_buffs']
        buffs[buff_name] = stacks
        
        self.log_event(f"Gained buff: {buff_name} ({stacks} stacks)", 'buff')
        
        # Schedule buff removal
        def remove_buff():
            time.sleep(duration)
            if buff_name in buffs:
                del buffs[buff_name]
                self.log_event(f"Buff expired: {buff_name}", 'buff')
                self.spell_engine.fire_event(EventType.BUFF_EXPIRE, 'player', 'player', {
                    'buff_name': buff_name
                })
        
        buff_thread = threading.Thread(target=remove_buff)
        buff_thread.daemon = True
        buff_thread.start()
    
    def add_debuff(self, debuff_name: str, duration: float = 30.0):
        """Add a debuff to the target"""
        debuffs = self.spell_engine.combat_state['active_debuffs']
        debuffs[debuff_name] = time.time() + duration
        
        self.log_event(f"Applied debuff: {debuff_name}", 'debuff')
    
    def get_combat_state(self) -> Dict:
        """Get current combat state"""
        return self.spell_engine.combat_state.copy()
    
    def get_spell_statistics(self) -> Dict:
        """Get spell usage statistics"""
        return self.spell_engine.get_spell_statistics()
    
    def get_combat_log(self, last_n: int = None) -> List[Dict]:
        """Get combat log entries"""
        if last_n:
            return self.combat_log[-last_n:]
        return self.combat_log.copy()
    
    def clear_combat_log(self):
        """Clear the combat log"""
        self.combat_log.clear()
    
    def set_simulation_speed(self, speed: float):
        """Set simulation speed multiplier"""
        self.simulation_speed = max(0.1, min(10.0, speed))
        self.log_event(f"Simulation speed set to {self.simulation_speed}x")
    
    def toggle_auto_rotation(self, enabled: bool = None):
        """Toggle auto rotation on/off"""
        if enabled is None:
            self.auto_rotation_enabled = not self.auto_rotation_enabled
        else:
            self.auto_rotation_enabled = enabled
        
        status = "enabled" if self.auto_rotation_enabled else "disabled"
        self.log_event(f"Auto rotation {status}")
    
    def toggle_enemy_ai(self, enabled: bool = None):
        """Toggle enemy AI on/off"""
        if enabled is None:
            self.enemy_ai_enabled = not self.enemy_ai_enabled
        else:
            self.enemy_ai_enabled = enabled
        
        status = "enabled" if self.enemy_ai_enabled else "disabled"
        self.log_event(f"Enemy AI {status}")
