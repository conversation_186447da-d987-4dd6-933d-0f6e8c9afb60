"""
Spell Execution Engine - Handles trigger logic and spell execution during combat
"""

import time
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class TriggerType(Enum):
    MANUAL = "manual"
    AUTO_ROTATION = "auto_rotation"
    EVENT_TRIGGER = "event_trigger"
    CONDITION_TRIGGER = "condition_trigger"

class EventType(Enum):
    SPELL_CRIT = "spell_crit"
    TAKE_DAMAGE = "take_damage"
    KILL_ENEMY = "kill_enemy"
    BUFF_EXPIRE = "buff_expire"
    SPELL_CAST = "spell_cast"
    ENTER_COMBAT = "enter_combat"
    LEAVE_COMBAT = "leave_combat"

@dataclass
class CombatEvent:
    event_type: EventType
    source: str
    target: str
    data: Dict[str, Any]
    timestamp: float

@dataclass
class SpellTrigger:
    trigger_type: TriggerType
    priority: str = "medium"
    conditions: List[Dict] = None
    events: List[EventType] = None
    chance: float = 1.0
    logic_operator: str = "AND"
    
    def __post_init__(self):
        if self.conditions is None:
            self.conditions = []
        if self.events is None:
            self.events = []

class SpellExecutionEngine:
    def __init__(self):
        self.active_spells = {}
        self.spell_triggers = {}
        self.event_queue = []
        self.combat_state = {
            'in_combat': False,
            'combat_start_time': 0,
            'player_health': 100,
            'player_mana': 100,
            'target_health': 100,
            'active_buffs': {},
            'active_debuffs': {},
            'cooldowns': {},
            'distance_to_target': 10
        }
        self.rotation_queue = []
        self.event_listeners = {}
        
    def register_spell(self, spell_id: str, spell_data: Dict, trigger_logic: Dict):
        """Register a spell with its trigger logic"""
        trigger = self._parse_trigger_logic(trigger_logic)
        self.spell_triggers[spell_id] = {
            'spell_data': spell_data,
            'trigger': trigger,
            'last_cast': 0,
            'cast_count': 0
        }
        
        # Add to rotation queue if auto rotation
        if trigger.trigger_type == TriggerType.AUTO_ROTATION:
            self.rotation_queue.append({
                'spell_id': spell_id,
                'priority': trigger.priority,
                'conditions': trigger.conditions
            })
            # Sort by priority
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            self.rotation_queue.sort(key=lambda x: priority_order.get(x['priority'], 1))
        
        # Register event listeners
        if trigger.trigger_type == TriggerType.EVENT_TRIGGER:
            for event_type in trigger.events:
                if event_type not in self.event_listeners:
                    self.event_listeners[event_type] = []
                self.event_listeners[event_type].append(spell_id)
    
    def _parse_trigger_logic(self, trigger_logic: Dict) -> SpellTrigger:
        """Parse trigger logic from spell builder"""
        trigger_type = TriggerType(trigger_logic.get('type', 'manual'))
        
        trigger = SpellTrigger(
            trigger_type=trigger_type,
            priority=trigger_logic.get('priority', 'medium'),
            chance=trigger_logic.get('chance', 100) / 100.0
        )
        
        if trigger_type == TriggerType.EVENT_TRIGGER:
            trigger.events = [EventType(event) for event in trigger_logic.get('events', [])]
        
        elif trigger_type == TriggerType.CONDITION_TRIGGER:
            trigger.conditions = trigger_logic.get('conditions', [])
            trigger.logic_operator = trigger_logic.get('logic', 'AND')
        
        elif trigger_type == TriggerType.AUTO_ROTATION:
            # Convert rotation condition to trigger conditions
            condition = trigger_logic.get('condition', 'always')
            trigger.conditions = self._convert_rotation_condition(condition)
        
        return trigger
    
    def _convert_rotation_condition(self, condition: str) -> List[Dict]:
        """Convert rotation condition to trigger conditions"""
        condition_map = {
            'always': [],
            'target_low_health': [{'type': 'health', 'target': 'target', 'operator': '<', 'value': 35}],
            'high_mana': [{'type': 'mana', 'operator': '>', 'value': 80}],
            'low_mana': [{'type': 'mana', 'operator': '<', 'value': 20}],
            'buff_active': [{'type': 'buff', 'name': 'any', 'stacks': 1}],
            'cooldowns_ready': [{'type': 'cooldown', 'name': 'major', 'status': 'ready'}]
        }
        return condition_map.get(condition, [])
    
    def fire_event(self, event_type: EventType, source: str, target: str, data: Dict = None):
        """Fire a combat event and check for triggered spells"""
        if data is None:
            data = {}
            
        event = CombatEvent(
            event_type=event_type,
            source=source,
            target=target,
            data=data,
            timestamp=time.time()
        )
        
        self.event_queue.append(event)
        
        # Check event-triggered spells
        if event_type in self.event_listeners:
            for spell_id in self.event_listeners[event_type]:
                self._try_trigger_spell(spell_id, event)
    
    def _try_trigger_spell(self, spell_id: str, event: CombatEvent = None):
        """Try to trigger a spell based on its conditions"""
        if spell_id not in self.spell_triggers:
            return False
        
        spell_info = self.spell_triggers[spell_id]
        trigger = spell_info['trigger']
        spell_data = spell_info['spell_data']
        
        # Check cooldown
        current_time = time.time()
        cooldown = spell_data.get('cooldown', 0)
        if current_time - spell_info['last_cast'] < cooldown:
            return False
        
        # Check trigger type specific conditions
        if trigger.trigger_type == TriggerType.EVENT_TRIGGER:
            if event and event.event_type in trigger.events:
                if random.random() <= trigger.chance:
                    return self._execute_spell(spell_id, event)
        
        elif trigger.trigger_type == TriggerType.CONDITION_TRIGGER:
            if self._check_conditions(trigger.conditions, trigger.logic_operator):
                return self._execute_spell(spell_id)
        
        elif trigger.trigger_type == TriggerType.AUTO_ROTATION:
            if self._check_conditions(trigger.conditions, 'AND'):
                return self._execute_spell(spell_id)
        
        return False
    
    def _check_conditions(self, conditions: List[Dict], logic_operator: str) -> bool:
        """Check if conditions are met"""
        if not conditions:
            return True
        
        results = []
        for condition in conditions:
            result = self._evaluate_condition(condition)
            results.append(result)
        
        if logic_operator == 'AND':
            return all(results)
        else:  # OR
            return any(results)
    
    def _evaluate_condition(self, condition: Dict) -> bool:
        """Evaluate a single condition"""
        condition_type = condition.get('type')
        
        if condition_type == 'health':
            target = condition.get('target', 'target')
            operator = condition.get('operator', '<')
            value = condition.get('value', 35)
            
            if target == 'self':
                current_health = self.combat_state['player_health']
            else:
                current_health = self.combat_state['target_health']
            
            return self._compare_values(current_health, operator, value)
        
        elif condition_type == 'mana':
            operator = condition.get('operator', '>')
            value = condition.get('value', 80)
            current_mana = self.combat_state['player_mana']
            return self._compare_values(current_mana, operator, value)
        
        elif condition_type == 'buff':
            buff_name = condition.get('name', '')
            min_stacks = condition.get('stacks', 1)
            active_buffs = self.combat_state['active_buffs']
            
            if buff_name == 'any':
                return len(active_buffs) > 0
            else:
                return active_buffs.get(buff_name, 0) >= min_stacks
        
        elif condition_type == 'debuff':
            debuff_name = condition.get('name', '')
            active_debuffs = self.combat_state['active_debuffs']
            return debuff_name in active_debuffs
        
        elif condition_type == 'cooldown':
            spell_name = condition.get('name', '')
            status = condition.get('status', 'ready')
            cooldowns = self.combat_state['cooldowns']
            
            if status == 'ready':
                return cooldowns.get(spell_name, 0) <= time.time()
            else:  # active
                return cooldowns.get(spell_name, 0) > time.time()
        
        elif condition_type == 'distance':
            operator = condition.get('operator', '<')
            value = condition.get('value', 10)
            current_distance = self.combat_state['distance_to_target']
            return self._compare_values(current_distance, operator, value)
        
        elif condition_type == 'time':
            operator = condition.get('operator', '>')
            value = condition.get('value', 10)
            combat_time = time.time() - self.combat_state['combat_start_time']
            return self._compare_values(combat_time, operator, value)
        
        return False
    
    def _compare_values(self, current: float, operator: str, target: float) -> bool:
        """Compare two values with an operator"""
        if operator == '<':
            return current < target
        elif operator == '>':
            return current > target
        elif operator == '<=':
            return current <= target
        elif operator == '>=':
            return current >= target
        elif operator == '==':
            return abs(current - target) < 0.01
        return False
    
    def _execute_spell(self, spell_id: str, event: CombatEvent = None) -> bool:
        """Execute a spell"""
        if spell_id not in self.spell_triggers:
            return False
        
        spell_info = self.spell_triggers[spell_id]
        spell_data = spell_info['spell_data']
        
        # Update last cast time
        spell_info['last_cast'] = time.time()
        spell_info['cast_count'] += 1
        
        # Apply spell effects
        self._apply_spell_effects(spell_data, event)
        
        # Fire spell cast event
        self.fire_event(EventType.SPELL_CAST, 'player', 'target', {
            'spell_id': spell_id,
            'spell_name': spell_data.get('name', 'Unknown'),
            'damage': spell_data.get('base_damage', 0)
        })
        
        return True
    
    def _apply_spell_effects(self, spell_data: Dict, event: CombatEvent = None):
        """Apply the effects of a spell"""
        # This would contain the actual spell effect logic
        # For now, just simulate basic damage/healing
        
        spell_type = spell_data.get('target_type', 'single_enemy')
        base_damage = spell_data.get('base_damage', 0)
        
        if 'enemy' in spell_type and base_damage > 0:
            # Deal damage to target
            damage = base_damage
            self.combat_state['target_health'] = max(0, self.combat_state['target_health'] - damage)
            
            # Check for critical hit
            if random.random() < 0.15:  # 15% crit chance
                damage *= 2
                self.fire_event(EventType.SPELL_CRIT, 'player', 'target', {'damage': damage})
            
            # Check if target dies
            if self.combat_state['target_health'] <= 0:
                self.fire_event(EventType.KILL_ENEMY, 'player', 'target', {})
        
        elif 'ally' in spell_type and base_damage > 0:
            # Heal player
            healing = base_damage
            self.combat_state['player_health'] = min(100, self.combat_state['player_health'] + healing)
    
    def process_auto_rotation(self):
        """Process auto rotation spells"""
        if not self.combat_state['in_combat']:
            return
        
        for rotation_spell in self.rotation_queue:
            spell_id = rotation_spell['spell_id']
            if self._try_trigger_spell(spell_id):
                break  # Only cast one spell per rotation tick
    
    def update_combat_state(self, **kwargs):
        """Update combat state"""
        self.combat_state.update(kwargs)
    
    def start_combat(self):
        """Start combat"""
        self.combat_state['in_combat'] = True
        self.combat_state['combat_start_time'] = time.time()
        self.fire_event(EventType.ENTER_COMBAT, 'player', 'target', {})
    
    def end_combat(self):
        """End combat"""
        self.combat_state['in_combat'] = False
        self.fire_event(EventType.LEAVE_COMBAT, 'player', 'target', {})
    
    def get_spell_statistics(self) -> Dict:
        """Get statistics about spell usage"""
        stats = {}
        for spell_id, spell_info in self.spell_triggers.items():
            stats[spell_id] = {
                'cast_count': spell_info['cast_count'],
                'last_cast': spell_info['last_cast'],
                'trigger_type': spell_info['trigger'].trigger_type.value
            }
        return stats
