from dataclasses import dataclass
from typing import Dict, Any, Optional
import time


@dataclass
class Effect:
    """Base class for all effects (buffs, debuffs, etc.)."""
    name: str
    duration: float
    stacks: int = 1
    max_stacks: int = 1
    applied_at: float = 0

    def is_expired(self, current_time: float) -> bool:
        """Check if this effect has expired."""
        if self.duration <= 0:  # Permanent effects
            return False
        return current_time >= self.applied_at + self.duration

    def get_remaining_time(self, current_time: float) -> float:
        """Get remaining time on this effect."""
        if self.duration <= 0:  # Permanent effects
            return float('inf')
        remaining = (self.applied_at + self.duration) - current_time
        return max(0.0, remaining)

    def refresh(self, current_time: float) -> None:
        """Refresh the effect duration."""
        self.applied_at = current_time

    def add_stack(self) -> bool:
        """Add a stack to this effect. Returns True if successful."""
        if self.stacks < self.max_stacks:
            self.stacks += 1
            return True
        return False

    def remove_stack(self) -> bool:
        """Remove a stack. Returns True if effect should be removed."""
        self.stacks -= 1
        return self.stacks <= 0


class Buff(Effect):
    """Buff effect that provides stat bonuses."""

    def __init__(self, name: str, duration: float, stat_modifiers: Dict[str, float],
                 max_stacks: int = 1, stackable: bool = False):
        super().__init__(name, duration, max_stacks=max_stacks)
        self.stat_modifiers = stat_modifiers.copy()  # e.g., {"spell_power": 50, "crit": 0.1}
        self.stackable = stackable

    def get_effective_modifiers(self) -> Dict[str, float]:
        """Get stat modifiers accounting for stacks."""
        if not self.stackable:
            return self.stat_modifiers.copy()

        # Apply stacking
        effective_modifiers = {}
        for stat, value in self.stat_modifiers.items():
            effective_modifiers[stat] = value * self.stacks
        return effective_modifiers

    def merge_with(self, other_buff: 'Buff') -> bool:
        """
        Merge with another buff of the same name.
        Returns True if merge was successful.
        """
        if self.name != other_buff.name:
            return False

        if self.stackable and self.stacks < self.max_stacks:
            # Add stacks
            stacks_to_add = min(other_buff.stacks, self.max_stacks - self.stacks)
            self.stacks += stacks_to_add
            # Refresh duration
            self.applied_at = other_buff.applied_at
            return True
        else:
            # Refresh duration for non-stackable buffs
            self.applied_at = other_buff.applied_at
            return True


class Debuff(Effect):
    """Debuff effect that can deal damage over time."""

    def __init__(self, name: str, duration: float, damage_per_tick: int = 0,
                 tick_interval: float = 3.0, max_stacks: int = 1):
        super().__init__(name, duration, max_stacks=max_stacks)
        self.damage_per_tick = damage_per_tick
        self.tick_interval = tick_interval
        self.last_tick = 0
        self.total_damage_dealt = 0

    def should_tick(self, current_time: float) -> bool:
        """Check if this debuff should tick now."""
        if self.damage_per_tick <= 0:
            return False
        return current_time >= self.last_tick + self.tick_interval

    def tick(self, current_time: float) -> int:
        """
        Process a tick of this debuff.
        Returns the damage dealt.
        """
        if not self.should_tick(current_time):
            return 0

        damage = self.damage_per_tick * self.stacks
        self.last_tick = current_time
        self.total_damage_dealt += damage
        return damage

    def get_total_damage(self) -> int:
        """Get total damage dealt by this debuff."""
        return self.total_damage_dealt


class EffectManager:
    """Manages effects on a character."""

    def __init__(self):
        self.active_effects: Dict[str, Effect] = {}

    def add_effect(self, effect: Effect) -> None:
        """Add an effect, handling stacking and refreshing."""
        if effect.name in self.active_effects:
            existing = self.active_effects[effect.name]
            if isinstance(existing, Buff) and isinstance(effect, Buff):
                if not existing.merge_with(effect):
                    # Replace if merge failed
                    self.active_effects[effect.name] = effect
            else:
                # Replace non-buff effects
                self.active_effects[effect.name] = effect
        else:
            self.active_effects[effect.name] = effect

    def remove_effect(self, effect_name: str) -> Optional[Effect]:
        """Remove an effect by name."""
        return self.active_effects.pop(effect_name, None)

    def get_effect(self, effect_name: str) -> Optional[Effect]:
        """Get an effect by name."""
        return self.active_effects.get(effect_name)

    def get_all_effects(self) -> Dict[str, Effect]:
        """Get all active effects."""
        return self.active_effects.copy()

    def update_effects(self, current_time: float) -> Dict[str, int]:
        """
        Update all effects, removing expired ones and processing ticks.
        Returns a dict of debuff damage dealt.
        """
        expired_effects = []
        damage_dealt = {}

        for name, effect in self.active_effects.items():
            # Check for expiration
            if effect.is_expired(current_time):
                expired_effects.append(name)
                continue

            # Process debuff ticks
            if isinstance(effect, Debuff):
                tick_damage = effect.tick(current_time)
                if tick_damage > 0:
                    damage_dealt[name] = tick_damage

        # Remove expired effects
        for name in expired_effects:
            self.remove_effect(name)

        return damage_dealt

    def clear_all_effects(self) -> None:
        """Remove all effects."""
        self.active_effects.clear()

    def get_stat_modifiers(self) -> Dict[str, float]:
        """Get combined stat modifiers from all active buffs."""
        combined_modifiers = {}

        for effect in self.active_effects.values():
            if isinstance(effect, Buff):
                modifiers = effect.get_effective_modifiers()
                for stat, value in modifiers.items():
                    combined_modifiers[stat] = combined_modifiers.get(stat, 0) + value

        return combined_modifiers