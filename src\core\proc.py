from dataclasses import dataclass
from typing import Callable, Any, Optional, Dict, List
from enum import Enum
import random
import time


class TriggerCondition(Enum):
    """Enumeration of proc trigger conditions."""
    ON_SPELL_CAST = "on_spell_cast"
    ON_CRIT = "on_crit"
    ON_DAMAGE_TAKEN = "on_damage_taken"
    ON_DAMAGE_DEALT = "on_damage_dealt"
    ON_HEAL_CAST = "on_heal_cast"
    ON_BUFF_APPLIED = "on_buff_applied"
    ON_DEBUFF_APPLIED = "on_debuff_applied"
    ON_MANA_SPENT = "on_mana_spent"
    ON_KILL = "on_kill"
    ON_LOW_HEALTH = "on_low_health"


@dataclass
class ProcEvent:
    """Data about an event that might trigger a proc."""
    event_type: TriggerCondition
    source: Any  # Character that caused the event
    target: Optional[Any] = None  # Target of the event
    spell: Optional[Any] = None  # Spell involved
    damage: int = 0
    healing: int = 0
    was_critical: bool = False
    timestamp: float = 0

    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


@dataclass
class Proc:
    """
    Represents a proc effect that can trigger based on certain conditions.
    """
    name: str
    chance: float  # 0.0 to 1.0
    trigger_condition: TriggerCondition
    effect: Callable[[ProcEvent], Any]  # Function to execute when proc triggers
    cooldown: float = 0
    last_proc_time: float = 0
    max_procs_per_minute: int = 0  # Rate limiting (0 = no limit)
    proc_count_this_minute: int = 0
    last_minute_reset: float = 0
    enabled: bool = True

    def can_proc(self, current_time: float) -> bool:
        """Check if this proc can trigger now."""
        if not self.enabled:
            return False

        # Check cooldown
        if current_time < self.last_proc_time + self.cooldown:
            return False

        # Check rate limiting
        if self.max_procs_per_minute > 0:
            # Reset counter if a minute has passed
            if current_time >= self.last_minute_reset + 60:
                self.proc_count_this_minute = 0
                self.last_minute_reset = current_time

            if self.proc_count_this_minute >= self.max_procs_per_minute:
                return False

        return True

    def try_proc(self, event: ProcEvent) -> bool:
        """
        Try to trigger this proc based on an event.
        Returns True if the proc triggered.
        """
        # Check if this proc responds to this event type
        if event.event_type != self.trigger_condition:
            return False

        # Check if proc can trigger
        if not self.can_proc(event.timestamp):
            return False

        # Roll for proc chance
        if random.random() >= self.chance:
            return False

        # Proc triggered!
        self.last_proc_time = event.timestamp
        if self.max_procs_per_minute > 0:
            self.proc_count_this_minute += 1

        # Execute the proc effect
        try:
            self.effect(event)
            return True
        except Exception as e:
            print(f"Error executing proc {self.name}: {e}")
            return False

    def reset_cooldown(self) -> None:
        """Reset the proc cooldown."""
        self.last_proc_time = 0

    def enable(self) -> None:
        """Enable this proc."""
        self.enabled = True

    def disable(self) -> None:
        """Disable this proc."""
        self.enabled = False

    def get_remaining_cooldown(self, current_time: float) -> float:
        """Get remaining cooldown time."""
        remaining = (self.last_proc_time + self.cooldown) - current_time
        return max(0.0, remaining)


class ProcManager:
    """Manages procs for a character."""

    def __init__(self):
        self.procs: Dict[str, Proc] = {}
        self.proc_history: List[Dict[str, Any]] = []

    def add_proc(self, proc: Proc) -> None:
        """Add a proc to the manager."""
        self.procs[proc.name] = proc

    def remove_proc(self, proc_name: str) -> Optional[Proc]:
        """Remove a proc by name."""
        return self.procs.pop(proc_name, None)

    def get_proc(self, proc_name: str) -> Optional[Proc]:
        """Get a proc by name."""
        return self.procs.get(proc_name)

    def process_event(self, event: ProcEvent) -> List[str]:
        """
        Process an event and trigger any applicable procs.
        Returns a list of proc names that triggered.
        """
        triggered_procs = []

        for proc_name, proc in self.procs.items():
            if proc.try_proc(event):
                triggered_procs.append(proc_name)

                # Log the proc trigger
                self.proc_history.append({
                    'proc_name': proc_name,
                    'event_type': event.event_type.value,
                    'timestamp': event.timestamp,
                    'source': getattr(event.source, 'name', str(event.source)),
                    'target': getattr(event.target, 'name', str(event.target)) if event.target else None
                })

        return triggered_procs

    def get_all_procs(self) -> Dict[str, Proc]:
        """Get all procs."""
        return self.procs.copy()

    def clear_all_procs(self) -> None:
        """Remove all procs."""
        self.procs.clear()

    def get_proc_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent proc history."""
        return self.proc_history[-limit:] if limit > 0 else self.proc_history.copy()

    def clear_history(self) -> None:
        """Clear proc history."""
        self.proc_history.clear()

    def get_proc_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics about proc triggers."""
        stats = {}

        for proc_name, proc in self.procs.items():
            proc_triggers = [entry for entry in self.proc_history if entry['proc_name'] == proc_name]

            stats[proc_name] = {
                'total_triggers': len(proc_triggers),
                'chance': proc.chance,
                'cooldown': proc.cooldown,
                'enabled': proc.enabled,
                'last_trigger': proc_triggers[-1]['timestamp'] if proc_triggers else None,
                'triggers_per_minute': proc.proc_count_this_minute if proc.max_procs_per_minute > 0 else None
            }

        return stats