from dataclasses import dataclass
from enum import Enum
import random

class School(Enum):
    FIRE = "fire"
    FROST = "frost"
    ARCANE = "arcane"
    SHADOW = "shadow"
    NATURE = "nature"
    HOLY = "holy"
    PHYSICAL = "physical"

@dataclass
class Spell:
    name: str
    school: School
    base_damage: int
    cast_time: float  # seconds
    cooldown: float   # seconds
    mana_cost: int
    coefficient: float  # spell power scaling
    crit_chance: float = 0.05
    
    def calculate_damage(self, spell_power: int, crit_modifier: float = 2.0):
        damage = self.base_damage + (spell_power * self.coefficient)
        if random.random() < self.crit_chance:
            damage *= crit_modifier
        return int(damage)