from typing import List, Dict, Any
from src.core.effects import Buff, Debuff


class DamageCalculator:
    """
    Damage calculation system that works with the effects system.
    """

    def __init__(self):
        self.total_damage = 0
        self.combat_log = []

    def calculate_damage(self, base_damage: float, buffs: List[Buff], debuffs: List[Debuff] = None) -> int:
        """
        Calculate damage with buff/debuff modifiers.

        Args:
            base_damage: Base damage before modifiers
            buffs: List of active buffs
            debuffs: List of active debuffs (optional)

        Returns:
            Final damage after all modifiers
        """
        if debuffs is None:
            debuffs = []

        # Start with base damage
        damage = base_damage

        # Apply buff modifiers
        damage_multiplier = 1.0
        flat_bonus = 0.0

        for buff in buffs:
            if hasattr(buff, 'stat_modifiers') and buff.stat_modifiers:
                # Check for damage-related modifiers
                if 'damage_multiplier' in buff.stat_modifiers:
                    damage_multiplier *= buff.stat_modifiers['damage_multiplier']
                if 'damage_bonus' in buff.stat_modifiers:
                    flat_bonus += buff.stat_modifiers['damage_bonus']
                if 'spell_power' in buff.stat_modifiers:
                    # Assume spell power adds to damage (simplified)
                    flat_bonus += buff.stat_modifiers['spell_power'] * 0.5

        # Apply debuff effects (damage reduction, etc.)
        for debuff in debuffs:
            if hasattr(debuff, 'damage_reduction'):
                damage_multiplier *= (1.0 - debuff.damage_reduction)

        # Calculate final damage
        final_damage = (damage + flat_bonus) * damage_multiplier

        return int(max(0, final_damage))

    def simulate_rotation(self, spells: List[Any], duration: float) -> Dict[str, Any]:
        """
        Simulate a spell rotation for maximum DPS.

        Args:
            spells: List of spells to use in rotation
            duration: Duration of the simulation in seconds

        Returns:
            Dictionary with simulation results
        """
        total_damage = 0
        total_time = 0
        cast_count = 0

        # Simple rotation simulation
        spell_index = 0
        while total_time < duration and spells:
            spell = spells[spell_index % len(spells)]

            # Simulate casting the spell
            if hasattr(spell, 'cast_time'):
                cast_time = spell.cast_time
            else:
                cast_time = 2.5  # Default cast time

            if hasattr(spell, 'base_damage'):
                spell_damage = spell.base_damage
            else:
                spell_damage = 100  # Default damage

            total_damage += spell_damage
            total_time += cast_time
            cast_count += 1
            spell_index += 1

        dps = total_damage / total_time if total_time > 0 else 0

        return {
            'total_damage': total_damage,
            'total_time': total_time,
            'cast_count': cast_count,
            'dps': dps,
            'average_damage_per_cast': total_damage / cast_count if cast_count > 0 else 0
        }

    def add_to_log(self, message: str) -> None:
        """Add a message to the combat log."""
        self.combat_log.append(message)

    def get_log(self) -> List[str]:
        """Get the combat log."""
        return self.combat_log.copy()

    def clear_log(self) -> None:
        """Clear the combat log."""
        self.combat_log.clear()

    def reset(self) -> None:
        """Reset the damage calculator."""
        self.total_damage = 0
        self.clear_log()