"""
Core interfaces for the WoW Simulator.

This module provides all the interfaces and abstract base classes that define
the contracts for the modular WoW Simulator architecture.
"""

# Base interfaces
from .base import (
    StatType,
    IStatContainer,
    IStatModifier,
    IEffect,
    ISpell,
    ICastResult,
    ICharacter,
    IEventListener,
    IEvent,
    IEventBus,
    IProc
)

# Effect interfaces
from .effects import (
    IBuff,
    IDebuff,
    IAura,
    IChanneledEffect,
    IEffectManager,
    IEffectFactory
)

# Spell interfaces
from .spells import (
    SpellSchool,
    SpellType,
    IDamageSpell,
    IHealSpell,
    IBuffSpell,
    IDebuffSpell,
    IChanneledSpell,
    IInstantSpell,
    ISpellBuilder,
    ISpellFactory,
    ISpellbook
)

# Event interfaces
from .events import (
    EventType,
    ISpellCastEvent,
    IDamageEvent,
    IHealEvent,
    IEffectEvent,
    IProcEvent,
    IStatChangeEvent,
    IEventFilter,
    IEventHandler,
    IEventFactory,
    IAdvancedEventBus
)

__all__ = [
    # Base interfaces
    'StatType',
    'IStatContainer',
    'IStatModifier',
    'IEffect',
    'ISpell',
    'ICastResult',
    'ICharacter',
    'IEventListener',
    'IEvent',
    'IEventBus',
    'IProc',

    # Effect interfaces
    'IBuff',
    'IDebuff',
    'IAura',
    'IChanneledEffect',
    'IEffectManager',
    'IEffectFactory',

    # Spell interfaces
    'SpellSchool',
    'SpellType',
    'IDamageSpell',
    'IHealSpell',
    'IBuffSpell',
    'IDebuffSpell',
    'IChanneledSpell',
    'IInstantSpell',
    'ISpellBuilder',
    'ISpellFactory',
    'ISpellbook',

    # Event interfaces
    'EventType',
    'ISpellCastEvent',
    'IDamageEvent',
    'IHealEvent',
    'IEffectEvent',
    'IProcEvent',
    'IStatChangeEvent',
    'IEventFilter',
    'IEventHandler',
    'IEventFactory',
    'IAdvancedEventBus'
]
