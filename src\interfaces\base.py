"""
Core interfaces and abstract base classes for the WoW Simulator.
These interfaces define contracts that enable loose coupling and easy extensibility.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Protocol
from enum import Enum


class StatType(Enum):
    """Enumeration of all possible character stats."""
    HEALTH = "health"
    MANA = "mana"
    SPELL_POWER = "spell_power"
    CRIT_CHANCE = "crit_chance"
    HASTE = "haste"
    HIT_CHANCE = "hit_chance"
    ARMOR = "armor"
    RESISTANCE_FIRE = "resistance_fire"
    RESISTANCE_FROST = "resistance_frost"
    RESISTANCE_ARCANE = "resistance_arcane"
    RESISTANCE_SHADOW = "resistance_shadow"
    RESISTANCE_NATURE = "resistance_nature"
    RESISTANCE_HOLY = "resistance_holy"


class IStatContainer(Protocol):
    """Interface for objects that can hold and modify stats."""
    
    def get_stat(self, stat_type: StatType) -> float:
        """Get the current value of a stat."""
        ...
    
    def set_stat(self, stat_type: StatType, value: float) -> None:
        """Set the base value of a stat."""
        ...
    
    def get_effective_stat(self, stat_type: StatType) -> float:
        """Get the effective value of a stat including all modifiers."""
        ...


class IStatModifier(Protocol):
    """Interface for objects that can modify stats."""
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get all stat modifications this object provides."""
        ...
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        ...


class IEffect(ABC):
    """Abstract base class for all effects (buffs, debuffs, etc.)."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """The name of this effect."""
        pass
    
    @property
    @abstractmethod
    def duration(self) -> float:
        """Duration of the effect in seconds."""
        pass
    
    @abstractmethod
    def is_expired(self, current_time: float) -> bool:
        """Check if this effect has expired."""
        pass
    
    @abstractmethod
    def apply(self, target: 'ICharacter') -> None:
        """Apply this effect to a target."""
        pass
    
    @abstractmethod
    def remove(self, target: 'ICharacter') -> None:
        """Remove this effect from a target."""
        pass
    
    @abstractmethod
    def tick(self, target: 'ICharacter', current_time: float) -> None:
        """Process a tick of this effect (for DoTs, etc.)."""
        pass


class ISpell(ABC):
    """Abstract base class for all spells."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """The name of this spell."""
        pass
    
    @property
    @abstractmethod
    def cast_time(self) -> float:
        """Cast time in seconds."""
        pass
    
    @property
    @abstractmethod
    def cooldown(self) -> float:
        """Cooldown in seconds."""
        pass
    
    @property
    @abstractmethod
    def mana_cost(self) -> int:
        """Mana cost to cast this spell."""
        pass
    
    @abstractmethod
    def can_cast(self, caster: 'ICharacter', target: Optional['ICharacter'] = None) -> bool:
        """Check if this spell can be cast by the caster on the target."""
        pass
    
    @abstractmethod
    def cast(self, caster: 'ICharacter', target: Optional['ICharacter'] = None) -> 'ICastResult':
        """Cast this spell."""
        pass


class ICastResult(ABC):
    """Result of casting a spell."""
    
    @property
    @abstractmethod
    def success(self) -> bool:
        """Whether the cast was successful."""
        pass
    
    @property
    @abstractmethod
    def damage_dealt(self) -> int:
        """Damage dealt by this cast."""
        pass
    
    @property
    @abstractmethod
    def effects_applied(self) -> List[IEffect]:
        """Effects applied by this cast."""
        pass
    
    @property
    @abstractmethod
    def was_critical(self) -> bool:
        """Whether this cast was a critical hit."""
        pass


class ICharacter(ABC):
    """Abstract base class for all characters."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """The character's name."""
        pass
    
    @abstractmethod
    def get_stat(self, stat_type: StatType) -> float:
        """Get a character stat."""
        pass
    
    @abstractmethod
    def get_effective_stat(self, stat_type: StatType) -> float:
        """Get effective stat value including all modifiers."""
        pass
    
    @abstractmethod
    def apply_effect(self, effect: IEffect) -> None:
        """Apply an effect to this character."""
        pass
    
    @abstractmethod
    def remove_effect(self, effect_name: str) -> None:
        """Remove an effect from this character."""
        pass
    
    @abstractmethod
    def cast_spell(self, spell: ISpell, target: Optional['ICharacter'] = None) -> ICastResult:
        """Cast a spell."""
        pass
    
    @abstractmethod
    def can_cast_spell(self, spell: ISpell) -> bool:
        """Check if this character can cast the given spell."""
        pass


class IEventListener(Protocol):
    """Interface for objects that can listen to events."""
    
    def handle_event(self, event: 'IEvent') -> None:
        """Handle an event."""
        ...


class IEvent(ABC):
    """Abstract base class for all events."""
    
    @property
    @abstractmethod
    def event_type(self) -> str:
        """The type of this event."""
        pass
    
    @property
    @abstractmethod
    def timestamp(self) -> float:
        """When this event occurred."""
        pass
    
    @property
    @abstractmethod
    def source(self) -> Optional[ICharacter]:
        """The character that caused this event."""
        pass
    
    @property
    @abstractmethod
    def target(self) -> Optional[ICharacter]:
        """The character that was affected by this event."""
        pass


class IEventBus(ABC):
    """Abstract base class for event bus implementations."""
    
    @abstractmethod
    def subscribe(self, event_type: str, listener: IEventListener) -> None:
        """Subscribe to events of a specific type."""
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: str, listener: IEventListener) -> None:
        """Unsubscribe from events of a specific type."""
        pass
    
    @abstractmethod
    def publish(self, event: IEvent) -> None:
        """Publish an event to all subscribers."""
        pass


class IProc(ABC):
    """Abstract base class for proc effects."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """The name of this proc."""
        pass
    
    @property
    @abstractmethod
    def chance(self) -> float:
        """The chance for this proc to trigger (0.0 to 1.0)."""
        pass
    
    @abstractmethod
    def can_proc(self, event: IEvent) -> bool:
        """Check if this proc can trigger from the given event."""
        pass
    
    @abstractmethod
    def try_proc(self, event: IEvent) -> bool:
        """Attempt to trigger this proc from the given event."""
        pass
    
    @abstractmethod
    def execute(self, event: IEvent) -> None:
        """Execute the proc effect."""
        pass
