"""
Interfaces for different types of effects in the WoW Simulator.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional
from .base import IEffect, ICharacter, StatType


class IBuff(IEffect):
    """Interface for buff effects."""
    
    @property
    @abstractmethod
    def stat_modifiers(self) -> Dict[StatType, float]:
        """Stat modifications provided by this buff."""
        pass
    
    @property
    @abstractmethod
    def stacks(self) -> int:
        """Current number of stacks."""
        pass
    
    @property
    @abstractmethod
    def max_stacks(self) -> int:
        """Maximum number of stacks allowed."""
        pass
    
    @abstractmethod
    def add_stack(self) -> bool:
        """Add a stack to this buff. Returns True if successful."""
        pass
    
    @abstractmethod
    def remove_stack(self) -> bool:
        """Remove a stack from this buff. Returns True if buff should be removed."""
        pass


class IDebuff(IEffect):
    """Interface for debuff effects."""
    
    @property
    @abstractmethod
    def damage_per_tick(self) -> int:
        """Damage dealt per tick."""
        pass
    
    @property
    @abstractmethod
    def tick_interval(self) -> float:
        """Time between ticks in seconds."""
        pass
    
    @abstractmethod
    def should_tick(self, current_time: float) -> bool:
        """Check if this debuff should tick now."""
        pass


class IAura(IEffect):
    """Interface for aura effects that affect nearby characters."""
    
    @property
    @abstractmethod
    def radius(self) -> float:
        """Radius of the aura effect."""
        pass
    
    @property
    @abstractmethod
    def affects_allies(self) -> bool:
        """Whether this aura affects allied characters."""
        pass
    
    @property
    @abstractmethod
    def affects_enemies(self) -> bool:
        """Whether this aura affects enemy characters."""
        pass
    
    @abstractmethod
    def get_affected_characters(self, source: ICharacter, all_characters: List[ICharacter]) -> List[ICharacter]:
        """Get all characters affected by this aura."""
        pass


class IChanneledEffect(IEffect):
    """Interface for channeled effects that require continuous casting."""
    
    @property
    @abstractmethod
    def channel_time(self) -> float:
        """Total channel time in seconds."""
        pass
    
    @property
    @abstractmethod
    def tick_interval(self) -> float:
        """Time between channel ticks."""
        pass
    
    @abstractmethod
    def interrupt(self) -> None:
        """Interrupt the channeling."""
        pass
    
    @abstractmethod
    def is_channeling(self) -> bool:
        """Check if currently channeling."""
        pass


class IEffectManager(ABC):
    """Interface for managing effects on a character."""
    
    @abstractmethod
    def apply_effect(self, effect: IEffect) -> None:
        """Apply an effect."""
        pass
    
    @abstractmethod
    def remove_effect(self, effect_name: str) -> None:
        """Remove an effect by name."""
        pass
    
    @abstractmethod
    def get_effect(self, effect_name: str) -> Optional[IEffect]:
        """Get an effect by name."""
        pass
    
    @abstractmethod
    def get_all_effects(self) -> List[IEffect]:
        """Get all active effects."""
        pass
    
    @abstractmethod
    def get_effects_by_type(self, effect_type: type) -> List[IEffect]:
        """Get all effects of a specific type."""
        pass
    
    @abstractmethod
    def update_effects(self, current_time: float) -> None:
        """Update all effects (remove expired, process ticks, etc.)."""
        pass
    
    @abstractmethod
    def clear_all_effects(self) -> None:
        """Remove all effects."""
        pass
    
    @abstractmethod
    def has_effect(self, effect_name: str) -> bool:
        """Check if an effect is active."""
        pass


class IEffectFactory(ABC):
    """Interface for creating effects."""
    
    @abstractmethod
    def create_buff(self, name: str, duration: float, stat_modifiers: Dict[StatType, float], **kwargs) -> IBuff:
        """Create a buff effect."""
        pass
    
    @abstractmethod
    def create_debuff(self, name: str, duration: float, damage_per_tick: int, tick_interval: float, **kwargs) -> IDebuff:
        """Create a debuff effect."""
        pass
    
    @abstractmethod
    def create_aura(self, name: str, duration: float, radius: float, affects_allies: bool, affects_enemies: bool, **kwargs) -> IAura:
        """Create an aura effect."""
        pass
    
    @abstractmethod
    def create_channeled_effect(self, name: str, channel_time: float, tick_interval: float, **kwargs) -> IChanneledEffect:
        """Create a channeled effect."""
        pass
