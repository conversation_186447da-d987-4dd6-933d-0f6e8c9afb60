"""
Interfaces for the event system in the WoW Simulator.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from .base import IEvent, IEventBus, IEventListener, ICharacter, ISpell, IEffect


class EventType:
    """Constants for event types."""
    SPELL_CAST_START = "spell_cast_start"
    SPELL_CAST_SUCCESS = "spell_cast_success"
    SPELL_CAST_FAILED = "spell_cast_failed"
    SPELL_CAST_INTERRUPTED = "spell_cast_interrupted"
    
    DAMAGE_DEALT = "damage_dealt"
    DAMAGE_TAKEN = "damage_taken"
    HEAL_DEALT = "heal_dealt"
    HEAL_TAKEN = "heal_taken"
    
    EFFECT_APPLIED = "effect_applied"
    EFFECT_REMOVED = "effect_removed"
    EFFECT_TICK = "effect_tick"
    
    CRITICAL_HIT = "critical_hit"
    CRITICAL_HEAL = "critical_heal"
    
    MANA_CHANGED = "mana_changed"
    HEALTH_CHANGED = "health_changed"
    
    PROC_TRIGGERED = "proc_triggered"
    
    COMBAT_START = "combat_start"
    COMBAT_END = "combat_end"
    
    CHARACTER_DEATH = "character_death"
    CHARACTER_RESURRECTION = "character_resurrection"


class ISpellCastEvent(IEvent):
    """Interface for spell casting events."""
    
    @property
    @abstractmethod
    def spell(self) -> ISpell:
        """The spell being cast."""
        pass
    
    @property
    @abstractmethod
    def cast_time(self) -> float:
        """The cast time of the spell."""
        pass


class IDamageEvent(IEvent):
    """Interface for damage events."""
    
    @property
    @abstractmethod
    def damage_amount(self) -> int:
        """Amount of damage dealt."""
        pass
    
    @property
    @abstractmethod
    def spell(self) -> Optional[ISpell]:
        """The spell that caused the damage, if any."""
        pass
    
    @property
    @abstractmethod
    def was_critical(self) -> bool:
        """Whether this was a critical hit."""
        pass
    
    @property
    @abstractmethod
    def damage_type(self) -> str:
        """Type of damage (spell school, physical, etc.)."""
        pass


class IHealEvent(IEvent):
    """Interface for healing events."""
    
    @property
    @abstractmethod
    def heal_amount(self) -> int:
        """Amount of healing done."""
        pass
    
    @property
    @abstractmethod
    def spell(self) -> Optional[ISpell]:
        """The spell that caused the healing, if any."""
        pass
    
    @property
    @abstractmethod
    def was_critical(self) -> bool:
        """Whether this was a critical heal."""
        pass
    
    @property
    @abstractmethod
    def overheal(self) -> int:
        """Amount of overhealing."""
        pass


class IEffectEvent(IEvent):
    """Interface for effect-related events."""
    
    @property
    @abstractmethod
    def effect(self) -> IEffect:
        """The effect involved in this event."""
        pass


class IProcEvent(IEvent):
    """Interface for proc events."""
    
    @property
    @abstractmethod
    def proc_name(self) -> str:
        """Name of the proc that triggered."""
        pass
    
    @property
    @abstractmethod
    def trigger_event(self) -> IEvent:
        """The event that triggered this proc."""
        pass


class IStatChangeEvent(IEvent):
    """Interface for stat change events."""
    
    @property
    @abstractmethod
    def stat_name(self) -> str:
        """Name of the stat that changed."""
        pass
    
    @property
    @abstractmethod
    def old_value(self) -> float:
        """Previous value of the stat."""
        pass
    
    @property
    @abstractmethod
    def new_value(self) -> float:
        """New value of the stat."""
        pass
    
    @property
    @abstractmethod
    def change_amount(self) -> float:
        """Amount of change."""
        pass


class IEventFilter(ABC):
    """Interface for filtering events."""
    
    @abstractmethod
    def should_process(self, event: IEvent) -> bool:
        """Check if an event should be processed."""
        pass


class IEventHandler(ABC):
    """Interface for handling events."""
    
    @abstractmethod
    def handle(self, event: IEvent) -> None:
        """Handle an event."""
        pass
    
    @abstractmethod
    def can_handle(self, event_type: str) -> bool:
        """Check if this handler can handle events of the given type."""
        pass


class IEventFactory(ABC):
    """Interface for creating events."""
    
    @abstractmethod
    def create_spell_cast_event(self, caster: ICharacter, spell: ISpell, target: Optional[ICharacter] = None) -> ISpellCastEvent:
        """Create a spell cast event."""
        pass
    
    @abstractmethod
    def create_damage_event(self, source: ICharacter, target: ICharacter, damage: int, spell: Optional[ISpell] = None, was_critical: bool = False) -> IDamageEvent:
        """Create a damage event."""
        pass
    
    @abstractmethod
    def create_heal_event(self, source: ICharacter, target: ICharacter, heal: int, overheal: int = 0, spell: Optional[ISpell] = None, was_critical: bool = False) -> IHealEvent:
        """Create a heal event."""
        pass
    
    @abstractmethod
    def create_effect_event(self, event_type: str, target: ICharacter, effect: IEffect) -> IEffectEvent:
        """Create an effect event."""
        pass
    
    @abstractmethod
    def create_proc_event(self, source: ICharacter, proc_name: str, trigger_event: IEvent) -> IProcEvent:
        """Create a proc event."""
        pass


class IAdvancedEventBus(IEventBus):
    """Extended event bus interface with advanced features."""
    
    @abstractmethod
    def subscribe_with_filter(self, event_type: str, listener: IEventListener, event_filter: Optional[IEventFilter] = None) -> None:
        """Subscribe to events with an optional filter."""
        pass
    
    @abstractmethod
    def subscribe_with_priority(self, event_type: str, listener: IEventListener, priority: int = 0) -> None:
        """Subscribe to events with a priority (higher priority = processed first)."""
        pass
    
    @abstractmethod
    def publish_async(self, event: IEvent) -> None:
        """Publish an event asynchronously."""
        pass
    
    @abstractmethod
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 100) -> List[IEvent]:
        """Get recent event history."""
        pass
    
    @abstractmethod
    def clear_event_history(self) -> None:
        """Clear the event history."""
        pass
