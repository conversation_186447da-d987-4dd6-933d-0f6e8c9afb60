"""
Interfaces for different types of spells in the WoW Simulator.
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from enum import Enum
from .base import ISpell, ICharacter, IEffect


class SpellSchool(Enum):
    """Enumeration of spell schools."""
    FIRE = "fire"
    FROST = "frost"
    ARCANE = "arcane"
    SHADOW = "shadow"
    NATURE = "nature"
    HOLY = "holy"
    PHYSICAL = "physical"


class SpellType(Enum):
    """Enumeration of spell types."""
    DAMAGE = "damage"
    HEAL = "heal"
    BUFF = "buff"
    DEBUFF = "debuff"
    UTILITY = "utility"
    CHANNELED = "channeled"
    INSTANT = "instant"


class IDamageSpell(ISpell):
    """Interface for damage-dealing spells."""
    
    @property
    @abstractmethod
    def school(self) -> SpellSchool:
        """The spell school of this damage spell."""
        pass
    
    @property
    @abstractmethod
    def base_damage(self) -> int:
        """Base damage before modifiers."""
        pass
    
    @property
    @abstractmethod
    def spell_power_coefficient(self) -> float:
        """How much spell power affects this spell."""
        pass
    
    @abstractmethod
    def calculate_damage(self, caster: <PERSON><PERSON>hara<PERSON>, target: Optional[ICharacter] = None) -> int:
        """Calculate the damage this spell would deal."""
        pass
    
    @abstractmethod
    def can_crit(self) -> bool:
        """Whether this spell can critically hit."""
        pass


class IHealSpell(ISpell):
    """Interface for healing spells."""
    
    @property
    @abstractmethod
    def base_heal(self) -> int:
        """Base healing before modifiers."""
        pass
    
    @property
    @abstractmethod
    def spell_power_coefficient(self) -> float:
        """How much spell power affects this spell."""
        pass
    
    @abstractmethod
    def calculate_heal(self, caster: ICharacter, target: ICharacter) -> int:
        """Calculate the healing this spell would provide."""
        pass


class IBuffSpell(ISpell):
    """Interface for spells that apply buffs."""
    
    @abstractmethod
    def get_buff_effects(self) -> List[IEffect]:
        """Get the buff effects this spell applies."""
        pass
    
    @property
    @abstractmethod
    def target_self(self) -> bool:
        """Whether this buff targets the caster."""
        pass
    
    @property
    @abstractmethod
    def target_allies(self) -> bool:
        """Whether this buff can target allies."""
        pass


class IDebuffSpell(ISpell):
    """Interface for spells that apply debuffs."""
    
    @abstractmethod
    def get_debuff_effects(self) -> List[IEffect]:
        """Get the debuff effects this spell applies."""
        pass
    
    @property
    @abstractmethod
    def requires_target(self) -> bool:
        """Whether this spell requires a target."""
        pass


class IChanneledSpell(ISpell):
    """Interface for channeled spells."""
    
    @property
    @abstractmethod
    def channel_time(self) -> float:
        """Total channel time in seconds."""
        pass
    
    @property
    @abstractmethod
    def tick_interval(self) -> float:
        """Time between channel ticks."""
        pass
    
    @property
    @abstractmethod
    def damage_per_tick(self) -> int:
        """Damage dealt per tick."""
        pass
    
    @abstractmethod
    def interrupt(self, caster: ICharacter) -> None:
        """Interrupt the channeling."""
        pass


class IInstantSpell(ISpell):
    """Interface for instant cast spells."""
    
    @property
    def cast_time(self) -> float:
        """Instant spells have 0 cast time."""
        return 0.0
    
    @abstractmethod
    def trigger_global_cooldown(self) -> bool:
        """Whether this spell triggers the global cooldown."""
        pass


class ISpellBuilder(ABC):
    """Interface for building spells using the builder pattern."""
    
    @abstractmethod
    def set_name(self, name: str) -> 'ISpellBuilder':
        """Set the spell name."""
        pass
    
    @abstractmethod
    def set_school(self, school: SpellSchool) -> 'ISpellBuilder':
        """Set the spell school."""
        pass
    
    @abstractmethod
    def set_cast_time(self, cast_time: float) -> 'ISpellBuilder':
        """Set the cast time."""
        pass
    
    @abstractmethod
    def set_cooldown(self, cooldown: float) -> 'ISpellBuilder':
        """Set the cooldown."""
        pass
    
    @abstractmethod
    def set_mana_cost(self, mana_cost: int) -> 'ISpellBuilder':
        """Set the mana cost."""
        pass
    
    @abstractmethod
    def set_damage(self, base_damage: int, coefficient: float = 1.0) -> 'ISpellBuilder':
        """Set damage parameters."""
        pass
    
    @abstractmethod
    def set_heal(self, base_heal: int, coefficient: float = 1.0) -> 'ISpellBuilder':
        """Set healing parameters."""
        pass
    
    @abstractmethod
    def add_effect(self, effect: IEffect) -> 'ISpellBuilder':
        """Add an effect to the spell."""
        pass
    
    @abstractmethod
    def build(self) -> ISpell:
        """Build the final spell."""
        pass


class ISpellFactory(ABC):
    """Interface for creating spells."""
    
    @abstractmethod
    def create_damage_spell(self, name: str, school: SpellSchool, base_damage: int, **kwargs) -> IDamageSpell:
        """Create a damage spell."""
        pass
    
    @abstractmethod
    def create_heal_spell(self, name: str, base_heal: int, **kwargs) -> IHealSpell:
        """Create a healing spell."""
        pass
    
    @abstractmethod
    def create_buff_spell(self, name: str, effects: List[IEffect], **kwargs) -> IBuffSpell:
        """Create a buff spell."""
        pass
    
    @abstractmethod
    def create_debuff_spell(self, name: str, effects: List[IEffect], **kwargs) -> IDebuffSpell:
        """Create a debuff spell."""
        pass
    
    @abstractmethod
    def get_builder(self) -> ISpellBuilder:
        """Get a spell builder for complex spell creation."""
        pass


class ISpellbook(ABC):
    """Interface for managing a character's spells."""
    
    @abstractmethod
    def add_spell(self, spell: ISpell) -> None:
        """Add a spell to the spellbook."""
        pass
    
    @abstractmethod
    def remove_spell(self, spell_name: str) -> None:
        """Remove a spell from the spellbook."""
        pass
    
    @abstractmethod
    def get_spell(self, spell_name: str) -> Optional[ISpell]:
        """Get a spell by name."""
        pass
    
    @abstractmethod
    def get_all_spells(self) -> List[ISpell]:
        """Get all spells in the spellbook."""
        pass
    
    @abstractmethod
    def get_spells_by_school(self, school: SpellSchool) -> List[ISpell]:
        """Get all spells of a specific school."""
        pass
    
    @abstractmethod
    def get_spells_by_type(self, spell_type: SpellType) -> List[ISpell]:
        """Get all spells of a specific type."""
        pass
    
    @abstractmethod
    def has_spell(self, spell_name: str) -> bool:
        """Check if a spell is in the spellbook."""
        pass
