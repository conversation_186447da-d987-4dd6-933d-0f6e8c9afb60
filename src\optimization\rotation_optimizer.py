"""
Spell Rotation Optimizer
Finds optimal spell rotations for maximum DPS output.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import time
import copy
from src.character import ModularCharacter
from src.spells.modular_spells import ModularSpell
from src.spells.spell_builder import SpellBuilder


class OptimizationGoal(Enum):
    """Optimization goals for rotation finder."""
    MAXIMUM_DPS = "maximum_dps"
    MAXIMUM_DAMAGE = "maximum_damage"
    MANA_EFFICIENT = "mana_efficient"
    BURST_DAMAGE = "burst_damage"
    SUSTAINED_DAMAGE = "sustained_damage"


@dataclass
class RotationStep:
    """A single step in a spell rotation."""
    spell_name: str
    cast_time: float
    damage: int
    mana_cost: int
    cooldown_remaining: float
    timestamp: float
    notes: str = ""


@dataclass
class RotationResult:
    """Result of rotation optimization."""
    rotation_steps: List[RotationStep]
    total_damage: int
    total_time: float
    dps: float
    mana_used: int
    mana_efficiency: float  # Damage per mana
    cooldown_efficiency: float  # % time not waiting for cooldowns
    spell_breakdown: Dict[str, Dict[str, Any]]


class RotationOptimizer:
    """Optimizes spell rotations for maximum effectiveness."""
    
    def __init__(self):
        self.simulation_step = 0.1  # 100ms simulation steps
        self.max_simulation_time = 300  # 5 minutes max
        self.gcd = 1.5  # Global cooldown
    
    def optimize_rotation(
        self,
        character: ModularCharacter,
        available_spells: List[ModularSpell],
        duration: float,
        goal: OptimizationGoal = OptimizationGoal.MAXIMUM_DPS,
        target_count: int = 1,
        movement_time: float = 0.0
    ) -> RotationResult:
        """
        Find the optimal spell rotation.
        
        Args:
            character: Character to optimize for
            available_spells: List of spells to choose from
            duration: Duration to optimize for (seconds)
            goal: Optimization goal
            target_count: Number of targets
            movement_time: Time spent moving (can't cast)
            
        Returns:
            RotationResult with optimal rotation
        """
        if goal == OptimizationGoal.MAXIMUM_DPS:
            return self._optimize_for_dps(character, available_spells, duration, target_count, movement_time)
        elif goal == OptimizationGoal.MANA_EFFICIENT:
            return self._optimize_for_mana_efficiency(character, available_spells, duration, target_count)
        elif goal == OptimizationGoal.BURST_DAMAGE:
            return self._optimize_for_burst(character, available_spells, min(duration, 30), target_count)
        else:
            # Default to DPS optimization
            return self._optimize_for_dps(character, available_spells, duration, target_count, movement_time)
    
    def _optimize_for_dps(
        self,
        character: ModularCharacter,
        spells: List[ModularSpell],
        duration: float,
        target_count: int,
        movement_time: float
    ) -> RotationResult:
        """Optimize for maximum DPS."""
        
        # Create priority list based on DPS potential
        spell_priorities = self._calculate_spell_priorities(character, spells, target_count)
        
        # Simulate rotation
        rotation_steps = []
        current_time = 0.0
        total_damage = 0
        mana_used = 0
        last_cast_time = 0.0
        spell_cooldowns = {spell.name: 0.0 for spell in spells}
        spell_usage = {spell.name: {"casts": 0, "damage": 0, "time": 0.0} for spell in spells}
        
        # Create a copy of character for simulation
        sim_character = copy.deepcopy(character)
        
        while current_time < duration:
            # Find best available spell
            best_spell = self._find_best_available_spell(
                spell_priorities, spell_cooldowns, current_time, last_cast_time, sim_character
            )
            
            if best_spell is None:
                # No spells available, advance time
                current_time += self.simulation_step
                continue
            
            # Check if we can cast (GCD and mana)
            if current_time < last_cast_time + self.gcd:
                current_time += self.simulation_step
                continue
            
            if sim_character.current_mana < best_spell.mana_cost:
                # Out of mana, try to find a cheaper spell or wait
                cheaper_spell = self._find_cheaper_spell(spells, sim_character.current_mana, spell_cooldowns, current_time, last_cast_time)
                if cheaper_spell:
                    best_spell = cheaper_spell
                else:
                    # No spells available, advance time
                    current_time += self.simulation_step
                    continue
            
            # Cast the spell
            cast_result = self._simulate_spell_cast(best_spell, sim_character, target_count)
            
            # Record the cast
            step = RotationStep(
                spell_name=best_spell.name,
                cast_time=best_spell.cast_time,
                damage=cast_result["damage"],
                mana_cost=best_spell.mana_cost,
                cooldown_remaining=best_spell.cooldown,
                timestamp=current_time
            )
            rotation_steps.append(step)
            
            # Update state
            total_damage += cast_result["damage"]
            mana_used += best_spell.mana_cost
            sim_character.current_mana -= best_spell.mana_cost
            
            # Update cooldowns
            spell_cooldowns[best_spell.name] = current_time + best_spell.cooldown
            last_cast_time = current_time
            
            # Update usage stats
            spell_usage[best_spell.name]["casts"] += 1
            spell_usage[best_spell.name]["damage"] += cast_result["damage"]
            spell_usage[best_spell.name]["time"] += max(best_spell.cast_time, self.gcd)
            
            # Advance time by cast time or GCD
            current_time += max(best_spell.cast_time, self.gcd)
        
        # Calculate results
        actual_time = min(current_time, duration)
        dps = total_damage / actual_time if actual_time > 0 else 0
        mana_efficiency = total_damage / mana_used if mana_used > 0 else 0
        
        # Calculate cooldown efficiency
        active_time = sum([usage["time"] for usage in spell_usage.values()])
        cooldown_efficiency = (active_time / actual_time) * 100 if actual_time > 0 else 0
        
        return RotationResult(
            rotation_steps=rotation_steps,
            total_damage=total_damage,
            total_time=actual_time,
            dps=dps,
            mana_used=mana_used,
            mana_efficiency=mana_efficiency,
            cooldown_efficiency=cooldown_efficiency,
            spell_breakdown=spell_usage
        )
    
    def _optimize_for_mana_efficiency(
        self,
        character: ModularCharacter,
        spells: List[ModularSpell],
        duration: float,
        target_count: int
    ) -> RotationResult:
        """Optimize for mana efficiency (damage per mana)."""
        
        # Calculate damage per mana for each spell
        mana_priorities = []
        for spell in spells:
            if spell.mana_cost > 0:
                damage = self._calculate_spell_damage(spell, character, target_count)
                dpm = damage / spell.mana_cost
                mana_priorities.append((spell, dpm))
        
        # Sort by damage per mana (highest first)
        mana_priorities.sort(key=lambda x: x[1], reverse=True)
        
        # Use the mana-efficient priority list
        efficient_spells = [spell for spell, _ in mana_priorities]
        
        return self._optimize_for_dps(character, efficient_spells, duration, target_count, 0.0)
    
    def _optimize_for_burst(
        self,
        character: ModularCharacter,
        spells: List[ModularSpell],
        duration: float,
        target_count: int
    ) -> RotationResult:
        """Optimize for burst damage (front-load damage)."""
        
        # Prioritize high-damage spells and cooldowns
        burst_priorities = []
        for spell in spells:
            damage = self._calculate_spell_damage(spell, character, target_count)
            # Prioritize instant high-damage spells and cooldowns
            priority_score = damage
            if spell.cast_time == 0:  # Instant spells
                priority_score *= 1.5
            if spell.cooldown > 0:  # Cooldown spells (usually more powerful)
                priority_score *= 1.3
            
            burst_priorities.append((spell, priority_score))
        
        # Sort by burst priority
        burst_priorities.sort(key=lambda x: x[1], reverse=True)
        burst_spells = [spell for spell, _ in burst_priorities]
        
        return self._optimize_for_dps(character, burst_spells, duration, target_count, 0.0)
    
    def _calculate_spell_priorities(
        self,
        character: ModularCharacter,
        spells: List[ModularSpell],
        target_count: int
    ) -> List[Tuple[ModularSpell, float]]:
        """Calculate priority scores for spells based on DPS potential."""
        
        priorities = []
        for spell in spells:
            damage = self._calculate_spell_damage(spell, character, target_count)
            cast_time = max(spell.cast_time, self.gcd)
            dps = damage / cast_time
            
            # Adjust for cooldowns (lower priority if long cooldown)
            if spell.cooldown > 0:
                cooldown_factor = cast_time / (cast_time + spell.cooldown)
                dps *= cooldown_factor
            
            priorities.append((spell, dps))
        
        # Sort by DPS (highest first)
        priorities.sort(key=lambda x: x[1], reverse=True)
        return priorities
    
    def _calculate_spell_damage(self, spell: ModularSpell, character: ModularCharacter, target_count: int) -> int:
        """Calculate expected damage for a spell."""
        # Create a mock target for damage calculation
        from src.character import ModularCharacter
        target = ModularCharacter("Mock Target")
        target.stats.set_stat("health", 5000)
        target.current_health = target.get_max_health()
        
        base_damage = spell.calculate_damage(character, target)
        
        # Apply crit chance
        if spell.can_crit:
            crit_chance = character.get_crit_chance()
            expected_damage = base_damage * (1 + crit_chance)  # Expected value with crits
        else:
            expected_damage = base_damage
        
        # Apply target count for AoE
        if target_count > 1:
            # Assume diminishing returns for AoE
            aoe_multiplier = min(target_count, 1 + (target_count - 1) * 0.7)
            expected_damage *= aoe_multiplier
        
        return int(expected_damage)
    
    def _find_best_available_spell(
        self,
        priorities: List[Tuple[ModularSpell, float]],
        cooldowns: Dict[str, float],
        current_time: float,
        last_cast_time: float,
        character: ModularCharacter
    ) -> Optional[ModularSpell]:
        """Find the best available spell to cast."""
        
        for spell, priority in priorities:
            # Check cooldown
            if current_time < cooldowns.get(spell.name, 0):
                continue
            
            # Check GCD
            if current_time < last_cast_time + self.gcd:
                continue
            
            # Check mana
            if character.current_mana < spell.mana_cost:
                continue
            
            return spell
        
        return None
    
    def _find_cheaper_spell(
        self,
        spells: List[ModularSpell],
        available_mana: int,
        cooldowns: Dict[str, float],
        current_time: float,
        last_cast_time: float
    ) -> Optional[ModularSpell]:
        """Find a spell that fits within available mana."""
        
        affordable_spells = []
        for spell in spells:
            if (spell.mana_cost <= available_mana and
                current_time >= cooldowns.get(spell.name, 0) and
                current_time >= last_cast_time + self.gcd):
                affordable_spells.append(spell)
        
        if not affordable_spells:
            return None
        
        # Return the most mana-efficient affordable spell
        best_spell = None
        best_efficiency = 0
        
        for spell in affordable_spells:
            if spell.mana_cost > 0:
                # Rough damage estimate using calculate_damage method
                try:
                    from src.character import ModularCharacter
                    mock_target = ModularCharacter("Mock")
                    mock_target.stats.set_stat("health", 1000)
                    mock_target.current_health = 1000
                    damage = spell.calculate_damage(character, mock_target)
                except:
                    # Fallback to base damage or default
                    if hasattr(spell, 'base_damage'):
                        damage = spell.base_damage if isinstance(spell.base_damage, (int, float)) else 100
                    else:
                        damage = 100

                efficiency = damage / spell.mana_cost
                if efficiency > best_efficiency:
                    best_efficiency = efficiency
                    best_spell = spell
        
        return best_spell or affordable_spells[0]
    
    def _simulate_spell_cast(self, spell: ModularSpell, character: ModularCharacter, target_count: int) -> Dict[str, Any]:
        """Simulate casting a spell and return results."""
        damage = self._calculate_spell_damage(spell, character, target_count)
        
        return {
            "damage": damage,
            "mana_cost": spell.mana_cost,
            "cast_time": spell.cast_time,
            "success": True
        }
    
    def compare_rotations(
        self,
        character: ModularCharacter,
        spell_sets: List[Tuple[str, List[ModularSpell]]],
        duration: float,
        goal: OptimizationGoal = OptimizationGoal.MAXIMUM_DPS
    ) -> Dict[str, RotationResult]:
        """Compare multiple spell rotations."""
        
        results = {}
        for name, spells in spell_sets:
            result = self.optimize_rotation(character, spells, duration, goal)
            results[name] = result
        
        return results
    
    def get_rotation_summary(self, result: RotationResult) -> str:
        """Get a human-readable summary of a rotation."""
        summary = []
        summary.append(f"Total Damage: {result.total_damage:,}")
        summary.append(f"DPS: {result.dps:.1f}")
        summary.append(f"Duration: {result.total_time:.1f}s")
        summary.append(f"Mana Used: {result.mana_used:,}")
        summary.append(f"Mana Efficiency: {result.mana_efficiency:.2f} damage/mana")
        summary.append(f"Cooldown Efficiency: {result.cooldown_efficiency:.1f}%")
        summary.append(f"Total Casts: {len(result.rotation_steps)}")
        
        return "\n".join(summary)
