"""
Timeline Visualizer for Spell Rotations
Creates visual representations of spell rotations and cooldowns.
"""

from typing import Dict, List, Any, Optional
import math
from .rotation_optimizer import RotationResult, RotationStep


class TimelineVisualizer:
    """Creates visual timelines for spell rotations."""
    
    def __init__(self):
        self.timeline_width = 80  # Characters wide
        self.time_scale = 1.0  # Seconds per character
    
    def visualize_rotation(self, result: RotationResult, title: str = "Spell Rotation") -> str:
        """Create a visual timeline of the rotation."""
        
        lines = []
        lines.append(f"📊 {title}")
        lines.append("=" * len(f"📊 {title}"))
        lines.append("")
        
        # Summary stats
        lines.append(f"Total Damage: {result.total_damage:,}")
        lines.append(f"DPS: {result.dps:.1f}")
        lines.append(f"Duration: {result.total_time:.1f}s")
        lines.append(f"Mana Used: {result.mana_used:,}")
        lines.append("")
        
        # Create timeline
        timeline = self._create_timeline(result)
        lines.extend(timeline)
        
        # Spell breakdown
        lines.append("")
        lines.append("📋 Spell Breakdown:")
        lines.append("-" * 40)
        breakdown = self._create_spell_breakdown(result)
        lines.extend(breakdown)
        
        return "\n".join(lines)
    
    def _create_timeline(self, result: RotationResult) -> List[str]:
        """Create the visual timeline."""
        lines = []
        
        if not result.rotation_steps:
            return ["No spells cast"]
        
        # Calculate time scale
        max_time = result.total_time
        self.time_scale = max_time / self.timeline_width
        
        # Time ruler
        lines.append("⏰ Timeline:")
        lines.append(self._create_time_ruler(max_time))
        lines.append("")
        
        # Group spells by type for better visualization
        spell_groups = self._group_spells_by_type(result.rotation_steps)
        
        for group_name, steps in spell_groups.items():
            if not steps:
                continue
            
            timeline_line = self._create_spell_timeline(steps, max_time)
            lines.append(f"{group_name:<15} {timeline_line}")
        
        lines.append("")
        
        # Cooldown visualization
        lines.append("❄️  Cooldowns:")
        cooldown_viz = self._create_cooldown_visualization(result.rotation_steps, max_time)
        lines.extend(cooldown_viz)
        
        return lines
    
    def _create_time_ruler(self, max_time: float) -> str:
        """Create a time ruler showing seconds."""
        ruler = ""
        for i in range(self.timeline_width):
            time_at_pos = i * self.time_scale
            if time_at_pos <= max_time:
                if i % 10 == 0:
                    ruler += "|"
                elif i % 5 == 0:
                    ruler += "+"
                else:
                    ruler += "-"
            else:
                ruler += " "
        
        # Add time labels
        labels = " " * 15  # Offset for spell names
        for i in range(0, self.timeline_width, 10):
            time_at_pos = i * self.time_scale
            if time_at_pos <= max_time:
                label = f"{time_at_pos:.0f}s"
                # Position the label
                pos = 15 + i - len(label) // 2
                if pos >= len(labels):
                    labels += " " * (pos - len(labels)) + label
                else:
                    # Overlay the label
                    labels = labels[:pos] + label + labels[pos + len(label):]
        
        return labels + "\n" + " " * 15 + ruler
    
    def _group_spells_by_type(self, steps: List[RotationStep]) -> Dict[str, List[RotationStep]]:
        """Group spells by type for better visualization."""
        groups = {
            "Instant": [],
            "Cast Time": [],
            "Cooldowns": [],
            "Other": []
        }
        
        for step in steps:
            if step.cast_time == 0:
                groups["Instant"].append(step)
            elif step.cooldown_remaining > 0:
                groups["Cooldowns"].append(step)
            elif step.cast_time > 0:
                groups["Cast Time"].append(step)
            else:
                groups["Other"].append(step)
        
        # Remove empty groups
        return {k: v for k, v in groups.items() if v}
    
    def _create_spell_timeline(self, steps: List[RotationStep], max_time: float) -> str:
        """Create timeline for a group of spells."""
        timeline = [" "] * self.timeline_width
        
        for step in steps:
            start_pos = int(step.timestamp / self.time_scale)
            cast_duration = max(step.cast_time, 1.5)  # Minimum GCD
            end_pos = int((step.timestamp + cast_duration) / self.time_scale)
            
            # Choose symbol based on spell characteristics
            if step.cast_time == 0:
                symbol = "⚡"  # Instant
            elif step.cooldown_remaining > 30:
                symbol = "🔥"  # Major cooldown
            elif step.cooldown_remaining > 0:
                symbol = "💫"  # Minor cooldown
            else:
                symbol = "●"   # Regular cast
            
            # Fill the timeline
            for pos in range(start_pos, min(end_pos + 1, self.timeline_width)):
                if pos < len(timeline):
                    timeline[pos] = symbol
        
        return "".join(timeline)
    
    def _create_cooldown_visualization(self, steps: List[RotationStep], max_time: float) -> List[str]:
        """Create cooldown visualization."""
        lines = []
        
        # Find spells with cooldowns
        cooldown_spells = {}
        for step in steps:
            if step.cooldown_remaining > 0:
                if step.spell_name not in cooldown_spells:
                    cooldown_spells[step.spell_name] = []
                cooldown_spells[step.spell_name].append(step)
        
        if not cooldown_spells:
            return ["No cooldowns to display"]
        
        for spell_name, spell_steps in cooldown_spells.items():
            timeline = [" "] * self.timeline_width
            
            for step in spell_steps:
                # Mark when spell is cast
                cast_pos = int(step.timestamp / self.time_scale)
                if cast_pos < len(timeline):
                    timeline[cast_pos] = "🔴"  # Cast marker
                
                # Mark cooldown period
                cooldown_end = step.timestamp + step.cooldown_remaining
                cooldown_end_pos = int(cooldown_end / self.time_scale)
                
                for pos in range(cast_pos + 1, min(cooldown_end_pos, self.timeline_width)):
                    if pos < len(timeline):
                        timeline[pos] = "─"  # Cooldown line
                
                # Mark when cooldown ends
                if cooldown_end_pos < len(timeline):
                    timeline[cooldown_end_pos] = "🟢"  # Ready marker
            
            lines.append(f"{spell_name:<15} {''.join(timeline)}")
        
        return lines
    
    def _create_spell_breakdown(self, result: RotationResult) -> List[str]:
        """Create spell usage breakdown."""
        lines = []
        
        # Sort spells by damage contribution
        sorted_spells = sorted(
            result.spell_breakdown.items(),
            key=lambda x: x[1]["damage"],
            reverse=True
        )
        
        for spell_name, stats in sorted_spells:
            if stats["casts"] == 0:
                continue
            
            damage_percent = (stats["damage"] / result.total_damage) * 100 if result.total_damage > 0 else 0
            avg_damage = stats["damage"] / stats["casts"] if stats["casts"] > 0 else 0
            
            lines.append(f"{spell_name}:")
            lines.append(f"  Casts: {stats['casts']}")
            lines.append(f"  Total Damage: {stats['damage']:,} ({damage_percent:.1f}%)")
            lines.append(f"  Avg Damage: {avg_damage:.0f}")
            lines.append(f"  Time: {stats['time']:.1f}s")
            lines.append("")
        
        return lines
    
    def create_comparison_chart(self, results: Dict[str, RotationResult]) -> str:
        """Create a comparison chart of multiple rotations."""
        lines = []
        lines.append("📊 Rotation Comparison")
        lines.append("=" * 50)
        lines.append("")
        
        # Header
        lines.append(f"{'Rotation':<20} {'DPS':<8} {'Damage':<10} {'Mana':<8} {'Efficiency':<10}")
        lines.append("-" * 60)
        
        # Sort by DPS
        sorted_results = sorted(results.items(), key=lambda x: x[1].dps, reverse=True)
        
        for name, result in sorted_results:
            lines.append(
                f"{name:<20} "
                f"{result.dps:<8.1f} "
                f"{result.total_damage:<10,} "
                f"{result.mana_used:<8,} "
                f"{result.mana_efficiency:<10.2f}"
            )
        
        lines.append("")
        
        # Visual DPS comparison
        lines.append("DPS Comparison:")
        max_dps = max(result.dps for result in results.values()) if results else 1
        
        for name, result in sorted_results:
            bar_length = int((result.dps / max_dps) * 40)
            bar = "█" * bar_length + "░" * (40 - bar_length)
            lines.append(f"{name:<20} {bar} {result.dps:.1f}")
        
        return "\n".join(lines)
    
    def create_mana_timeline(self, result: RotationResult, starting_mana: int) -> str:
        """Create a timeline showing mana usage."""
        lines = []
        lines.append("💧 Mana Usage Timeline")
        lines.append("=" * 30)
        lines.append("")
        
        if not result.rotation_steps:
            return "\n".join(lines + ["No spells cast"])
        
        # Calculate mana at each step
        current_mana = starting_mana
        mana_points = [(0, current_mana)]
        
        for step in result.rotation_steps:
            current_mana -= step.mana_cost
            mana_points.append((step.timestamp, current_mana))
        
        # Create visual representation
        max_time = result.total_time
        timeline_chars = 60
        time_scale = max_time / timeline_chars
        
        # Mana percentage timeline
        mana_timeline = []
        for i in range(timeline_chars):
            time_at_pos = i * time_scale
            
            # Find mana at this time
            mana_at_time = starting_mana
            for timestamp, mana in mana_points:
                if timestamp <= time_at_pos:
                    mana_at_time = mana
                else:
                    break
            
            mana_percent = mana_at_time / starting_mana
            
            if mana_percent > 0.8:
                mana_timeline.append("█")
            elif mana_percent > 0.6:
                mana_timeline.append("▓")
            elif mana_percent > 0.4:
                mana_timeline.append("▒")
            elif mana_percent > 0.2:
                mana_timeline.append("░")
            else:
                mana_timeline.append(" ")
        
        lines.append("Mana Level:")
        lines.append("100% █████████████████████████████████████████████████████████")
        lines.append(f"     {''.join(mana_timeline)}")
        lines.append("  0% ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░")
        lines.append("")
        
        # Mana usage stats
        lines.append(f"Starting Mana: {starting_mana:,}")
        lines.append(f"Ending Mana: {current_mana:,}")
        lines.append(f"Mana Used: {result.mana_used:,}")
        lines.append(f"Mana Efficiency: {result.mana_efficiency:.2f} damage/mana")
        
        return "\n".join(lines)
