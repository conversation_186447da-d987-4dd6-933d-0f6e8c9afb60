"""
DEPRECATED: Legacy spell system.

This file is kept for backward compatibility but should not be used for new code.
Use src.spells.modular_spells instead for the new modular spell system.
"""

import warnings
from dataclasses import dataclass
from typing import Dict, List, Optional
from enum import Enum

# Issue deprecation warning when this module is imported
warnings.warn(
    "src.spell_system is deprecated. Use src.spells.modular_spells instead.",
    DeprecationWarning,
    stacklevel=2
)


class SpellType(Enum):
    """Legacy spell type enumeration."""
    DAMAGE = "damage"
    BUFF = "buff"
    DEBUFF = "debuff"
    PROC = "proc"


@dataclass
class Spell:
    """
    Legacy spell class.

    For new code, use src.spells.modular_spells.ModularSpell instead.
    """
    name: str
    damage: int
    cast_time: float
    cooldown: float
    mana_cost: int
    spell_type: SpellType

    def __post_init__(self):
        """Issue warning when creating legacy spells."""
        warnings.warn(
            f"Creating legacy Spell '{self.name}'. Consider using ModularSpell instead.",
            DeprecationWarning,
            stacklevel=2
        )


class Character:
    """
    Legacy character class.

    For new code, use src.character.ModularCharacter instead.
    """

    def __init__(self):
        warnings.warn(
            "Creating legacy Character. Consider using ModularCharacter instead.",
            DeprecationWarning,
            stacklevel=2
        )

        self.health = 100
        self.mana = 100
        self.buffs = {}
        self.debuffs = {}

    def cast_spell(self, spell: Spell, target):
        """
        Basic spell casting logic.

        This is a stub implementation. For full functionality,
        use ModularCharacter with ModularSpell.
        """
        print(f"Legacy cast: {spell.name} -> {target if target else 'no target'}")
        # Basic spell casting logic would go here
        pass