"""
Advanced spell effects system for complex WoW-like mechanics.
Handles procs, conditional effects, chain effects, and spell synergies.
"""

from typing import Dict, List, Any, Optional, Callable, Union
from enum import Enum
from dataclasses import dataclass
import random
import time
try:
    from src.core.proc import ProcEvent, TriggerCondition
except ImportError:
    # Fallback for missing proc system
    class ProcEvent:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class TriggerCondition:
        ON_SPELL_CAST = "on_spell_cast"

try:
    from src.stats.standalone_stats import StatType
except ImportError:
    # Fallback for missing StatType
    class StatType:
        HEALTH = "health"
        MANA = "mana"
        SPELL_POWER = "spell_power"


class AdvancedEffectType(Enum):
    """Advanced effect types for complex spell mechanics."""
    PROC_EFFECT = "proc_effect"
    CONDITIONAL_EFFECT = "conditional_effect"
    CHAIN_EFFECT = "chain_effect"
    SYNERGY_EFFECT = "synergy_effect"
    STACKING_EFFECT = "stacking_effect"
    TRANSFORMATION_EFFECT = "transformation_effect"
    RESOURCE_MANIPULATION = "resource_manipulation"
    SPELL_MODIFICATION = "spell_modification"


class ConditionType(Enum):
    """Types of conditions for conditional effects."""
    TARGET_HEALTH_PERCENT = "target_health_percent"
    CASTER_HEALTH_PERCENT = "caster_health_percent"
    TARGET_MANA_PERCENT = "target_mana_percent"
    CASTER_MANA_PERCENT = "caster_mana_percent"
    HAS_BUFF = "has_buff"
    HAS_DEBUFF = "has_debuff"
    SPELL_CRITICAL = "spell_critical"
    TARGET_TYPE = "target_type"
    COMBAT_TIME = "combat_time"
    SPELL_SCHOOL = "spell_school"
    DISTANCE_TO_TARGET = "distance_to_target"


@dataclass
class EffectCondition:
    """Condition that must be met for an effect to trigger."""
    condition_type: ConditionType
    operator: str  # ">=", "<=", "==", "!=", "contains"
    value: Any
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """Evaluate if this condition is met."""
        try:
            actual_value = self._get_context_value(context)
            
            if self.operator == ">=":
                return actual_value >= self.value
            elif self.operator == "<=":
                return actual_value <= self.value
            elif self.operator == "==":
                return actual_value == self.value
            elif self.operator == "!=":
                return actual_value != self.value
            elif self.operator == "contains":
                return self.value in actual_value
            else:
                return False
                
        except Exception:
            return False
    
    def _get_context_value(self, context: Dict[str, Any]) -> Any:
        """Extract the relevant value from context."""
        if self.condition_type == ConditionType.TARGET_HEALTH_PERCENT:
            target = context.get('target')
            if target:
                return (target.current_health / target.get_max_health()) * 100
        elif self.condition_type == ConditionType.CASTER_HEALTH_PERCENT:
            caster = context.get('caster')
            if caster:
                return (caster.current_health / caster.get_max_health()) * 100
        elif self.condition_type == ConditionType.SPELL_CRITICAL:
            return context.get('was_critical', False)
        elif self.condition_type == ConditionType.HAS_BUFF:
            target = context.get('target')
            if target and hasattr(target, 'buffs'):
                return self.value in target.buffs
        # Add more condition types as needed
        
        return None


@dataclass
class AdvancedSpellEffect:
    """Advanced spell effect with complex mechanics."""
    effect_type: AdvancedEffectType
    name: str
    description: str
    
    # Basic properties
    value: Union[int, float, Dict[str, Any]]
    duration: float = 0.0
    chance: float = 1.0
    
    # Advanced properties
    conditions: List[EffectCondition] = None
    cooldown: float = 0.0
    max_stacks: int = 1
    spell_power_coefficient: float = 0.0
    
    # Chain effect properties
    max_targets: int = 1
    damage_reduction_per_jump: float = 0.0
    max_range: float = 30.0
    
    # Proc properties
    proc_spell: Optional[str] = None
    internal_cooldown: float = 0.0
    
    def __post_init__(self):
        if self.conditions is None:
            self.conditions = []


class AdvancedEffectProcessor:
    """Processes advanced spell effects."""
    
    def __init__(self):
        self.active_effects: Dict[str, Dict[str, Any]] = {}
        self.effect_history: List[Dict[str, Any]] = []
    
    def process_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an advanced spell effect.
        
        Args:
            effect: The effect to process
            context: Context including caster, target, spell, etc.
            
        Returns:
            Dictionary with effect results
        """
        # Check conditions
        if not self._check_conditions(effect, context):
            return {"success": False, "reason": "Conditions not met"}
        
        # Check chance
        if random.random() > effect.chance:
            return {"success": False, "reason": "Chance roll failed"}
        
        # Check cooldown
        if not self._check_cooldown(effect, context):
            return {"success": False, "reason": "Effect on cooldown"}
        
        # Process based on effect type
        result = self._process_by_type(effect, context)
        
        # Record effect
        self._record_effect(effect, context, result)
        
        return result
    
    def _check_conditions(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> bool:
        """Check if all conditions are met."""
        for condition in effect.conditions:
            if not condition.evaluate(context):
                return False
        return True
    
    def _check_cooldown(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> bool:
        """Check if effect is off cooldown."""
        if effect.cooldown <= 0:
            return True
        
        effect_key = f"{effect.name}_{context.get('caster_id', 'unknown')}"
        last_use = self.active_effects.get(effect_key, {}).get('last_use', 0)
        
        return time.time() >= last_use + effect.cooldown
    
    def _process_by_type(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process effect based on its type."""
        if effect.effect_type == AdvancedEffectType.PROC_EFFECT:
            return self._process_proc_effect(effect, context)
        elif effect.effect_type == AdvancedEffectType.CONDITIONAL_EFFECT:
            return self._process_conditional_effect(effect, context)
        elif effect.effect_type == AdvancedEffectType.CHAIN_EFFECT:
            return self._process_chain_effect(effect, context)
        elif effect.effect_type == AdvancedEffectType.STACKING_EFFECT:
            return self._process_stacking_effect(effect, context)
        elif effect.effect_type == AdvancedEffectType.SYNERGY_EFFECT:
            return self._process_synergy_effect(effect, context)
        else:
            return {"success": False, "reason": f"Unknown effect type: {effect.effect_type}"}
    
    def _process_proc_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a proc effect that triggers another spell or effect."""
        caster = context.get('caster')
        target = context.get('target')
        
        if not caster:
            return {"success": False, "reason": "No caster"}
        
        # Create proc event
        proc_event = ProcEvent(
            event_type=TriggerCondition.ON_SPELL_CAST,
            source=caster,
            target=target,
            spell=context.get('spell'),
            damage=context.get('damage', 0),
            was_critical=context.get('was_critical', False)
        )
        
        # Process the proc
        if hasattr(caster, 'proc_manager'):
            triggered_procs = caster.proc_manager.process_event(proc_event)
            return {
                "success": True,
                "triggered_procs": triggered_procs,
                "proc_event": proc_event
            }
        
        return {"success": False, "reason": "No proc manager"}
    
    def _process_conditional_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a conditional effect."""
        # Conditions already checked, so execute the effect
        target = context.get('target')
        caster = context.get('caster')
        
        if isinstance(effect.value, dict):
            # Apply stat modifications or other effects
            if 'damage' in effect.value:
                damage = effect.value['damage']
                if effect.spell_power_coefficient > 0 and caster:
                    spell_power = caster.get_spell_power()
                    damage += spell_power * effect.spell_power_coefficient
                
                if target:
                    target.current_health -= damage
                    target.current_health = max(0, target.current_health)
                
                return {
                    "success": True,
                    "damage_dealt": damage,
                    "effect_type": "conditional_damage"
                }
        
        return {"success": True, "effect_type": "conditional_other"}
    
    def _process_chain_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a chain effect that jumps between targets."""
        caster = context.get('caster')
        initial_target = context.get('target')
        
        if not caster or not initial_target:
            return {"success": False, "reason": "Missing caster or target"}
        
        # Simulate chain effect
        targets_hit = [initial_target]
        total_damage = 0
        current_damage = context.get('damage', 0)
        
        for jump in range(min(effect.max_targets - 1, 3)):  # Limit for demo
            # Reduce damage for each jump
            current_damage *= (1.0 - effect.damage_reduction_per_jump)
            
            # Find next target (simplified - would need proper target finding)
            next_target = self._find_chain_target(targets_hit, effect.max_range)
            if not next_target:
                break
            
            targets_hit.append(next_target)
            
            # Apply damage
            if hasattr(next_target, 'current_health'):
                next_target.current_health -= current_damage
                next_target.current_health = max(0, next_target.current_health)
                total_damage += current_damage
        
        return {
            "success": True,
            "targets_hit": len(targets_hit),
            "total_chain_damage": total_damage,
            "effect_type": "chain"
        }
    
    def _process_stacking_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a stacking effect."""
        target = context.get('target')
        if not target:
            return {"success": False, "reason": "No target"}
        
        effect_key = f"{effect.name}_{id(target)}"
        current_stacks = self.active_effects.get(effect_key, {}).get('stacks', 0)
        
        # Add stack
        new_stacks = min(current_stacks + 1, effect.max_stacks)
        
        # Store effect state
        self.active_effects[effect_key] = {
            'stacks': new_stacks,
            'last_use': time.time(),
            'target': target,
            'effect': effect
        }
        
        # Calculate stacked value
        if isinstance(effect.value, (int, float)):
            total_value = effect.value * new_stacks
        else:
            total_value = effect.value
        
        return {
            "success": True,
            "stacks": new_stacks,
            "total_value": total_value,
            "effect_type": "stacking"
        }
    
    def _process_synergy_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a synergy effect that enhances other spells."""
        caster = context.get('caster')
        if not caster:
            return {"success": False, "reason": "No caster"}
        
        # Apply synergy bonus (simplified)
        synergy_data = {
            "bonus_damage": effect.value if isinstance(effect.value, (int, float)) else 0,
            "duration": effect.duration,
            "applies_to": effect.description
        }
        
        return {
            "success": True,
            "synergy_applied": synergy_data,
            "effect_type": "synergy"
        }
    
    def _find_chain_target(self, existing_targets: List[Any], max_range: float) -> Optional[Any]:
        """Find the next target for chain effects (simplified)."""
        # In a real implementation, this would search for nearby enemies
        # For demo purposes, we'll create a mock target
        from src.character import ModularCharacter
        
        if len(existing_targets) >= 3:  # Limit chain length for demo
            return None
        
        # Create a mock chain target
        chain_target = ModularCharacter(f"Chain Target {len(existing_targets)}")
        chain_target.stats.set_stat(StatType.HEALTH, 1000)
        chain_target.current_health = chain_target.get_max_health()
        
        return chain_target
    
    def _record_effect(self, effect: AdvancedSpellEffect, context: Dict[str, Any], result: Dict[str, Any]):
        """Record effect usage for tracking."""
        self.effect_history.append({
            'effect_name': effect.name,
            'effect_type': effect.effect_type.value,
            'timestamp': time.time(),
            'success': result.get('success', False),
            'context': {
                'caster': getattr(context.get('caster'), 'name', 'Unknown'),
                'target': getattr(context.get('target'), 'name', 'Unknown'),
                'spell': getattr(context.get('spell'), 'name', 'Unknown')
            },
            'result': result
        })
        
        # Update cooldown tracking
        if effect.cooldown > 0 and result.get('success'):
            effect_key = f"{effect.name}_{context.get('caster_id', 'unknown')}"
            if effect_key not in self.active_effects:
                self.active_effects[effect_key] = {}
            self.active_effects[effect_key]['last_use'] = time.time()
    
    def get_effect_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent effect history."""
        return self.effect_history[-limit:] if limit > 0 else self.effect_history.copy()
    
    def clear_history(self):
        """Clear effect history."""
        self.effect_history.clear()
    
    def get_active_stacking_effects(self) -> Dict[str, Dict[str, Any]]:
        """Get all active stacking effects."""
        stacking_effects = {}
        current_time = time.time()
        
        for effect_key, effect_data in self.active_effects.items():
            if 'stacks' in effect_data:
                effect = effect_data.get('effect')
                if effect and effect.duration > 0:
                    # Check if effect has expired
                    elapsed = current_time - effect_data.get('last_use', 0)
                    if elapsed < effect.duration:
                        stacking_effects[effect_key] = effect_data
        
        return stacking_effects
