"""
Modular spell system that integrates with the character stats system.
"""

from typing import Dict, List, Optional, Tuple
from enum import Enum
import random
import time
from src.stats.standalone_stats import StatType


class SpellSchool(Enum):
    """Spell schools for damage calculation."""
    FIRE = "fire"
    FROST = "frost"
    ARCANE = "arcane"
    SHADOW = "shadow"
    NATURE = "nature"
    HOLY = "holy"
    PHYSICAL = "physical"


class SpellResult:
    """Result of casting a spell."""
    
    def __init__(self, success: bool = True, damage: int = 0, healing: int = 0, 
                 was_critical: bool = False, was_resisted: bool = False, 
                 effects_applied: List[str] = None):
        self.success = success
        self.damage = damage
        self.healing = healing
        self.was_critical = was_critical
        self.was_resisted = was_resisted
        self.effects_applied = effects_applied or []
        self.cast_time = time.time()


class ModularSpell:
    """
    A spell that uses the modular character system for damage calculations.
    """
    
    def __init__(self, name: str, school: SpellSchool, base_damage: int = 0, 
                 base_healing: int = 0, cast_time: float = 0.0, cooldown: float = 0.0,
                 mana_cost: int = 0, spell_power_coefficient: float = 1.0,
                 can_crit: bool = True, requires_target: bool = True):
        self.name = name
        self.school = school
        self.base_damage = base_damage
        self.base_healing = base_healing
        self.cast_time = cast_time
        self.cooldown = cooldown
        self.mana_cost = mana_cost
        self.spell_power_coefficient = spell_power_coefficient
        self.can_crit = can_crit
        self.requires_target = requires_target
        self.last_cast_time = 0.0
    
    def can_cast(self, caster, target=None) -> Tuple[bool, str]:
        """Check if the spell can be cast."""
        current_time = time.time()
        
        # Check cooldown
        if current_time < self.last_cast_time + self.cooldown:
            remaining = self.last_cast_time + self.cooldown - current_time
            return False, f"On cooldown for {remaining:.1f} seconds"
        
        # Check mana
        if caster.current_mana < self.mana_cost:
            return False, "Not enough mana"
        
        # Check target requirement
        if self.requires_target and target is None:
            return False, "Requires a target"
        
        # Check global cooldown
        if current_time < caster.last_cast_time + caster.global_cooldown:
            return False, "Global cooldown active"
        
        return True, "Can cast"
    
    def calculate_damage(self, caster, target=None) -> int:
        """Calculate damage using the caster's stats."""
        if self.base_damage == 0:
            return 0
        
        # Get caster's spell power
        spell_power = caster.get_spell_power()
        
        # Calculate base damage with spell power scaling
        damage = self.base_damage + (spell_power * self.spell_power_coefficient)
        
        # Apply target resistances if target exists
        if target:
            resistance = target.get_resistance(self.school.value)
            resistance_reduction = resistance / (resistance + 300)  # WoW-like formula
            damage *= (1 - resistance_reduction)
        
        return int(damage)
    
    def calculate_healing(self, caster, target=None) -> int:
        """Calculate healing using the caster's stats."""
        if self.base_healing == 0:
            return 0
        
        # Get caster's spell power (healing power would be separate in real WoW)
        spell_power = caster.get_spell_power()
        
        # Calculate base healing with spell power scaling
        healing = self.base_healing + (spell_power * self.spell_power_coefficient)
        
        return int(healing)
    
    def check_critical_hit(self, caster) -> bool:
        """Check if the spell critically hits."""
        if not self.can_crit:
            return False
        
        crit_chance = caster.get_crit_chance()
        return random.random() < crit_chance
    
    def check_hit(self, caster, target=None) -> bool:
        """Check if the spell hits the target."""
        if target is None:
            return True  # Self-cast or AoE spells always hit
        
        hit_chance = caster.get_hit_chance()
        # In a real implementation, you'd also consider target's dodge/resist
        return random.random() < hit_chance
    
    def cast(self, caster, target=None) -> SpellResult:
        """Cast the spell and return the result."""
        # Check if spell can be cast
        can_cast, reason = self.can_cast(caster, target)
        if not can_cast:
            return SpellResult(success=False)
        
        # Consume mana
        caster.current_mana -= self.mana_cost
        
        # Update cast times
        current_time = time.time()
        self.last_cast_time = current_time
        caster.last_cast_time = current_time
        
        # Check if spell hits
        if not self.check_hit(caster, target):
            return SpellResult(success=True, damage=0, healing=0)
        
        # Calculate damage and healing
        damage = self.calculate_damage(caster, target)
        healing = self.calculate_healing(caster, target)
        
        # Check for critical hit
        was_critical = self.check_critical_hit(caster)
        if was_critical:
            damage = int(damage * 2.0)  # 2x damage for crits
            healing = int(healing * 1.5)  # 1.5x healing for crits
        
        # Apply damage/healing
        if damage > 0 and target:
            target.current_health -= damage
            target.current_health = max(0, target.current_health)
        
        if healing > 0 and target:
            target.current_health += healing
            target.current_health = min(target.get_max_health(), target.current_health)
        
        return SpellResult(
            success=True,
            damage=damage,
            healing=healing,
            was_critical=was_critical
        )


class SpellLibrary:
    """Collection of predefined spells."""
    
    @staticmethod
    def fireball() -> ModularSpell:
        """Classic fireball spell."""
        return ModularSpell(
            name="Fireball",
            school=SpellSchool.FIRE,
            base_damage=500,
            cast_time=3.0,
            cooldown=0.0,
            mana_cost=260,
            spell_power_coefficient=1.0,
            can_crit=True,
            requires_target=True
        )
    
    @staticmethod
    def frostbolt() -> ModularSpell:
        """Frostbolt with slowing effect."""
        return ModularSpell(
            name="Frostbolt",
            school=SpellSchool.FROST,
            base_damage=440,
            cast_time=2.5,
            cooldown=0.0,
            mana_cost=240,
            spell_power_coefficient=0.95,
            can_crit=True,
            requires_target=True
        )
    
    @staticmethod
    def arcane_missiles() -> ModularSpell:
        """Channeled arcane missiles."""
        return ModularSpell(
            name="Arcane Missiles",
            school=SpellSchool.ARCANE,
            base_damage=300,  # Per missile
            cast_time=5.0,  # Channel time
            cooldown=0.0,
            mana_cost=355,
            spell_power_coefficient=0.6,
            can_crit=True,
            requires_target=True
        )
    
    @staticmethod
    def heal() -> ModularSpell:
        """Basic healing spell."""
        return ModularSpell(
            name="Heal",
            school=SpellSchool.HOLY,
            base_healing=800,
            cast_time=2.5,
            cooldown=0.0,
            mana_cost=280,
            spell_power_coefficient=1.0,
            can_crit=True,
            requires_target=True
        )
    
    @staticmethod
    def arcane_power() -> ModularSpell:
        """Self-buff spell."""
        return ModularSpell(
            name="Arcane Power",
            school=SpellSchool.ARCANE,
            cast_time=0.0,  # Instant
            cooldown=180.0,  # 3 minute cooldown
            mana_cost=0,
            spell_power_coefficient=0.0,
            can_crit=False,
            requires_target=False
        )


class CombatSimulator:
    """Simple combat simulator using the modular spell system."""
    
    def __init__(self):
        self.combat_log: List[str] = []
    
    def log(self, message: str) -> None:
        """Add a message to the combat log."""
        self.combat_log.append(f"[{time.time():.1f}] {message}")
        print(message)
    
    def simulate_spell_cast(self, caster, spell: ModularSpell, target=None) -> SpellResult:
        """Simulate casting a spell and log the results."""
        self.log(f"{caster.name} begins casting {spell.name}")
        
        # Simulate cast time (in a real game, this would be handled differently)
        if spell.cast_time > 0:
            self.log(f"  Casting for {spell.cast_time} seconds...")
        
        # Cast the spell
        result = spell.cast(caster, target)
        
        if not result.success:
            self.log(f"  {spell.name} failed to cast!")
            return result
        
        # Log results
        if result.damage > 0:
            crit_text = " (Critical Hit!)" if result.was_critical else ""
            target_name = target.name if target else "target"
            self.log(f"  {spell.name} hits {target_name} for {result.damage} {spell.school.value} damage{crit_text}")
        
        if result.healing > 0:
            crit_text = " (Critical Heal!)" if result.was_critical else ""
            target_name = target.name if target else "target"
            self.log(f"  {spell.name} heals {target_name} for {result.healing} health{crit_text}")
        
        return result
    
    def get_combat_log(self) -> List[str]:
        """Get the full combat log."""
        return self.combat_log.copy()
    
    def clear_log(self) -> None:
        """Clear the combat log."""
        self.combat_log.clear()
