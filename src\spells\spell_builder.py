"""
Spell builder that creates ModularSpell objects from configurations.
"""

import random
from typing import Dict, List, Any, Optional, Tuple
from .modular_spells import Modular<PERSON>pell, SpellSchool, SpellResult
from .spell_schema import SpellConfig, SpellEffect, SpellSchoolType, EffectType, TargetType
from .spell_validator import SpellValidator
from src.core.effects import Buff, Debuff
from src.stats.standalone_stats import StatType


class SpellBuilder:
    """Builds ModularSpell objects from SpellConfig configurations."""
    
    def __init__(self):
        self.validator = SpellValidator()
        
        # Map schema enums to modular spell enums
        self.school_mapping = {
            SpellSchoolType.FIRE: SpellSchool.FIRE,
            SpellSchoolType.FROST: SpellSchool.FROST,
            SpellSchoolType.ARCANE: SpellSchool.ARCANE,
            SpellSchoolType.SHADOW: SpellSchool.SHADOW,
            SpellSchoolType.NATURE: SpellSchool.NATURE,
            SpellSchoolType.HOLY: SpellSchool.HOLY,
            SpellSchoolType.PHYSICAL: SpellSchool.PHYSICAL
        }
    
    def build_spell(self, config: SpellConfig, validate: bool = True) -> ModularSpell:
        """
        Build a ModularSpell from a SpellConfig.
        
        Args:
            config: The spell configuration
            validate: Whether to validate the config first
            
        Returns:
            ModularSpell object
            
        Raises:
            ValueError: If validation fails or config is invalid
        """
        if validate:
            is_valid, errors = self.validator.validate_spell_config(config)
            if not is_valid:
                raise ValueError(f"Invalid spell config: {'; '.join(errors)}")
        
        # Create the base spell
        spell = ConfigurableSpell(
            name=config.name,
            school=self.school_mapping[config.school],
            base_damage=self._get_damage_value(config.base_damage),
            base_healing=self._get_damage_value(config.base_healing),
            cast_time=config.cast_time,
            cooldown=config.cooldown,
            mana_cost=config.mana_cost,
            spell_power_coefficient=config.spell_power_coefficient,
            can_crit=config.can_crit,
            requires_target=self._requires_target(config.target_type),
            config=config  # Store the full config for advanced features
        )
        
        return spell
    
    def build_spell_from_dict(self, spell_data: Dict[str, Any], validate: bool = True) -> ModularSpell:
        """
        Build a ModularSpell from dictionary data.
        
        Args:
            spell_data: Dictionary containing spell configuration
            validate: Whether to validate the data first
            
        Returns:
            ModularSpell object
            
        Raises:
            ValueError: If validation fails or data is invalid
        """
        if validate:
            is_valid, errors, config = self.validator.validate_spell_from_dict(spell_data)
            if not is_valid:
                raise ValueError(f"Invalid spell data: {'; '.join(errors)}")
        else:
            from .spell_schema import create_spell_config_from_dict
            config = create_spell_config_from_dict(spell_data)
        
        return self.build_spell(config, validate=False)
    
    def _get_damage_value(self, damage_config) -> int:
        """Convert damage config to a single value."""
        if isinstance(damage_config, list):
            return int(sum(damage_config) / len(damage_config))  # Use average
        return int(damage_config)
    
    def _requires_target(self, target_type: TargetType) -> bool:
        """Determine if the spell requires a target."""
        return target_type not in [TargetType.SELF, TargetType.NO_TARGET, TargetType.AOE_ALL]


class ConfigurableSpell(ModularSpell):
    """
    Extended ModularSpell that supports configuration-based effects.
    """
    
    def __init__(self, name: str, school: SpellSchool, base_damage: int = 0,
                 base_healing: int = 0, cast_time: float = 0.0, cooldown: float = 0.0,
                 mana_cost: int = 0, spell_power_coefficient: float = 1.0,
                 can_crit: bool = True, requires_target: bool = True,
                 config: Optional[SpellConfig] = None):
        
        super().__init__(
            name=name,
            school=school,
            base_damage=base_damage,
            base_healing=base_healing,
            cast_time=cast_time,
            cooldown=cooldown,
            mana_cost=mana_cost,
            spell_power_coefficient=spell_power_coefficient,
            can_crit=can_crit,
            requires_target=requires_target
        )
        
        self.config = config
        self.description = config.description if config else ""
        
        # Handle damage ranges
        if config and isinstance(config.base_damage, list):
            self.damage_range = config.base_damage
        else:
            self.damage_range = None
            
        if config and isinstance(config.base_healing, list):
            self.healing_range = config.base_healing
        else:
            self.healing_range = None
    
    def calculate_damage(self, caster, target=None) -> int:
        """Calculate damage with support for damage ranges."""
        if self.base_damage == 0:
            return 0
        
        # Use damage range if available
        if self.damage_range:
            min_damage, max_damage = self.damage_range
            base_damage = random.randint(min_damage, max_damage)
        else:
            base_damage = self.base_damage
        
        # Get caster's spell power
        spell_power = caster.get_spell_power()
        
        # Calculate damage with spell power scaling
        damage = base_damage + (spell_power * self.spell_power_coefficient)
        
        # Apply target resistances if target exists
        if target:
            resistance = target.get_resistance(self.school.value)
            resistance_reduction = resistance / (resistance + 300)  # WoW-like formula
            damage *= (1 - resistance_reduction)
        
        return int(damage)
    
    def calculate_healing(self, caster, target=None) -> int:
        """Calculate healing with support for healing ranges."""
        if self.base_healing == 0:
            return 0
        
        # Use healing range if available
        if self.healing_range:
            min_healing, max_healing = self.healing_range
            base_healing = random.randint(min_healing, max_healing)
        else:
            base_healing = self.base_healing
        
        # Get caster's spell power
        spell_power = caster.get_spell_power()
        
        # Calculate healing with spell power scaling
        healing = base_healing + (spell_power * self.spell_power_coefficient)
        
        return int(healing)
    
    def cast(self, caster, target=None) -> SpellResult:
        """Cast the spell with configuration-based effects."""
        # Use parent cast method for basic functionality
        result = super().cast(caster, target)
        
        if not result.success:
            return result
        
        # Apply configuration-based effects
        if self.config and self.config.effects:
            effects_applied = []
            
            for effect in self.config.effects:
                if random.random() <= effect.chance:
                    applied_effect = self._apply_effect(effect, caster, target, result)
                    if applied_effect:
                        effects_applied.append(applied_effect)
            
            # Update result with applied effects
            result.effects_applied.extend(effects_applied)
        
        return result
    
    def _apply_effect(self, effect: SpellEffect, caster, target, spell_result: SpellResult) -> Optional[str]:
        """Apply a single effect from the configuration."""
        try:
            if effect.type == EffectType.DAMAGE_OVER_TIME:
                return self._apply_dot_effect(effect, target)
            
            elif effect.type == EffectType.HEAL_OVER_TIME:
                return self._apply_hot_effect(effect, target)
            
            elif effect.type == EffectType.BUFF:
                return self._apply_buff_effect(effect, target)
            
            elif effect.type == EffectType.DEBUFF:
                return self._apply_debuff_effect(effect, target)
            
            elif effect.type == EffectType.DIRECT_DAMAGE:
                return self._apply_direct_damage_effect(effect, target, spell_result)
            
            elif effect.type == EffectType.DIRECT_HEAL:
                return self._apply_direct_heal_effect(effect, target, spell_result)
            
            # Add more effect types as needed
            
        except Exception as e:
            print(f"Error applying effect {effect.type}: {e}")
            return None
        
        return None
    
    def _apply_dot_effect(self, effect: SpellEffect, target) -> Optional[str]:
        """Apply a damage over time effect."""
        if not target or not isinstance(effect.value, (int, float)):
            return None
        
        debuff = Debuff(
            name=f"{self.name} DoT",
            duration=effect.duration,
            damage_per_tick=int(effect.value),
            tick_interval=effect.tick_interval
        )
        
        if hasattr(target, 'apply_debuff'):
            target.apply_debuff(debuff)
            return f"{debuff.name} applied"
        
        return None
    
    def _apply_hot_effect(self, effect: SpellEffect, target) -> Optional[str]:
        """Apply a heal over time effect."""
        # Similar to DoT but for healing
        # This would need a HoT effect class
        return f"HoT effect applied (not fully implemented)"
    
    def _apply_buff_effect(self, effect: SpellEffect, target) -> Optional[str]:
        """Apply a buff effect."""
        if not target or not isinstance(effect.value, dict):
            return None
        
        # Convert effect value to stat modifiers
        stat_modifiers = {}
        for stat_name, value in effect.value.items():
            # Convert string stat names to StatType if possible
            if stat_name == "spell_power":
                stat_modifiers["spell_power"] = value
            elif stat_name == "mana":
                stat_modifiers["mana"] = value
            elif stat_name == "crit_chance":
                stat_modifiers["crit"] = value
            else:
                stat_modifiers[stat_name] = value
        
        buff = Buff(
            name=f"{self.name} Buff",
            duration=effect.duration,
            stat_modifiers=stat_modifiers
        )
        
        if hasattr(target, 'apply_buff'):
            target.apply_buff(buff)
            return f"{buff.name} applied"
        
        return None
    
    def _apply_debuff_effect(self, effect: SpellEffect, target) -> Optional[str]:
        """Apply a debuff effect."""
        if not target or not isinstance(effect.value, dict):
            return None
        
        # Create a debuff with the specified properties
        debuff = Debuff(
            name=f"{self.name} Debuff",
            duration=effect.duration
        )
        
        # Add custom properties from the effect value
        for prop_name, prop_value in effect.value.items():
            setattr(debuff, prop_name, prop_value)
        
        if hasattr(target, 'apply_debuff'):
            target.apply_debuff(debuff)
            return f"{debuff.name} applied"
        
        return None
    
    def _apply_direct_damage_effect(self, effect: SpellEffect, target, spell_result: SpellResult) -> Optional[str]:
        """Apply additional direct damage."""
        if not target or not isinstance(effect.value, (int, float)):
            return None
        
        additional_damage = int(effect.value)
        spell_result.damage += additional_damage
        target.current_health -= additional_damage
        target.current_health = max(0, target.current_health)
        
        return f"Additional {additional_damage} damage"
    
    def _apply_direct_heal_effect(self, effect: SpellEffect, target, spell_result: SpellResult) -> Optional[str]:
        """Apply additional direct healing."""
        if not target or not isinstance(effect.value, (int, float)):
            return None
        
        additional_healing = int(effect.value)
        spell_result.healing += additional_healing
        target.current_health += additional_healing
        target.current_health = min(target.get_max_health(), target.current_health)
        
        return f"Additional {additional_healing} healing"
    
    def get_description(self) -> str:
        """Get a detailed description of the spell."""
        if self.description:
            return self.description
        
        # Generate description from configuration
        desc_parts = []
        
        if self.base_damage > 0:
            if self.damage_range:
                desc_parts.append(f"Deals {self.damage_range[0]}-{self.damage_range[1]} {self.school.value} damage")
            else:
                desc_parts.append(f"Deals {self.base_damage} {self.school.value} damage")
        
        if self.base_healing > 0:
            if self.healing_range:
                desc_parts.append(f"Heals for {self.healing_range[0]}-{self.healing_range[1]}")
            else:
                desc_parts.append(f"Heals for {self.base_healing}")
        
        if self.config and self.config.effects:
            for effect in self.config.effects:
                if effect.type == EffectType.DAMAGE_OVER_TIME:
                    desc_parts.append(f"DoT: {effect.value} damage every {effect.tick_interval}s for {effect.duration}s")
                elif effect.type == EffectType.BUFF and isinstance(effect.value, dict):
                    buff_desc = ", ".join([f"+{v} {k}" for k, v in effect.value.items()])
                    desc_parts.append(f"Buff: {buff_desc} for {effect.duration}s")
        
        return ". ".join(desc_parts) if desc_parts else "No description available"


class AdvancedSpellTemplateLibrary:
    """Library of advanced spell templates with complex effects."""

    @staticmethod
    def get_all_advanced_templates() -> Dict[str, Dict[str, Any]]:
        """Get all available advanced spell templates."""
        return {
            "living_bomb": AdvancedSpellTemplateLibrary.living_bomb_template(),
            "chain_lightning": AdvancedSpellTemplateLibrary.chain_lightning_template(),
            "ignite_fireball": AdvancedSpellTemplateLibrary.ignite_fireball_template(),
            "execute": AdvancedSpellTemplateLibrary.execute_template(),
            "hot_streak": AdvancedSpellTemplateLibrary.hot_streak_template(),
            "arcane_blast": AdvancedSpellTemplateLibrary.arcane_blast_template(),
            "polymorph": AdvancedSpellTemplateLibrary.polymorph_template(),
            "mana_burn": AdvancedSpellTemplateLibrary.mana_burn_template(),
            "spell_steal": AdvancedSpellTemplateLibrary.spell_steal_template(),
            "combustion": AdvancedSpellTemplateLibrary.combustion_template()
        }

    @staticmethod
    def living_bomb_template() -> Dict[str, Any]:
        """Living Bomb - DoT that explodes when it expires."""
        return {
            "name": "Living Bomb",
            "description": "DoT that explodes when it expires or is dispelled",
            "school": "fire",
            "cast_time": 2.0,
            "cooldown": 0.0,
            "mana_cost": 220,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": 0,
            "spell_power_coefficient": 0.0,
            "can_crit": False,
            "effects": [
                {
                    "type": "damage_over_time",
                    "value": 92,
                    "duration": 12.0,
                    "tick_interval": 3.0,
                    "chance": 1.0
                }
            ],
            "advanced_effects": [
                {
                    "effect_type": "conditional_effect",
                    "name": "Living Bomb Explosion",
                    "description": "Explodes when DoT expires",
                    "value": {"damage": 690, "aoe_radius": 10.0},
                    "conditions": [
                        {
                            "condition_type": "spell_effect_expires",
                            "operator": "==",
                            "value": "damage_over_time"
                        }
                    ],
                    "spell_power_coefficient": 0.8,
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def chain_lightning_template() -> Dict[str, Any]:
        """Chain Lightning - Jumps between targets."""
        return {
            "name": "Chain Lightning",
            "description": "Lightning that jumps to nearby enemies",
            "school": "nature",
            "cast_time": 2.0,
            "cooldown": 0.0,
            "mana_cost": 380,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": [450, 550],
            "spell_power_coefficient": 0.9,
            "can_crit": True,
            "effects": [],
            "advanced_effects": [
                {
                    "effect_type": "chain_effect",
                    "name": "Lightning Chain",
                    "description": "Jumps to up to 3 additional targets",
                    "value": 1.0,  # Full damage to first target
                    "max_targets": 4,
                    "damage_reduction_per_jump": 0.15,
                    "max_range": 12.0,
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def ignite_fireball_template() -> Dict[str, Any]:
        """Fireball with Ignite proc."""
        return {
            "name": "Fireball with Ignite",
            "description": "Fireball that can proc Ignite on critical hits",
            "school": "fire",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 260,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": [500, 600],
            "spell_power_coefficient": 1.0,
            "can_crit": True,
            "effects": [],
            "advanced_effects": [
                {
                    "effect_type": "proc_effect",
                    "name": "Ignite",
                    "description": "40% of crit damage as DoT",
                    "value": {"dot_percentage": 0.4},
                    "duration": 4.0,
                    "conditions": [
                        {
                            "condition_type": "spell_critical",
                            "operator": "==",
                            "value": True
                        }
                    ],
                    "chance": 1.0,
                    "max_stacks": 5
                }
            ]
        }

    @staticmethod
    def execute_template() -> Dict[str, Any]:
        """Execute - More damage vs low health targets."""
        return {
            "name": "Execute",
            "description": "Deals increased damage to low health enemies",
            "school": "physical",
            "cast_time": 0.0,
            "cooldown": 6.0,
            "mana_cost": 150,
            "target_type": "single_enemy",
            "range": 5.0,
            "base_damage": [200, 250],
            "spell_power_coefficient": 0.5,
            "can_crit": True,
            "effects": [],
            "advanced_effects": [
                {
                    "effect_type": "conditional_effect",
                    "name": "Execute Bonus",
                    "description": "300% damage vs targets below 20% health",
                    "value": {"damage_multiplier": 3.0},
                    "conditions": [
                        {
                            "condition_type": "target_health_percent",
                            "operator": "<=",
                            "value": 20
                        }
                    ],
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def hot_streak_template() -> Dict[str, Any]:
        """Hot Streak - Synergy effect for fire spells."""
        return {
            "name": "Hot Streak Fireball",
            "description": "Fireball that can trigger Hot Streak",
            "school": "fire",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 260,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": [500, 600],
            "spell_power_coefficient": 1.0,
            "can_crit": True,
            "effects": [],
            "advanced_effects": [
                {
                    "effect_type": "synergy_effect",
                    "name": "Hot Streak",
                    "description": "Next Pyroblast is instant after 2 crits",
                    "value": {"instant_cast": True, "spell_name": "Pyroblast"},
                    "duration": 10.0,
                    "conditions": [
                        {
                            "condition_type": "spell_critical",
                            "operator": "==",
                            "value": True
                        }
                    ],
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def arcane_blast_template() -> Dict[str, Any]:
        """Arcane Blast - Stacking spell."""
        return {
            "name": "Arcane Blast",
            "description": "Stacking spell that increases damage and mana cost",
            "school": "arcane",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 195,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": [372, 428],
            "spell_power_coefficient": 0.714,
            "can_crit": True,
            "effects": [],
            "advanced_effects": [
                {
                    "effect_type": "stacking_effect",
                    "name": "Arcane Blast Stack",
                    "description": "Each stack increases damage and mana cost",
                    "value": {"damage_bonus": 0.75, "mana_multiplier": 1.75},
                    "duration": 6.0,
                    "max_stacks": 4,
                    "chance": 1.0
                }
            ]
        }


class SpellTemplateLibrary:
    """Library of common spell templates that users can customize."""

    @staticmethod
    def get_all_templates() -> Dict[str, Dict[str, Any]]:
        """Get all available spell templates."""
        return {
            "direct_damage": SpellTemplateLibrary.direct_damage_template(),
            "damage_over_time": SpellTemplateLibrary.dot_template(),
            "direct_heal": SpellTemplateLibrary.direct_heal_template(),
            "heal_over_time": SpellTemplateLibrary.hot_template(),
            "buff_spell": SpellTemplateLibrary.buff_template(),
            "debuff_spell": SpellTemplateLibrary.debuff_template(),
            "channeled_damage": SpellTemplateLibrary.channeled_template(),
            "aoe_damage": SpellTemplateLibrary.aoe_template(),
            "instant_nuke": SpellTemplateLibrary.instant_nuke_template(),
            "utility_spell": SpellTemplateLibrary.utility_template()
        }

    @staticmethod
    def direct_damage_template() -> Dict[str, Any]:
        """Template for direct damage spells."""
        return {
            "name": "New Direct Damage Spell",
            "description": "A spell that deals direct damage to a single target.",
            "school": "fire",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 200,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": [400, 500],
            "spell_power_coefficient": 1.0,
            "can_crit": True,
            "effects": []
        }

    @staticmethod
    def dot_template() -> Dict[str, Any]:
        """Template for damage over time spells."""
        return {
            "name": "New DoT Spell",
            "description": "A spell that deals damage over time.",
            "school": "shadow",
            "cast_time": 1.5,
            "cooldown": 0.0,
            "mana_cost": 150,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": 0,
            "spell_power_coefficient": 0.0,
            "can_crit": False,
            "effects": [
                {
                    "type": "damage_over_time",
                    "value": 100,
                    "duration": 15.0,
                    "tick_interval": 3.0,
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def direct_heal_template() -> Dict[str, Any]:
        """Template for direct healing spells."""
        return {
            "name": "New Heal Spell",
            "description": "A spell that heals a single target.",
            "school": "holy",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 250,
            "target_type": "single_ally",
            "range": 40.0,
            "base_healing": [600, 750],
            "spell_power_coefficient": 1.0,
            "can_crit": True,
            "effects": []
        }

    @staticmethod
    def hot_template() -> Dict[str, Any]:
        """Template for heal over time spells."""
        return {
            "name": "New HoT Spell",
            "description": "A spell that heals over time.",
            "school": "nature",
            "cast_time": 2.0,
            "cooldown": 0.0,
            "mana_cost": 180,
            "target_type": "single_ally",
            "range": 40.0,
            "base_healing": 0,
            "spell_power_coefficient": 0.0,
            "can_crit": False,
            "effects": [
                {
                    "type": "heal_over_time",
                    "value": 80,
                    "duration": 12.0,
                    "tick_interval": 3.0,
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def buff_template() -> Dict[str, Any]:
        """Template for buff spells."""
        return {
            "name": "New Buff Spell",
            "description": "A spell that enhances the target.",
            "school": "arcane",
            "cast_time": 3.0,
            "cooldown": 0.0,
            "mana_cost": 100,
            "target_type": "single_ally",
            "range": 30.0,
            "base_damage": 0,
            "spell_power_coefficient": 0.0,
            "can_crit": False,
            "effects": [
                {
                    "type": "buff",
                    "value": {"spell_power": 50, "mana": 200},
                    "duration": 300.0,
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def debuff_template() -> Dict[str, Any]:
        """Template for debuff spells."""
        return {
            "name": "New Debuff Spell",
            "description": "A spell that weakens the target.",
            "school": "shadow",
            "cast_time": 1.5,
            "cooldown": 0.0,
            "mana_cost": 120,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": 0,
            "spell_power_coefficient": 0.0,
            "can_crit": False,
            "effects": [
                {
                    "type": "debuff",
                    "value": {"movement_speed": -0.5, "damage_reduction": 0.2},
                    "duration": 10.0,
                    "chance": 1.0
                }
            ]
        }

    @staticmethod
    def channeled_template() -> Dict[str, Any]:
        """Template for channeled spells."""
        return {
            "name": "New Channeled Spell",
            "description": "A channeled spell that deals damage over time.",
            "school": "arcane",
            "cast_time": 5.0,
            "cooldown": 0.0,
            "mana_cost": 300,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": 250,
            "spell_power_coefficient": 0.6,
            "can_crit": True,
            "channeled": True,
            "channel_ticks": 5,
            "effects": []
        }

    @staticmethod
    def aoe_template() -> Dict[str, Any]:
        """Template for area of effect spells."""
        return {
            "name": "New AoE Spell",
            "description": "A spell that damages all enemies in an area.",
            "school": "fire",
            "cast_time": 3.0,
            "cooldown": 0.0,
            "mana_cost": 400,
            "target_type": "ground_target",
            "range": 30.0,
            "aoe_radius": 8.0,
            "base_damage": [300, 400],
            "spell_power_coefficient": 0.8,
            "can_crit": True,
            "effects": []
        }

    @staticmethod
    def instant_nuke_template() -> Dict[str, Any]:
        """Template for instant high-damage spells."""
        return {
            "name": "New Instant Nuke",
            "description": "An instant spell that deals high damage.",
            "school": "arcane",
            "cast_time": 0.0,
            "cooldown": 8.0,
            "mana_cost": 350,
            "target_type": "single_enemy",
            "range": 30.0,
            "base_damage": [250, 300],
            "spell_power_coefficient": 0.8,
            "can_crit": True,
            "effects": []
        }

    @staticmethod
    def utility_template() -> Dict[str, Any]:
        """Template for utility spells."""
        return {
            "name": "New Utility Spell",
            "description": "A utility spell with special effects.",
            "school": "arcane",
            "cast_time": 1.5,
            "cooldown": 30.0,
            "mana_cost": 150,
            "target_type": "single_any",
            "range": 30.0,
            "base_damage": 0,
            "spell_power_coefficient": 0.0,
            "can_crit": False,
            "effects": [
                {
                    "type": "debuff",
                    "value": {"silenced": True},
                    "duration": 5.0,
                    "chance": 1.0
                }
            ]
        }
