"""
Command-line interface for creating spells.
Provides an interactive way to create and test spells.
"""

import json
import os
from typing import Dict, Any, Optional
from .spell_builder import Spell<PERSON><PERSON>er, SpellTemplateLibrary
from .spell_validator import SpellValidator
from .spell_schema import SpellSchoolType, TargetType, EffectType
from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.stats.standalone_stats import StatType


class SpellCreatorCLI:
    """Interactive command-line spell creator."""
    
    def __init__(self):
        self.builder = SpellBuilder()
        self.validator = SpellValidator()
        self.templates = SpellTemplateLibrary.get_all_templates()
        self.created_spells = {}
        
    def run(self):
        """Run the interactive spell creator."""
        print("🧙‍♂️ WoW Simulator - Spell Creator")
        print("=" * 40)
        print("Create custom spells for your WoW simulator!")
        print()
        
        while True:
            self.show_main_menu()
            choice = input("Enter your choice: ").strip()
            
            if choice == '1':
                self.create_spell_from_template()
            elif choice == '2':
                self.create_spell_from_scratch()
            elif choice == '3':
                self.test_spell()
            elif choice == '4':
                self.list_created_spells()
            elif choice == '5':
                self.save_spells_to_file()
            elif choice == '6':
                self.load_spells_from_file()
            elif choice == '7':
                self.validate_spell()
            elif choice == '8':
                self.show_templates()
            elif choice == '0':
                print("Goodbye! 👋")
                break
            else:
                print("Invalid choice. Please try again.")
            
            input("\nPress Enter to continue...")
    
    def show_main_menu(self):
        """Display the main menu."""
        print("\n" + "=" * 40)
        print("Main Menu:")
        print("1. Create spell from template")
        print("2. Create spell from scratch")
        print("3. Test a spell")
        print("4. List created spells")
        print("5. Save spells to file")
        print("6. Load spells from file")
        print("7. Validate a spell")
        print("8. Show available templates")
        print("0. Exit")
        print("=" * 40)
    
    def show_templates(self):
        """Show available spell templates."""
        print("\n📋 Available Spell Templates:")
        print("-" * 30)
        
        for i, (template_name, template_data) in enumerate(self.templates.items(), 1):
            print(f"{i}. {template_name.replace('_', ' ').title()}")
            print(f"   {template_data['description']}")
            print()
    
    def create_spell_from_template(self):
        """Create a spell using a template."""
        print("\n🎯 Create Spell from Template")
        print("-" * 30)
        
        # Show templates
        template_names = list(self.templates.keys())
        for i, template_name in enumerate(template_names, 1):
            print(f"{i}. {template_name.replace('_', ' ').title()}")
        
        try:
            choice = int(input("\nSelect template (number): ")) - 1
            if 0 <= choice < len(template_names):
                template_name = template_names[choice]
                template_data = self.templates[template_name].copy()
                
                print(f"\nUsing template: {template_name.replace('_', ' ').title()}")
                
                # Customize the template
                spell_data = self.customize_spell_data(template_data)
                
                # Create the spell
                self.create_and_validate_spell(spell_data)
            else:
                print("Invalid template selection.")
                
        except ValueError:
            print("Please enter a valid number.")
    
    def create_spell_from_scratch(self):
        """Create a spell from scratch."""
        print("\n⚡ Create Spell from Scratch")
        print("-" * 30)
        
        spell_data = {}
        
        # Basic properties
        spell_data['name'] = input("Spell name: ").strip()
        spell_data['description'] = input("Description: ").strip()
        
        # School
        schools = [school.value for school in SpellSchoolType]
        print(f"\nAvailable schools: {', '.join(schools)}")
        spell_data['school'] = input("School: ").strip().lower()
        
        # Casting properties
        spell_data['cast_time'] = float(input("Cast time (seconds): "))
        spell_data['cooldown'] = float(input("Cooldown (seconds): "))
        spell_data['mana_cost'] = int(input("Mana cost: "))
        
        # Target type
        targets = [target.value for target in TargetType]
        print(f"\nAvailable target types: {', '.join(targets)}")
        spell_data['target_type'] = input("Target type: ").strip().lower()
        
        # Damage/Healing
        damage_input = input("Base damage (single number or min,max): ").strip()
        if ',' in damage_input:
            min_dmg, max_dmg = map(int, damage_input.split(','))
            spell_data['base_damage'] = [min_dmg, max_dmg]
        elif damage_input:
            spell_data['base_damage'] = int(damage_input)
        else:
            spell_data['base_damage'] = 0
        
        healing_input = input("Base healing (single number or min,max): ").strip()
        if ',' in healing_input:
            min_heal, max_heal = map(int, healing_input.split(','))
            spell_data['base_healing'] = [min_heal, max_heal]
        elif healing_input:
            spell_data['base_healing'] = int(healing_input)
        else:
            spell_data['base_healing'] = 0
        
        # Spell power coefficient
        spell_data['spell_power_coefficient'] = float(input("Spell power coefficient (0.0-3.0): ") or "1.0")
        
        # Can crit
        spell_data['can_crit'] = input("Can critically hit? (y/n): ").lower().startswith('y')
        
        # Range
        spell_data['range'] = float(input("Range (yards): ") or "30")
        
        # Effects (simplified)
        if input("Add effects? (y/n): ").lower().startswith('y'):
            spell_data['effects'] = self.create_simple_effects()
        else:
            spell_data['effects'] = []
        
        # Create the spell
        self.create_and_validate_spell(spell_data)
    
    def customize_spell_data(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """Allow user to customize template data."""
        print("\nCustomize the spell (press Enter to keep default):")
        
        # Name
        new_name = input(f"Name [{template_data['name']}]: ").strip()
        if new_name:
            template_data['name'] = new_name
        
        # Description
        new_desc = input(f"Description [{template_data['description']}]: ").strip()
        if new_desc:
            template_data['description'] = new_desc
        
        # Damage
        if template_data.get('base_damage', 0) > 0:
            current_damage = template_data['base_damage']
            if isinstance(current_damage, list):
                damage_str = f"{current_damage[0]}-{current_damage[1]}"
            else:
                damage_str = str(current_damage)
            
            new_damage = input(f"Damage [{damage_str}]: ").strip()
            if new_damage:
                if '-' in new_damage or ',' in new_damage:
                    separator = '-' if '-' in new_damage else ','
                    min_dmg, max_dmg = map(int, new_damage.split(separator))
                    template_data['base_damage'] = [min_dmg, max_dmg]
                else:
                    template_data['base_damage'] = int(new_damage)
        
        # Mana cost
        new_mana = input(f"Mana cost [{template_data['mana_cost']}]: ").strip()
        if new_mana:
            template_data['mana_cost'] = int(new_mana)
        
        # Cast time
        new_cast_time = input(f"Cast time [{template_data['cast_time']}]: ").strip()
        if new_cast_time:
            template_data['cast_time'] = float(new_cast_time)
        
        return template_data
    
    def create_simple_effects(self) -> list:
        """Create simple effects interactively."""
        effects = []
        
        while True:
            print("\nEffect types: damage_over_time, buff, debuff")
            effect_type = input("Effect type (or 'done' to finish): ").strip().lower()
            
            if effect_type == 'done':
                break
            
            if effect_type == 'damage_over_time':
                damage = int(input("Damage per tick: "))
                duration = float(input("Duration (seconds): "))
                interval = float(input("Tick interval (seconds): ") or "3")
                
                effects.append({
                    "type": "damage_over_time",
                    "value": damage,
                    "duration": duration,
                    "tick_interval": interval,
                    "chance": 1.0
                })
            
            elif effect_type in ['buff', 'debuff']:
                print("Enter stat modifications (e.g., spell_power:50, mana:200)")
                stats_input = input("Stats: ").strip()
                
                stat_mods = {}
                if stats_input:
                    for stat_pair in stats_input.split(','):
                        if ':' in stat_pair:
                            stat_name, stat_value = stat_pair.split(':')
                            stat_mods[stat_name.strip()] = float(stat_value.strip())
                
                duration = float(input("Duration (seconds): "))
                
                effects.append({
                    "type": effect_type,
                    "value": stat_mods,
                    "duration": duration,
                    "chance": 1.0
                })
        
        return effects
    
    def create_and_validate_spell(self, spell_data: Dict[str, Any]):
        """Create and validate a spell from data."""
        try:
            # Validate first
            is_valid, errors, config = self.validator.validate_spell_from_dict(spell_data)
            
            if not is_valid:
                print(f"\n❌ Spell validation failed:")
                for error in errors:
                    print(f"  • {error}")
                
                # Show balance suggestions
                if config:
                    suggestions = self.validator.suggest_balance_fixes(config)
                    if suggestions:
                        print("\n💡 Balance suggestions:")
                        for suggestion in suggestions:
                            print(f"  • {suggestion}")
                
                return
            
            # Create the spell
            spell = self.builder.build_spell_from_dict(spell_data)
            
            # Store it
            self.created_spells[spell.name] = {
                'spell': spell,
                'data': spell_data
            }
            
            print(f"\n✅ Successfully created spell: {spell.name}")
            print(f"Description: {spell.get_description()}")
            
        except Exception as e:
            print(f"\n❌ Error creating spell: {e}")
    
    def test_spell(self):
        """Test a created spell."""
        if not self.created_spells:
            print("\nNo spells created yet. Create a spell first!")
            return
        
        print("\n🧪 Test Spell")
        print("-" * 20)
        
        # List spells
        spell_names = list(self.created_spells.keys())
        for i, name in enumerate(spell_names, 1):
            print(f"{i}. {name}")
        
        try:
            choice = int(input("\nSelect spell to test: ")) - 1
            if 0 <= choice < len(spell_names):
                spell_name = spell_names[choice]
                spell = self.created_spells[spell_name]['spell']
                
                # Create test characters
                mage = ModularCharacter("Test Mage")
                mage.stats.set_stat(StatType.SPELL_POWER, 250)
                mage.stats.set_stat(StatType.CRIT_CHANCE, 0.15)
                mage.current_mana = 5000
                
                target = ModularCharacter("Test Target")
                target.stats.set_stat(StatType.HEALTH, 3000)
                target.current_health = target.get_max_health()
                
                # Test the spell
                print(f"\nTesting {spell.name}:")
                print(f"Caster: Spell Power {mage.get_spell_power()}, Crit {mage.get_crit_chance()*100:.1f}%")
                
                # Calculate theoretical values
                damage = spell.calculate_damage(mage, target)
                healing = spell.calculate_healing(mage, target)
                
                print(f"Theoretical damage: {damage}")
                if healing > 0:
                    print(f"Theoretical healing: {healing}")
                print(f"Mana cost: {spell.mana_cost}")
                print(f"Cast time: {spell.cast_time}s")
                
                if spell.mana_cost > 0:
                    dpm = damage / spell.mana_cost
                    print(f"Damage per mana: {dpm:.2f}")
                
                cast_cycle = max(spell.cast_time, 1.5)  # GCD
                dps = damage / cast_cycle
                print(f"Theoretical DPS: {dps:.1f}")
                
            else:
                print("Invalid selection.")
                
        except ValueError:
            print("Please enter a valid number.")
    
    def list_created_spells(self):
        """List all created spells."""
        if not self.created_spells:
            print("\nNo spells created yet.")
            return
        
        print(f"\n📜 Created Spells ({len(self.created_spells)}):")
        print("-" * 40)
        
        for name, spell_info in self.created_spells.items():
            spell = spell_info['spell']
            print(f"• {name}")
            print(f"  School: {spell.school.value}")
            print(f"  Cast: {spell.cast_time}s, Cooldown: {spell.cooldown}s")
            print(f"  Mana: {spell.mana_cost}")
            print()
    
    def save_spells_to_file(self):
        """Save created spells to a JSON file."""
        if not self.created_spells:
            print("\nNo spells to save.")
            return
        
        filename = input("Enter filename (without .json): ").strip()
        if not filename:
            filename = "my_spells"
        
        filename = f"{filename}.json"
        
        # Convert spells to JSON format
        spell_data = {}
        for name, spell_info in self.created_spells.items():
            spell_data[name.lower().replace(' ', '_')] = spell_info['data']
        
        try:
            with open(filename, 'w') as f:
                json.dump(spell_data, f, indent=2)
            
            print(f"\n✅ Saved {len(spell_data)} spells to {filename}")
            
        except Exception as e:
            print(f"\n❌ Error saving file: {e}")
    
    def load_spells_from_file(self):
        """Load spells from a JSON file."""
        filename = input("Enter filename: ").strip()
        
        try:
            with open(filename, 'r') as f:
                spell_data = json.load(f)
            
            loaded_count = 0
            for spell_name, spell_config in spell_data.items():
                try:
                    spell = self.builder.build_spell_from_dict(spell_config)
                    self.created_spells[spell.name] = {
                        'spell': spell,
                        'data': spell_config
                    }
                    loaded_count += 1
                except Exception as e:
                    print(f"Failed to load {spell_name}: {e}")
            
            print(f"\n✅ Loaded {loaded_count} spells from {filename}")
            
        except FileNotFoundError:
            print(f"\n❌ File {filename} not found.")
        except Exception as e:
            print(f"\n❌ Error loading file: {e}")
    
    def validate_spell(self):
        """Validate a specific spell."""
        if not self.created_spells:
            print("\nNo spells to validate.")
            return
        
        # List spells
        spell_names = list(self.created_spells.keys())
        for i, name in enumerate(spell_names, 1):
            print(f"{i}. {name}")
        
        try:
            choice = int(input("\nSelect spell to validate: ")) - 1
            if 0 <= choice < len(spell_names):
                spell_name = spell_names[choice]
                spell_data = self.created_spells[spell_name]['data']
                
                is_valid, errors, config = self.validator.validate_spell_from_dict(spell_data)
                
                print(f"\nValidation results for {spell_name}:")
                print(f"Valid: {'✅ Yes' if is_valid else '❌ No'}")
                
                if errors:
                    print("Errors:")
                    for error in errors:
                        print(f"  • {error}")
                
                if config:
                    suggestions = self.validator.suggest_balance_fixes(config)
                    if suggestions:
                        print("Balance suggestions:")
                        for suggestion in suggestions:
                            print(f"  • {suggestion}")
            else:
                print("Invalid selection.")
                
        except ValueError:
            print("Please enter a valid number.")


def main():
    """Run the spell creator CLI."""
    creator = SpellCreatorCLI()
    creator.run()


if __name__ == "__main__":
    main()
