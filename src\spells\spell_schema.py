"""
Spell creation data format and schema definitions.
This module defines the structure for creating spells from JSON configurations.
"""

from typing import Dict, List, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass


class SpellSchoolType(Enum):
    """Available spell schools."""
    FIRE = "fire"
    FROST = "frost"
    ARCANE = "arcane"
    SHADOW = "shadow"
    NATURE = "nature"
    HOLY = "holy"
    PHYSICAL = "physical"


class EffectType(Enum):
    """Types of effects a spell can have."""
    DIRECT_DAMAGE = "direct_damage"
    DAMAGE_OVER_TIME = "damage_over_time"
    DIRECT_HEAL = "direct_heal"
    HEAL_OVER_TIME = "heal_over_time"
    BUFF = "buff"
    DEBUFF = "debuff"
    STAT_MODIFIER = "stat_modifier"
    PROC = "proc"
    TELEPORT = "teleport"
    SUMMON = "summon"


class TargetType(Enum):
    """Who the spell can target."""
    SELF = "self"
    SINGLE_ENEMY = "single_enemy"
    SINGLE_ALLY = "single_ally"
    SINGLE_ANY = "single_any"
    AOE_ENEMY = "aoe_enemy"
    AOE_ALLY = "aoe_ally"
    AOE_ALL = "aoe_all"
    GROUND_TARGET = "ground_target"
    NO_TARGET = "no_target"


@dataclass
class SpellEffect:
    """Configuration for a single spell effect."""
    type: EffectType
    value: Union[int, float, Dict[str, Any]]
    duration: float = 0.0  # 0 = instant effect
    tick_interval: float = 3.0  # For DoT/HoT effects
    chance: float = 1.0  # Probability of effect occurring
    conditions: Optional[Dict[str, Any]] = None  # Conditional requirements


@dataclass
class SpellConfig:
    """Complete spell configuration."""
    # Basic properties
    name: str
    description: str
    school: SpellSchoolType
    
    # Casting properties
    cast_time: float
    cooldown: float
    mana_cost: int
    
    # Targeting
    target_type: TargetType
    range: float = 30.0  # yards
    aoe_radius: float = 0.0  # For AoE spells
    
    # Damage/Healing
    base_damage: Union[int, List[int]] = 0  # Can be single value or [min, max]
    base_healing: Union[int, List[int]] = 0
    spell_power_coefficient: float = 1.0
    
    # Critical hits
    can_crit: bool = True
    crit_multiplier: float = 2.0
    
    # Effects
    effects: List[SpellEffect] = None
    
    # Advanced properties
    channeled: bool = False
    channel_ticks: int = 1
    requires_line_of_sight: bool = True
    interruptible: bool = True
    
    # Scaling
    level_scaling: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        if self.effects is None:
            self.effects = []


# JSON Schema for validation
SPELL_JSON_SCHEMA = {
    "type": "object",
    "required": ["name", "school", "cast_time", "cooldown", "mana_cost", "target_type"],
    "properties": {
        "name": {
            "type": "string",
            "minLength": 1,
            "maxLength": 50
        },
        "description": {
            "type": "string",
            "maxLength": 500
        },
        "school": {
            "type": "string",
            "enum": [school.value for school in SpellSchoolType]
        },
        "cast_time": {
            "type": "number",
            "minimum": 0,
            "maximum": 10
        },
        "cooldown": {
            "type": "number",
            "minimum": 0,
            "maximum": 600
        },
        "mana_cost": {
            "type": "integer",
            "minimum": 0,
            "maximum": 2000
        },
        "target_type": {
            "type": "string",
            "enum": [target.value for target in TargetType]
        },
        "range": {
            "type": "number",
            "minimum": 0,
            "maximum": 100
        },
        "base_damage": {
            "oneOf": [
                {"type": "integer", "minimum": 0},
                {
                    "type": "array",
                    "items": {"type": "integer", "minimum": 0},
                    "minItems": 2,
                    "maxItems": 2
                }
            ]
        },
        "base_healing": {
            "oneOf": [
                {"type": "integer", "minimum": 0},
                {
                    "type": "array", 
                    "items": {"type": "integer", "minimum": 0},
                    "minItems": 2,
                    "maxItems": 2
                }
            ]
        },
        "spell_power_coefficient": {
            "type": "number",
            "minimum": 0,
            "maximum": 3
        },
        "can_crit": {
            "type": "boolean"
        },
        "crit_multiplier": {
            "type": "number",
            "minimum": 1,
            "maximum": 5
        },
        "effects": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["type", "value"],
                "properties": {
                    "type": {
                        "type": "string",
                        "enum": [effect.value for effect in EffectType]
                    },
                    "value": {
                        "oneOf": [
                            {"type": "number"},
                            {"type": "integer"},
                            {"type": "object"}
                        ]
                    },
                    "duration": {
                        "type": "number",
                        "minimum": 0
                    },
                    "tick_interval": {
                        "type": "number",
                        "minimum": 0.1
                    },
                    "chance": {
                        "type": "number",
                        "minimum": 0,
                        "maximum": 1
                    }
                }
            }
        },
        "channeled": {
            "type": "boolean"
        },
        "channel_ticks": {
            "type": "integer",
            "minimum": 1,
            "maximum": 20
        }
    }
}


def create_spell_config_from_dict(data: Dict[str, Any]) -> SpellConfig:
    """Create a SpellConfig from a dictionary (e.g., loaded from JSON)."""
    
    # Convert string enums to enum objects
    if isinstance(data.get('school'), str):
        data['school'] = SpellSchoolType(data['school'])
    
    if isinstance(data.get('target_type'), str):
        data['target_type'] = TargetType(data['target_type'])
    
    # Convert effects
    if 'effects' in data and data['effects']:
        effects = []
        for effect_data in data['effects']:
            if isinstance(effect_data.get('type'), str):
                effect_data['type'] = EffectType(effect_data['type'])
            effects.append(SpellEffect(**effect_data))
        data['effects'] = effects
    
    return SpellConfig(**data)


def spell_config_to_dict(config: SpellConfig) -> Dict[str, Any]:
    """Convert a SpellConfig to a dictionary for JSON serialization."""
    
    result = {
        'name': config.name,
        'description': config.description,
        'school': config.school.value,
        'cast_time': config.cast_time,
        'cooldown': config.cooldown,
        'mana_cost': config.mana_cost,
        'target_type': config.target_type.value,
        'range': config.range,
        'aoe_radius': config.aoe_radius,
        'base_damage': config.base_damage,
        'base_healing': config.base_healing,
        'spell_power_coefficient': config.spell_power_coefficient,
        'can_crit': config.can_crit,
        'crit_multiplier': config.crit_multiplier,
        'channeled': config.channeled,
        'channel_ticks': config.channel_ticks,
        'requires_line_of_sight': config.requires_line_of_sight,
        'interruptible': config.interruptible
    }
    
    # Convert effects
    if config.effects:
        result['effects'] = []
        for effect in config.effects:
            effect_dict = {
                'type': effect.type.value,
                'value': effect.value,
                'duration': effect.duration,
                'tick_interval': effect.tick_interval,
                'chance': effect.chance
            }
            if effect.conditions:
                effect_dict['conditions'] = effect.conditions
            result['effects'].append(effect_dict)
    
    if config.level_scaling:
        result['level_scaling'] = config.level_scaling
    
    return result
