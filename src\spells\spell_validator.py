"""
Spell configuration validator.
Ensures spell configurations are valid, balanced, and won't break game systems.
"""

import json
import jsonschema
from typing import Dict, List, Any, Tuple, Optional
from .spell_schema import (
    SpellConfig, SpellEffect, SpellSchoolType, EffectType, TargetType,
    SPELL_JSON_SCHEMA, create_spell_config_from_dict
)


class ValidationError(Exception):
    """Raised when spell validation fails."""
    pass


class SpellValidator:
    """Validates spell configurations for correctness and balance."""
    
    def __init__(self):
        # Balance constraints
        self.max_dps = 1000  # Maximum DPS allowed
        self.max_hps = 800   # Maximum HPS allowed
        self.max_mana_efficiency = 5.0  # Damage per mana point
        self.min_cast_time = 0.0
        self.max_cast_time = 10.0
        self.max_cooldown = 600.0  # 10 minutes
        self.max_mana_cost = 2000
        
        # School-specific modifiers for balance
        self.school_modifiers = {
            SpellSchoolType.FIRE: {"damage_bonus": 1.0, "dot_bonus": 1.2},
            SpellSchoolType.FROST: {"damage_bonus": 0.95, "slow_bonus": 1.0},
            SpellSchoolType.ARCANE: {"damage_bonus": 1.1, "mana_cost_penalty": 1.1},
            SpellSchoolType.SHADOW: {"damage_bonus": 1.05, "dot_bonus": 1.3},
            SpellSchoolType.NATURE: {"damage_bonus": 1.0, "chain_bonus": 1.0},
            SpellSchoolType.HOLY: {"healing_bonus": 1.2, "damage_penalty": 0.8},
            SpellSchoolType.PHYSICAL: {"damage_bonus": 0.9, "crit_bonus": 1.1}
        }
    
    def validate_json_schema(self, spell_data: Dict[str, Any]) -> List[str]:
        """Validate against JSON schema."""
        errors = []
        
        try:
            jsonschema.validate(spell_data, SPELL_JSON_SCHEMA)
        except jsonschema.ValidationError as e:
            errors.append(f"Schema validation error: {e.message}")
        except jsonschema.SchemaError as e:
            errors.append(f"Schema error: {e.message}")
        
        return errors
    
    def validate_spell_config(self, config: SpellConfig) -> Tuple[bool, List[str]]:
        """
        Validate a spell configuration.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Basic validation
        errors.extend(self._validate_basic_properties(config))
        
        # Balance validation
        errors.extend(self._validate_balance(config))
        
        # Logic validation
        errors.extend(self._validate_logic(config))
        
        # Effect validation
        errors.extend(self._validate_effects(config))
        
        return len(errors) == 0, errors
    
    def _validate_basic_properties(self, config: SpellConfig) -> List[str]:
        """Validate basic spell properties."""
        errors = []
        
        # Name validation
        if not config.name or len(config.name.strip()) == 0:
            errors.append("Spell name cannot be empty")
        elif len(config.name) > 50:
            errors.append("Spell name too long (max 50 characters)")
        
        # Cast time validation
        if config.cast_time < self.min_cast_time:
            errors.append(f"Cast time too low (min {self.min_cast_time})")
        elif config.cast_time > self.max_cast_time:
            errors.append(f"Cast time too high (max {self.max_cast_time})")
        
        # Cooldown validation
        if config.cooldown < 0:
            errors.append("Cooldown cannot be negative")
        elif config.cooldown > self.max_cooldown:
            errors.append(f"Cooldown too high (max {self.max_cooldown})")
        
        # Mana cost validation
        if config.mana_cost < 0:
            errors.append("Mana cost cannot be negative")
        elif config.mana_cost > self.max_mana_cost:
            errors.append(f"Mana cost too high (max {self.max_mana_cost})")
        
        # Range validation
        if config.range < 0:
            errors.append("Range cannot be negative")
        elif config.range > 100:
            errors.append("Range too high (max 100 yards)")
        
        return errors
    
    def _validate_balance(self, config: SpellConfig) -> List[str]:
        """Validate spell balance."""
        errors = []
        
        # Calculate theoretical DPS
        dps = self._calculate_theoretical_dps(config)
        if dps > self.max_dps:
            errors.append(f"Spell DPS too high: {dps:.1f} (max {self.max_dps})")
        
        # Calculate theoretical HPS
        hps = self._calculate_theoretical_hps(config)
        if hps > self.max_hps:
            errors.append(f"Spell HPS too high: {hps:.1f} (max {self.max_hps})")
        
        # Mana efficiency check
        if config.mana_cost > 0:
            damage_per_mana = self._get_average_damage(config) / config.mana_cost
            if damage_per_mana > self.max_mana_efficiency:
                errors.append(f"Mana efficiency too high: {damage_per_mana:.2f} (max {self.max_mana_efficiency})")
        
        # Instant cast restrictions
        if config.cast_time == 0:
            avg_damage = self._get_average_damage(config)
            if avg_damage > 300:  # Instant spells should be weaker
                errors.append("Instant cast spells cannot deal more than 300 damage")
        
        return errors
    
    def _validate_logic(self, config: SpellConfig) -> List[str]:
        """Validate spell logic consistency."""
        errors = []
        
        # Channeled spell validation
        if config.channeled:
            if config.cast_time <= 0:
                errors.append("Channeled spells must have cast time > 0")
            if config.channel_ticks <= 0:
                errors.append("Channeled spells must have channel_ticks > 0")
        
        # Target type validation
        if config.target_type in [TargetType.AOE_ENEMY, TargetType.AOE_ALLY, TargetType.AOE_ALL]:
            if config.aoe_radius <= 0:
                errors.append("AoE spells must have aoe_radius > 0")
        
        if config.target_type == TargetType.GROUND_TARGET:
            if config.aoe_radius <= 0:
                errors.append("Ground target spells must have aoe_radius > 0")
        
        # Damage/healing consistency
        if config.base_damage > 0 and config.base_healing > 0:
            errors.append("Spell cannot both damage and heal (use effects for complex spells)")
        
        # School-specific validation
        if config.school == SpellSchoolType.HOLY:
            if config.base_damage > 0 and config.base_healing == 0:
                errors.append("Holy spells should primarily heal, not damage")
        
        return errors
    
    def _validate_effects(self, config: SpellConfig) -> List[str]:
        """Validate spell effects."""
        errors = []
        
        if not config.effects:
            return errors
        
        for i, effect in enumerate(config.effects):
            effect_errors = self._validate_single_effect(effect, i)
            errors.extend(effect_errors)
        
        return errors
    
    def _validate_single_effect(self, effect: SpellEffect, index: int) -> List[str]:
        """Validate a single spell effect."""
        errors = []
        prefix = f"Effect {index + 1}: "
        
        # Duration validation
        if effect.duration < 0:
            errors.append(f"{prefix}Duration cannot be negative")
        elif effect.duration > 300:  # 5 minutes max
            errors.append(f"{prefix}Duration too long (max 300 seconds)")
        
        # Chance validation
        if effect.chance < 0 or effect.chance > 1:
            errors.append(f"{prefix}Chance must be between 0 and 1")
        
        # Type-specific validation
        if effect.type in [EffectType.DAMAGE_OVER_TIME, EffectType.HEAL_OVER_TIME]:
            if effect.duration <= 0:
                errors.append(f"{prefix}DoT/HoT effects must have duration > 0")
            if effect.tick_interval <= 0:
                errors.append(f"{prefix}DoT/HoT effects must have tick_interval > 0")
            if effect.tick_interval > effect.duration:
                errors.append(f"{prefix}Tick interval cannot be longer than duration")
        
        # Value validation based on type
        if effect.type == EffectType.DIRECT_DAMAGE:
            if not isinstance(effect.value, (int, float)) or effect.value <= 0:
                errors.append(f"{prefix}Direct damage must be a positive number")
        
        elif effect.type == EffectType.DIRECT_HEAL:
            if not isinstance(effect.value, (int, float)) or effect.value <= 0:
                errors.append(f"{prefix}Direct heal must be a positive number")
        
        elif effect.type in [EffectType.BUFF, EffectType.DEBUFF, EffectType.STAT_MODIFIER]:
            if not isinstance(effect.value, dict):
                errors.append(f"{prefix}Buff/debuff/stat modifier must have dict value")
        
        return errors
    
    def _calculate_theoretical_dps(self, config: SpellConfig) -> float:
        """Calculate theoretical DPS of the spell."""
        avg_damage = self._get_average_damage(config)
        
        # Add DoT damage
        dot_damage = 0
        if config.effects:
            for effect in config.effects:
                if effect.type == EffectType.DAMAGE_OVER_TIME:
                    if isinstance(effect.value, (int, float)):
                        ticks = effect.duration / effect.tick_interval
                        dot_damage += effect.value * ticks * effect.chance
        
        total_damage = avg_damage + dot_damage
        
        # Calculate cast cycle time
        cycle_time = max(config.cast_time, config.cooldown)
        if cycle_time <= 0:
            cycle_time = 1.5  # Assume GCD
        
        return total_damage / cycle_time
    
    def _calculate_theoretical_hps(self, config: SpellConfig) -> float:
        """Calculate theoretical HPS of the spell."""
        avg_healing = self._get_average_healing(config)
        
        # Add HoT healing
        hot_healing = 0
        if config.effects:
            for effect in config.effects:
                if effect.type == EffectType.HEAL_OVER_TIME:
                    if isinstance(effect.value, (int, float)):
                        ticks = effect.duration / effect.tick_interval
                        hot_healing += effect.value * ticks * effect.chance
        
        total_healing = avg_healing + hot_healing
        
        # Calculate cast cycle time
        cycle_time = max(config.cast_time, config.cooldown)
        if cycle_time <= 0:
            cycle_time = 1.5  # Assume GCD
        
        return total_healing / cycle_time
    
    def _get_average_damage(self, config: SpellConfig) -> float:
        """Get average damage of the spell."""
        if isinstance(config.base_damage, list):
            return sum(config.base_damage) / len(config.base_damage)
        return float(config.base_damage)
    
    def _get_average_healing(self, config: SpellConfig) -> float:
        """Get average healing of the spell."""
        if isinstance(config.base_healing, list):
            return sum(config.base_healing) / len(config.base_healing)
        return float(config.base_healing)
    
    def validate_spell_from_dict(self, spell_data: Dict[str, Any]) -> Tuple[bool, List[str], Optional[SpellConfig]]:
        """
        Validate a spell from dictionary data.
        
        Returns:
            Tuple of (is_valid, errors, spell_config_or_none)
        """
        # First validate JSON schema
        schema_errors = self.validate_json_schema(spell_data)
        if schema_errors:
            return False, schema_errors, None
        
        try:
            # Create spell config
            config = create_spell_config_from_dict(spell_data)
            
            # Validate the config
            is_valid, errors = self.validate_spell_config(config)
            
            return is_valid, errors, config if is_valid else None
            
        except Exception as e:
            return False, [f"Error creating spell config: {str(e)}"], None
    
    def suggest_balance_fixes(self, config: SpellConfig) -> List[str]:
        """Suggest fixes for balance issues."""
        suggestions = []
        
        dps = self._calculate_theoretical_dps(config)
        if dps > self.max_dps:
            reduction_factor = self.max_dps / dps
            suggestions.append(f"Reduce damage by {(1-reduction_factor)*100:.1f}% to balance DPS")
        
        hps = self._calculate_theoretical_hps(config)
        if hps > self.max_hps:
            reduction_factor = self.max_hps / hps
            suggestions.append(f"Reduce healing by {(1-reduction_factor)*100:.1f}% to balance HPS")
        
        if config.mana_cost > 0:
            damage_per_mana = self._get_average_damage(config) / config.mana_cost
            if damage_per_mana > self.max_mana_efficiency:
                new_cost = self._get_average_damage(config) / self.max_mana_efficiency
                suggestions.append(f"Increase mana cost to {new_cost:.0f} for better efficiency")
        
        return suggestions
