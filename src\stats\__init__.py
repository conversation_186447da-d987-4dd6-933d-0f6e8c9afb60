"""
Modular stats system for the WoW Simulator.

This module provides a flexible, extensible stats system that can handle
any stat type and complex calculations without requiring code changes.
"""

from .base_stats import BaseStatContainer, StatModifier
from .stat_calculator import StatCalculator, StatCalculationRule
from .stat_manager import StatManager
from .modifiers import (
    AdditiveModifier,
    MultiplicativeModifier,
    PercentageModifier,
    ConditionalModifier,
    StackingModifier
)

__all__ = [
    'BaseStatContainer',
    'StatModifier',
    'StatCalculator',
    'StatCalculationRule',
    'StatManager',
    'AdditiveModifier',
    'MultiplicativeModifier',
    'PercentageModifier',
    'ConditionalModifier',
    'StackingModifier'
]
