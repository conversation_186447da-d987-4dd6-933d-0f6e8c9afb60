"""
Base stat container and modifier implementations.
"""

from typing import Dict, List, Optional, Set, Callable
from src.interfaces import StatType, IStatContainer, IStatModifier


class BaseStatContainer(IStatContainer):
    """
    Basic implementation of a stat container that holds base stat values
    and can calculate effective stats with modifiers.
    """
    
    def __init__(self, initial_stats: Optional[Dict[StatType, float]] = None):
        """Initialize with optional starting stats."""
        self._base_stats: Dict[StatType, float] = {}
        self._modifiers: List[IStatModifier] = []
        
        if initial_stats:
            for stat_type, value in initial_stats.items():
                self.set_stat(stat_type, value)
    
    def get_stat(self, stat_type: StatType) -> float:
        """Get the base value of a stat."""
        return self._base_stats.get(stat_type, 0.0)
    
    def set_stat(self, stat_type: StatType, value: float) -> None:
        """Set the base value of a stat."""
        self._base_stats[stat_type] = value
    
    def get_effective_stat(self, stat_type: StatType) -> float:
        """Get the effective value including all modifiers."""
        base_value = self.get_stat(stat_type)
        
        # Apply all modifiers that affect this stat
        effective_value = base_value
        for modifier in self._modifiers:
            if modifier.applies_to_stat(stat_type):
                modifiers = modifier.get_stat_modifiers()
                if stat_type in modifiers:
                    effective_value += modifiers[stat_type]
        
        return effective_value
    
    def add_modifier(self, modifier: IStatModifier) -> None:
        """Add a stat modifier."""
        if modifier not in self._modifiers:
            self._modifiers.append(modifier)
    
    def remove_modifier(self, modifier: IStatModifier) -> None:
        """Remove a stat modifier."""
        if modifier in self._modifiers:
            self._modifiers.remove(modifier)
    
    def get_all_modifiers(self) -> List[IStatModifier]:
        """Get all active modifiers."""
        return self._modifiers.copy()
    
    def get_modifiers_for_stat(self, stat_type: StatType) -> List[IStatModifier]:
        """Get all modifiers that affect a specific stat."""
        return [mod for mod in self._modifiers if mod.applies_to_stat(stat_type)]
    
    def get_all_stats(self) -> Dict[StatType, float]:
        """Get all base stats."""
        return self._base_stats.copy()
    
    def get_all_effective_stats(self) -> Dict[StatType, float]:
        """Get all effective stats."""
        effective_stats = {}
        for stat_type in StatType:
            effective_stats[stat_type] = self.get_effective_stat(stat_type)
        return effective_stats


class StatModifier(IStatModifier):
    """
    Basic implementation of a stat modifier.
    """
    
    def __init__(self, name: str, modifiers: Dict[StatType, float]):
        """
        Initialize with a name and stat modifications.
        
        Args:
            name: Name of this modifier (for identification)
            modifiers: Dict mapping stat types to modification values
        """
        self.name = name
        self._modifiers = modifiers.copy()
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get all stat modifications this object provides."""
        return self._modifiers.copy()
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        return stat_type in self._modifiers
    
    def add_stat_modifier(self, stat_type: StatType, value: float) -> None:
        """Add or update a stat modifier."""
        self._modifiers[stat_type] = value
    
    def remove_stat_modifier(self, stat_type: StatType) -> None:
        """Remove a stat modifier."""
        if stat_type in self._modifiers:
            del self._modifiers[stat_type]
    
    def get_affected_stats(self) -> Set[StatType]:
        """Get all stats affected by this modifier."""
        return set(self._modifiers.keys())
    
    def __str__(self) -> str:
        return f"StatModifier({self.name}): {self._modifiers}"
    
    def __repr__(self) -> str:
        return f"StatModifier(name='{self.name}', modifiers={self._modifiers})"


class ConditionalStatModifier(StatModifier):
    """
    A stat modifier that only applies when certain conditions are met.
    """
    
    def __init__(self, name: str, modifiers: Dict[StatType, float], condition_func: Callable[[], bool]):
        """
        Initialize with a condition function.
        
        Args:
            name: Name of this modifier
            modifiers: Stat modifications
            condition_func: Function that returns True when modifier should apply
        """
        super().__init__(name, modifiers)
        self._condition_func = condition_func
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat and condition is met."""
        return stat_type in self._modifiers and self._condition_func()
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get stat modifications only if condition is met."""
        if self._condition_func():
            return self._modifiers.copy()
        return {}


class StackingStatModifier(StatModifier):
    """
    A stat modifier that can stack multiple times.
    """
    
    def __init__(self, name: str, modifiers: Dict[StatType, float], max_stacks: int = 10):
        """
        Initialize with stacking capability.
        
        Args:
            name: Name of this modifier
            modifiers: Base stat modifications per stack
            max_stacks: Maximum number of stacks allowed
        """
        super().__init__(name, modifiers)
        self.max_stacks = max_stacks
        self.current_stacks = 1
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get stat modifications multiplied by current stacks."""
        stacked_modifiers = {}
        for stat_type, value in self._modifiers.items():
            stacked_modifiers[stat_type] = value * self.current_stacks
        return stacked_modifiers
    
    def add_stack(self) -> bool:
        """Add a stack. Returns True if successful."""
        if self.current_stacks < self.max_stacks:
            self.current_stacks += 1
            return True
        return False
    
    def remove_stack(self) -> bool:
        """Remove a stack. Returns True if modifier should be removed."""
        self.current_stacks -= 1
        return self.current_stacks <= 0
    
    def set_stacks(self, stacks: int) -> None:
        """Set the number of stacks."""
        self.current_stacks = max(0, min(stacks, self.max_stacks))
    
    def __str__(self) -> str:
        return f"StackingStatModifier({self.name}): {self._modifiers} x{self.current_stacks}"
