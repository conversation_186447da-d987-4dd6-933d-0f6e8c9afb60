"""
Advanced stat modifier implementations for complex calculations.
"""

from typing import Dict, Callable, Any, Optional
from abc import ABC, abstractmethod
from src.interfaces import StatType, IStatModifier


class ModifierType:
    """Constants for different types of stat modifications."""
    ADDITIVE = "additive"
    MULTIPLICATIVE = "multiplicative"
    PERCENTAGE = "percentage"
    CONDITIONAL = "conditional"
    STACKING = "stacking"


class AdvancedStatModifier(IStatModifier, ABC):
    """
    Abstract base class for advanced stat modifiers with complex calculation logic.
    """
    
    def __init__(self, name: str, modifier_type: str):
        self.name = name
        self.modifier_type = modifier_type
        self._enabled = True
    
    def enable(self) -> None:
        """Enable this modifier."""
        self._enabled = True
    
    def disable(self) -> None:
        """Disable this modifier."""
        self._enabled = False
    
    def is_enabled(self) -> bool:
        """Check if this modifier is enabled."""
        return self._enabled
    
    @abstractmethod
    def calculate_modifier(self, stat_type: StatType, base_value: float, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the modification for a specific stat."""
        pass


class AdditiveModifier(AdvancedStatModifier):
    """
    Modifier that adds a flat value to stats.
    """
    
    def __init__(self, name: str, stat_bonuses: Dict[StatType, float]):
        super().__init__(name, ModifierType.ADDITIVE)
        self._stat_bonuses = stat_bonuses.copy()
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get all stat modifications this object provides."""
        if not self._enabled:
            return {}
        return self._stat_bonuses.copy()
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        return self._enabled and stat_type in self._stat_bonuses
    
    def calculate_modifier(self, stat_type: StatType, base_value: float, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the additive modification."""
        if not self._enabled or stat_type not in self._stat_bonuses:
            return 0.0
        return self._stat_bonuses[stat_type]
    
    def set_bonus(self, stat_type: StatType, bonus: float) -> None:
        """Set the bonus for a specific stat."""
        self._stat_bonuses[stat_type] = bonus
    
    def get_bonus(self, stat_type: StatType) -> float:
        """Get the bonus for a specific stat."""
        return self._stat_bonuses.get(stat_type, 0.0)


class MultiplicativeModifier(AdvancedStatModifier):
    """
    Modifier that multiplies stats by a factor.
    """
    
    def __init__(self, name: str, stat_multipliers: Dict[StatType, float]):
        super().__init__(name, ModifierType.MULTIPLICATIVE)
        self._stat_multipliers = stat_multipliers.copy()
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get all stat modifications this object provides."""
        if not self._enabled:
            return {}
        
        # Convert multipliers to additive values for compatibility
        # (multiplier - 1) * base_value = additive bonus
        # This is handled in calculate_modifier for proper calculation
        return {stat: 0.0 for stat in self._stat_multipliers.keys()}
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        return self._enabled and stat_type in self._stat_multipliers
    
    def calculate_modifier(self, stat_type: StatType, base_value: float, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the multiplicative modification."""
        if not self._enabled or stat_type not in self._stat_multipliers:
            return 0.0
        
        multiplier = self._stat_multipliers[stat_type]
        return base_value * (multiplier - 1.0)  # Convert to additive bonus
    
    def set_multiplier(self, stat_type: StatType, multiplier: float) -> None:
        """Set the multiplier for a specific stat."""
        self._stat_multipliers[stat_type] = multiplier
    
    def get_multiplier(self, stat_type: StatType) -> float:
        """Get the multiplier for a specific stat."""
        return self._stat_multipliers.get(stat_type, 1.0)


class PercentageModifier(AdvancedStatModifier):
    """
    Modifier that adds a percentage of the base stat value.
    """
    
    def __init__(self, name: str, stat_percentages: Dict[StatType, float]):
        super().__init__(name, ModifierType.PERCENTAGE)
        self._stat_percentages = stat_percentages.copy()
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get all stat modifications this object provides."""
        if not self._enabled:
            return {}
        return {stat: 0.0 for stat in self._stat_percentages.keys()}
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        return self._enabled and stat_type in self._stat_percentages
    
    def calculate_modifier(self, stat_type: StatType, base_value: float, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the percentage-based modification."""
        if not self._enabled or stat_type not in self._stat_percentages:
            return 0.0
        
        percentage = self._stat_percentages[stat_type]
        return base_value * (percentage / 100.0)
    
    def set_percentage(self, stat_type: StatType, percentage: float) -> None:
        """Set the percentage bonus for a specific stat."""
        self._stat_percentages[stat_type] = percentage
    
    def get_percentage(self, stat_type: StatType) -> float:
        """Get the percentage bonus for a specific stat."""
        return self._stat_percentages.get(stat_type, 0.0)


class ConditionalModifier(AdvancedStatModifier):
    """
    Modifier that only applies when certain conditions are met.
    """
    
    def __init__(self, name: str, base_modifier: IStatModifier, condition_func: Callable[[], bool]):
        super().__init__(name, ModifierType.CONDITIONAL)
        self._base_modifier = base_modifier
        self._condition_func = condition_func
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get stat modifications only if condition is met."""
        if not self._enabled or not self._condition_func():
            return {}
        return self._base_modifier.get_stat_modifiers()
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat and condition is met."""
        return (self._enabled and 
                self._condition_func() and 
                self._base_modifier.applies_to_stat(stat_type))
    
    def calculate_modifier(self, stat_type: StatType, base_value: float, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the conditional modification."""
        if not self._enabled or not self._condition_func():
            return 0.0
        
        if hasattr(self._base_modifier, 'calculate_modifier'):
            return self._base_modifier.calculate_modifier(stat_type, base_value, context)
        else:
            # Fallback for simple modifiers
            modifiers = self._base_modifier.get_stat_modifiers()
            return modifiers.get(stat_type, 0.0)
    
    def update_condition(self, condition_func: Callable[[], bool]) -> None:
        """Update the condition function."""
        self._condition_func = condition_func


class StackingModifier(AdvancedStatModifier):
    """
    Modifier that can stack multiple times with diminishing returns or caps.
    """
    
    def __init__(self, name: str, base_modifiers: Dict[StatType, float], 
                 max_stacks: int = 10, diminishing_returns: bool = False):
        super().__init__(name, ModifierType.STACKING)
        self._base_modifiers = base_modifiers.copy()
        self.max_stacks = max_stacks
        self.current_stacks = 0
        self.diminishing_returns = diminishing_returns
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get stat modifications multiplied by current stacks."""
        if not self._enabled or self.current_stacks == 0:
            return {}
        
        stacked_modifiers = {}
        for stat_type, base_value in self._base_modifiers.items():
            if self.diminishing_returns:
                # Apply diminishing returns formula: value * (1 - (1 - efficiency)^stacks)
                efficiency = 0.8  # Each stack is 80% as effective as the previous
                effective_stacks = 1 - (1 - efficiency) ** self.current_stacks
                stacked_modifiers[stat_type] = base_value * effective_stacks / efficiency
            else:
                stacked_modifiers[stat_type] = base_value * self.current_stacks
        
        return stacked_modifiers
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        return (self._enabled and 
                self.current_stacks > 0 and 
                stat_type in self._base_modifiers)
    
    def calculate_modifier(self, stat_type: StatType, base_value: float, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the stacking modification."""
        modifiers = self.get_stat_modifiers()
        return modifiers.get(stat_type, 0.0)
    
    def add_stack(self) -> bool:
        """Add a stack. Returns True if successful."""
        if self.current_stacks < self.max_stacks:
            self.current_stacks += 1
            return True
        return False
    
    def remove_stack(self) -> bool:
        """Remove a stack. Returns True if modifier should be removed."""
        if self.current_stacks > 0:
            self.current_stacks -= 1
        return self.current_stacks == 0
    
    def set_stacks(self, stacks: int) -> None:
        """Set the number of stacks."""
        self.current_stacks = max(0, min(stacks, self.max_stacks))
    
    def get_stacks(self) -> int:
        """Get the current number of stacks."""
        return self.current_stacks
