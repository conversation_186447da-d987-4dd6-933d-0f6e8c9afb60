"""
Standalone stats system that works without complex imports.
This is a simplified version that demonstrates the modular concepts.
"""

from typing import Dict, List, Optional, Any, Callable
from enum import Enum


class StatType(Enum):
    """Enumeration of all possible character stats."""
    HEALTH = "health"
    MANA = "mana"
    SPELL_POWER = "spell_power"
    CRIT_CHANCE = "crit_chance"
    HASTE = "haste"
    HIT_CHANCE = "hit_chance"
    ARMOR = "armor"
    RESISTANCE_FIRE = "resistance_fire"
    RESISTANCE_FROST = "resistance_frost"
    RESISTANCE_ARCANE = "resistance_arcane"
    RESISTANCE_SHADOW = "resistance_shadow"
    RESISTANCE_NATURE = "resistance_nature"
    RESISTANCE_HOLY = "resistance_holy"


class StatModifier:
    """Basic stat modifier implementation."""
    
    def __init__(self, name: str, modifiers: Dict[StatType, float]):
        self.name = name
        self._modifiers = modifiers.copy()
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get all stat modifications this object provides."""
        return self._modifiers.copy()
    
    def applies_to_stat(self, stat_type: StatType) -> bool:
        """Check if this modifier affects the given stat."""
        return stat_type in self._modifiers


class AdditiveModifier(StatModifier):
    """Modifier that adds flat values to stats."""
    
    def __init__(self, name: str, stat_bonuses: Dict[StatType, float]):
        super().__init__(name, stat_bonuses)
        self.modifier_type = "additive"


class PercentageModifier(StatModifier):
    """Modifier that adds percentage bonuses to stats."""
    
    def __init__(self, name: str, stat_percentages: Dict[StatType, float]):
        super().__init__(name, {})
        self._percentages = stat_percentages.copy()
        self.modifier_type = "percentage"
    
    def calculate_modifier(self, stat_type: StatType, base_value: float) -> float:
        """Calculate the percentage-based modification."""
        if stat_type not in self._percentages:
            return 0.0
        percentage = self._percentages[stat_type]
        return base_value * (percentage / 100.0)


class StackingModifier(StatModifier):
    """Modifier that can stack multiple times."""
    
    def __init__(self, name: str, base_modifiers: Dict[StatType, float], max_stacks: int = 10):
        super().__init__(name, base_modifiers)
        self.max_stacks = max_stacks
        self.current_stacks = 0
        self.modifier_type = "stacking"
    
    def get_stat_modifiers(self) -> Dict[StatType, float]:
        """Get stat modifications multiplied by current stacks."""
        if self.current_stacks == 0:
            return {}
        
        stacked_modifiers = {}
        for stat_type, base_value in self._modifiers.items():
            stacked_modifiers[stat_type] = base_value * self.current_stacks
        return stacked_modifiers
    
    def add_stack(self) -> bool:
        """Add a stack. Returns True if successful."""
        if self.current_stacks < self.max_stacks:
            self.current_stacks += 1
            return True
        return False
    
    def remove_stack(self) -> bool:
        """Remove a stack. Returns True if modifier should be removed."""
        if self.current_stacks > 0:
            self.current_stacks -= 1
        return self.current_stacks == 0
    
    def get_stacks(self) -> int:
        """Get current number of stacks."""
        return self.current_stacks


class StatContainer:
    """Container for character stats with modifier support."""
    
    def __init__(self, initial_stats: Optional[Dict[StatType, float]] = None):
        self._base_stats: Dict[StatType, float] = {}
        self._modifiers: List[StatModifier] = []
        
        if initial_stats:
            for stat_type, value in initial_stats.items():
                self.set_stat(stat_type, value)
    
    def get_stat(self, stat_type: StatType) -> float:
        """Get the base value of a stat."""
        return self._base_stats.get(stat_type, 0.0)
    
    def set_stat(self, stat_type: StatType, value: float) -> None:
        """Set the base value of a stat."""
        self._base_stats[stat_type] = value
    
    def get_effective_stat(self, stat_type: StatType) -> float:
        """Get the effective value including all modifiers."""
        base_value = self.get_stat(stat_type)
        result = base_value
        
        # Apply additive modifiers
        additive_bonus = 0.0
        percentage_bonus = 0.0
        
        for modifier in self._modifiers:
            if not modifier.applies_to_stat(stat_type):
                continue
            
            if hasattr(modifier, 'modifier_type'):
                if modifier.modifier_type == "additive" or modifier.modifier_type == "stacking":
                    modifiers = modifier.get_stat_modifiers()
                    additive_bonus += modifiers.get(stat_type, 0.0)
                elif modifier.modifier_type == "percentage":
                    if hasattr(modifier, 'calculate_modifier'):
                        percentage_bonus += modifier.calculate_modifier(stat_type, base_value)
            else:
                # Default to additive
                modifiers = modifier.get_stat_modifiers()
                additive_bonus += modifiers.get(stat_type, 0.0)
        
        result += additive_bonus
        result += percentage_bonus
        
        return max(0.0, result)
    
    def add_modifier(self, modifier: StatModifier) -> None:
        """Add a stat modifier."""
        if modifier not in self._modifiers:
            self._modifiers.append(modifier)
    
    def remove_modifier(self, modifier: StatModifier) -> None:
        """Remove a stat modifier."""
        if modifier in self._modifiers:
            self._modifiers.remove(modifier)
    
    def get_all_modifiers(self) -> List[StatModifier]:
        """Get all active modifiers."""
        return self._modifiers.copy()


class SimpleStatManager:
    """Simplified stat manager for easy use."""
    
    def __init__(self):
        self._container = StatContainer()
        self._modifier_registry: Dict[str, StatModifier] = {}
    
    def set_stat(self, stat_type: StatType, value: float) -> None:
        """Set a base stat value."""
        self._container.set_stat(stat_type, value)
    
    def get_stat(self, stat_type: StatType) -> float:
        """Get a base stat value."""
        return self._container.get_stat(stat_type)
    
    def get_effective_stat(self, stat_type: StatType) -> float:
        """Get effective stat value including modifiers."""
        return self._container.get_effective_stat(stat_type)
    
    def add_flat_bonus(self, name: str, stat_bonuses: Dict[StatType, float]) -> str:
        """Add a flat stat bonus."""
        modifier = AdditiveModifier(name, stat_bonuses)
        self._modifier_registry[name] = modifier
        self._container.add_modifier(modifier)
        return name
    
    def add_percentage_bonus(self, name: str, stat_percentages: Dict[StatType, float]) -> str:
        """Add a percentage stat bonus."""
        modifier = PercentageModifier(name, stat_percentages)
        self._modifier_registry[name] = modifier
        self._container.add_modifier(modifier)
        return name
    
    def add_stacking_modifier(self, name: str, base_modifiers: Dict[StatType, float], max_stacks: int = 10) -> str:
        """Add a stacking modifier."""
        modifier = StackingModifier(name, base_modifiers, max_stacks)
        self._modifier_registry[name] = modifier
        self._container.add_modifier(modifier)
        return name
    
    def add_stack(self, modifier_name: str) -> bool:
        """Add a stack to a stacking modifier."""
        modifier = self._modifier_registry.get(modifier_name)
        if modifier and hasattr(modifier, 'add_stack'):
            return modifier.add_stack()
        return False
    
    def remove_stack(self, modifier_name: str) -> bool:
        """Remove a stack from a stacking modifier."""
        modifier = self._modifier_registry.get(modifier_name)
        if modifier and hasattr(modifier, 'remove_stack'):
            should_remove = modifier.remove_stack()
            if should_remove:
                self.remove_modifier(modifier_name)
            return should_remove
        return False
    
    def remove_modifier(self, name: str) -> bool:
        """Remove a modifier by name."""
        if name in self._modifier_registry:
            modifier = self._modifier_registry[name]
            self._container.remove_modifier(modifier)
            del self._modifier_registry[name]
            return True
        return False
    
    def get_modifier(self, name: str) -> Optional[StatModifier]:
        """Get a modifier by name."""
        return self._modifier_registry.get(name)
    
    def get_all_effective_stats(self) -> Dict[StatType, float]:
        """Get all effective stats."""
        return {stat_type: self.get_effective_stat(stat_type) for stat_type in StatType}
    
    def get_stat_breakdown(self, stat_type: StatType) -> Dict[str, Any]:
        """Get a breakdown of how a stat is calculated."""
        base_value = self.get_stat(stat_type)
        effective_value = self.get_effective_stat(stat_type)
        
        affecting_modifiers = []
        for name, modifier in self._modifier_registry.items():
            if modifier.applies_to_stat(stat_type):
                affecting_modifiers.append(name)
        
        return {
            'base_value': base_value,
            'effective_value': effective_value,
            'total_bonus': effective_value - base_value,
            'affecting_modifiers': affecting_modifiers
        }
