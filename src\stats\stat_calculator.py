"""
Advanced stat calculation engine with customizable rules and formulas.
"""

from typing import Dict, List, Callable, Optional, Any
from abc import ABC, abstractmethod
from enum import Enum
from src.interfaces import StatType, IStatContainer, IStatModifier


class CalculationOrder(Enum):
    """Order in which different types of modifiers are applied."""
    ADDITIVE = 1
    PERCENTAGE = 2
    MULTIPLICATIVE = 3
    CONDITIONAL = 4


class StatCalculationRule(ABC):
    """
    Abstract base class for stat calculation rules.
    Allows customization of how stats are calculated.
    """
    
    @abstractmethod
    def calculate(self, base_value: float, modifiers: List[IStatModifier], 
                  stat_type: StatType, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate the final stat value."""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """Get the priority of this rule (lower = calculated first)."""
        pass


class DefaultStatCalculationRule(StatCalculationRule):
    """
    Default stat calculation rule that applies modifiers in a standard order:
    1. Base value
    2. Additive modifiers
    3. Percentage modifiers
    4. Multiplicative modifiers
    """
    
    def calculate(self, base_value: float, modifiers: List[IStatModifier], 
                  stat_type: StatType, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate stat using standard WoW-like formula."""
        
        # Start with base value
        result = base_value
        
        # Apply additive modifiers first
        additive_bonus = 0.0
        percentage_bonus = 0.0
        multiplicative_factor = 1.0
        
        for modifier in modifiers:
            if not modifier.applies_to_stat(stat_type):
                continue
            
            # Check if this is an advanced modifier with calculation method
            if hasattr(modifier, 'calculate_modifier') and hasattr(modifier, 'modifier_type'):
                modifier_value = modifier.calculate_modifier(stat_type, base_value, context)
                
                if modifier.modifier_type == "additive":
                    additive_bonus += modifier_value
                elif modifier.modifier_type == "percentage":
                    percentage_bonus += modifier_value
                elif modifier.modifier_type == "multiplicative":
                    multiplicative_factor *= (1.0 + modifier_value / base_value) if base_value > 0 else 1.0
            else:
                # Handle simple modifiers
                stat_modifiers = modifier.get_stat_modifiers()
                if stat_type in stat_modifiers:
                    additive_bonus += stat_modifiers[stat_type]
        
        # Apply calculations in order
        result += additive_bonus  # Add flat bonuses
        result += percentage_bonus  # Add percentage bonuses
        result *= multiplicative_factor  # Apply multiplicative factors
        
        return max(0.0, result)  # Ensure non-negative result
    
    def get_priority(self) -> int:
        """Default rule has medium priority."""
        return 100


class CritChanceCalculationRule(StatCalculationRule):
    """
    Special calculation rule for critical hit chance with caps and diminishing returns.
    """
    
    def __init__(self, base_crit: float = 0.05, crit_cap: float = 1.0):
        self.base_crit = base_crit
        self.crit_cap = crit_cap
    
    def calculate(self, base_value: float, modifiers: List[IStatModifier], 
                  stat_type: StatType, context: Optional[Dict[str, Any]] = None) -> float:
        """Calculate crit chance with cap."""
        if stat_type != StatType.CRIT_CHANCE:
            return base_value
        
        # Use default calculation
        default_rule = DefaultStatCalculationRule()
        result = default_rule.calculate(base_value, modifiers, stat_type, context)
        
        # Apply cap
        return min(result, self.crit_cap)
    
    def get_priority(self) -> int:
        """High priority for crit calculations."""
        return 50


class StatCalculator:
    """
    Advanced stat calculator that can handle complex calculations with custom rules.
    """
    
    def __init__(self):
        self._calculation_rules: Dict[StatType, List[StatCalculationRule]] = {}
        self._global_rules: List[StatCalculationRule] = []
        
        # Add default rules
        self.add_global_rule(DefaultStatCalculationRule())
        self.add_stat_rule(StatType.CRIT_CHANCE, CritChanceCalculationRule())
    
    def add_stat_rule(self, stat_type: StatType, rule: StatCalculationRule) -> None:
        """Add a calculation rule for a specific stat type."""
        if stat_type not in self._calculation_rules:
            self._calculation_rules[stat_type] = []
        self._calculation_rules[stat_type].append(rule)
        # Sort by priority
        self._calculation_rules[stat_type].sort(key=lambda r: r.get_priority())
    
    def add_global_rule(self, rule: StatCalculationRule) -> None:
        """Add a calculation rule that applies to all stats."""
        self._global_rules.append(rule)
        self._global_rules.sort(key=lambda r: r.get_priority())
    
    def remove_stat_rule(self, stat_type: StatType, rule: StatCalculationRule) -> None:
        """Remove a calculation rule for a specific stat type."""
        if stat_type in self._calculation_rules and rule in self._calculation_rules[stat_type]:
            self._calculation_rules[stat_type].remove(rule)
    
    def remove_global_rule(self, rule: StatCalculationRule) -> None:
        """Remove a global calculation rule."""
        if rule in self._global_rules:
            self._global_rules.remove(rule)
    
    def calculate_stat(self, stat_type: StatType, base_value: float, 
                      modifiers: List[IStatModifier], 
                      context: Optional[Dict[str, Any]] = None) -> float:
        """
        Calculate the final value of a stat using all applicable rules.
        """
        # Get all applicable rules
        applicable_rules = self._global_rules.copy()
        if stat_type in self._calculation_rules:
            applicable_rules.extend(self._calculation_rules[stat_type])
        
        # Sort by priority
        applicable_rules.sort(key=lambda r: r.get_priority())
        
        # Apply rules in order
        result = base_value
        for rule in applicable_rules:
            result = rule.calculate(result, modifiers, stat_type, context)
        
        return result
    
    def calculate_all_stats(self, stat_container: IStatContainer, 
                           context: Optional[Dict[str, Any]] = None) -> Dict[StatType, float]:
        """
        Calculate all stats for a stat container.
        """
        results = {}
        modifiers = stat_container.get_all_modifiers() if hasattr(stat_container, 'get_all_modifiers') else []
        
        for stat_type in StatType:
            base_value = stat_container.get_stat(stat_type)
            results[stat_type] = self.calculate_stat(stat_type, base_value, modifiers, context)
        
        return results
    
    def get_stat_breakdown(self, stat_type: StatType, base_value: float, 
                          modifiers: List[IStatModifier],
                          context: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        Get a detailed breakdown of how a stat is calculated.
        """
        breakdown = {
            'base_value': base_value,
            'additive_bonus': 0.0,
            'percentage_bonus': 0.0,
            'multiplicative_factor': 1.0,
            'final_value': 0.0
        }
        
        # Calculate bonuses from each modifier
        for modifier in modifiers:
            if not modifier.applies_to_stat(stat_type):
                continue
            
            if hasattr(modifier, 'calculate_modifier') and hasattr(modifier, 'modifier_type'):
                modifier_value = modifier.calculate_modifier(stat_type, base_value, context)
                
                if modifier.modifier_type == "additive":
                    breakdown['additive_bonus'] += modifier_value
                elif modifier.modifier_type == "percentage":
                    breakdown['percentage_bonus'] += modifier_value
                elif modifier.modifier_type == "multiplicative":
                    breakdown['multiplicative_factor'] *= (1.0 + modifier_value / base_value) if base_value > 0 else 1.0
            else:
                # Handle simple modifiers as additive
                stat_modifiers = modifier.get_stat_modifiers()
                if stat_type in stat_modifiers:
                    breakdown['additive_bonus'] += stat_modifiers[stat_type]
        
        # Calculate final value
        result = base_value
        result += breakdown['additive_bonus']
        result += breakdown['percentage_bonus']
        result *= breakdown['multiplicative_factor']
        breakdown['final_value'] = max(0.0, result)
        
        return breakdown
