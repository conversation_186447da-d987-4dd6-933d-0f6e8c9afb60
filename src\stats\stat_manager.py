"""
High-level stat management system that coordinates all stat-related operations.
"""

from typing import Dict, List, Optional, Set, Any, Callable
from src.interfaces import StatType, IStatContainer, IStatModifier
from .base_stats import BaseStatContainer
from .stat_calculator import StatCalculator
from .modifiers import (
    AdditiveModifier, 
    MultiplicativeModifier, 
    PercentageModifier,
    ConditionalModifier,
    StackingModifier
)


class StatManager:
    """
    High-level manager that coordinates stat containers, modifiers, and calculations.
    This is the main interface for working with the stats system.
    """
    
    def __init__(self, stat_container: Optional[IStatContainer] = None):
        """
        Initialize with an optional stat container.
        If none provided, creates a default BaseStatContainer.
        """
        self._stat_container = stat_container or BaseStatContainer()
        self._calculator = StatCalculator()
        self._modifier_registry: Dict[str, IStatModifier] = {}
        self._stat_change_listeners: List[Callable[[StatType, float, float], None]] = []
        self._context: Dict[str, Any] = {}
    
    # Core stat operations
    def get_stat(self, stat_type: StatType) -> float:
        """Get the base value of a stat."""
        return self._stat_container.get_stat(stat_type)
    
    def set_stat(self, stat_type: StatType, value: float) -> None:
        """Set the base value of a stat."""
        old_value = self.get_effective_stat(stat_type)
        self._stat_container.set_stat(stat_type, value)
        new_value = self.get_effective_stat(stat_type)
        
        # Notify listeners of change
        self._notify_stat_change(stat_type, old_value, new_value)
    
    def get_effective_stat(self, stat_type: StatType) -> float:
        """Get the effective value of a stat including all modifiers."""
        base_value = self._stat_container.get_stat(stat_type)
        modifiers = list(self._modifier_registry.values())
        return self._calculator.calculate_stat(stat_type, base_value, modifiers, self._context)
    
    def get_all_effective_stats(self) -> Dict[StatType, float]:
        """Get all effective stats."""
        return {stat_type: self.get_effective_stat(stat_type) for stat_type in StatType}
    
    # Modifier management
    def add_modifier(self, modifier: IStatModifier, name: Optional[str] = None) -> str:
        """
        Add a stat modifier.
        
        Args:
            modifier: The modifier to add
            name: Optional name for the modifier (auto-generated if not provided)
            
        Returns:
            The name/key of the added modifier
        """
        if name is None:
            name = f"modifier_{len(self._modifier_registry)}"
        
        # Store old values for change notification
        old_stats = self.get_all_effective_stats()
        
        self._modifier_registry[name] = modifier
        
        # Notify of changes
        new_stats = self.get_all_effective_stats()
        for stat_type in StatType:
            if old_stats[stat_type] != new_stats[stat_type]:
                self._notify_stat_change(stat_type, old_stats[stat_type], new_stats[stat_type])
        
        return name
    
    def remove_modifier(self, name: str) -> bool:
        """
        Remove a modifier by name.
        
        Returns:
            True if modifier was found and removed
        """
        if name not in self._modifier_registry:
            return False
        
        # Store old values for change notification
        old_stats = self.get_all_effective_stats()
        
        del self._modifier_registry[name]
        
        # Notify of changes
        new_stats = self.get_all_effective_stats()
        for stat_type in StatType:
            if old_stats[stat_type] != new_stats[stat_type]:
                self._notify_stat_change(stat_type, old_stats[stat_type], new_stats[stat_type])
        
        return True
    
    def get_modifier(self, name: str) -> Optional[IStatModifier]:
        """Get a modifier by name."""
        return self._modifier_registry.get(name)
    
    def has_modifier(self, name: str) -> bool:
        """Check if a modifier exists."""
        return name in self._modifier_registry
    
    def get_all_modifiers(self) -> Dict[str, IStatModifier]:
        """Get all modifiers."""
        return self._modifier_registry.copy()
    
    def clear_modifiers(self) -> None:
        """Remove all modifiers."""
        old_stats = self.get_all_effective_stats()
        self._modifier_registry.clear()
        
        # Notify of changes
        new_stats = self.get_all_effective_stats()
        for stat_type in StatType:
            if old_stats[stat_type] != new_stats[stat_type]:
                self._notify_stat_change(stat_type, old_stats[stat_type], new_stats[stat_type])
    
    # Convenience methods for creating common modifiers
    def add_flat_bonus(self, name: str, stat_bonuses: Dict[StatType, float]) -> str:
        """Add a flat stat bonus modifier."""
        modifier = AdditiveModifier(name, stat_bonuses)
        return self.add_modifier(modifier, name)
    
    def add_percentage_bonus(self, name: str, stat_percentages: Dict[StatType, float]) -> str:
        """Add a percentage-based stat bonus modifier."""
        modifier = PercentageModifier(name, stat_percentages)
        return self.add_modifier(modifier, name)
    
    def add_multiplier(self, name: str, stat_multipliers: Dict[StatType, float]) -> str:
        """Add a multiplicative stat modifier."""
        modifier = MultiplicativeModifier(name, stat_multipliers)
        return self.add_modifier(modifier, name)
    
    def add_conditional_modifier(self, name: str, base_modifier: IStatModifier, 
                               condition: Callable[[], bool]) -> str:
        """Add a conditional modifier that only applies when condition is met."""
        modifier = ConditionalModifier(name, base_modifier, condition)
        return self.add_modifier(modifier, name)
    
    def add_stacking_modifier(self, name: str, base_modifiers: Dict[StatType, float], 
                            max_stacks: int = 10, diminishing_returns: bool = False) -> str:
        """Add a stacking modifier."""
        modifier = StackingModifier(name, base_modifiers, max_stacks, diminishing_returns)
        return self.add_modifier(modifier, name)
    
    # Stacking modifier helpers
    def add_stack(self, modifier_name: str) -> bool:
        """Add a stack to a stacking modifier."""
        modifier = self.get_modifier(modifier_name)
        if modifier and hasattr(modifier, 'add_stack'):
            old_stats = self.get_all_effective_stats()
            result = modifier.add_stack()
            
            if result:
                new_stats = self.get_all_effective_stats()
                for stat_type in StatType:
                    if old_stats[stat_type] != new_stats[stat_type]:
                        self._notify_stat_change(stat_type, old_stats[stat_type], new_stats[stat_type])
            
            return result
        return False
    
    def remove_stack(self, modifier_name: str) -> bool:
        """Remove a stack from a stacking modifier. Returns True if modifier should be removed."""
        modifier = self.get_modifier(modifier_name)
        if modifier and hasattr(modifier, 'remove_stack'):
            old_stats = self.get_all_effective_stats()
            should_remove = modifier.remove_stack()
            
            if should_remove:
                self.remove_modifier(modifier_name)
            else:
                new_stats = self.get_all_effective_stats()
                for stat_type in StatType:
                    if old_stats[stat_type] != new_stats[stat_type]:
                        self._notify_stat_change(stat_type, old_stats[stat_type], new_stats[stat_type])
            
            return should_remove
        return False
    
    # Analysis and debugging
    def get_stat_breakdown(self, stat_type: StatType) -> Dict[str, float]:
        """Get a detailed breakdown of how a stat is calculated."""
        base_value = self._stat_container.get_stat(stat_type)
        modifiers = list(self._modifier_registry.values())
        return self._calculator.get_stat_breakdown(stat_type, base_value, modifiers, self._context)
    
    def get_modifiers_affecting_stat(self, stat_type: StatType) -> Dict[str, IStatModifier]:
        """Get all modifiers that affect a specific stat."""
        affecting_modifiers = {}
        for name, modifier in self._modifier_registry.items():
            if modifier.applies_to_stat(stat_type):
                affecting_modifiers[name] = modifier
        return affecting_modifiers
    
    # Context management for conditional calculations
    def set_context(self, key: str, value: Any) -> None:
        """Set a context value for calculations."""
        self._context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get a context value."""
        return self._context.get(key, default)
    
    def clear_context(self) -> None:
        """Clear all context values."""
        self._context.clear()
    
    # Event system for stat changes
    def add_stat_change_listener(self, listener: Callable[[StatType, float, float], None]) -> None:
        """Add a listener for stat changes."""
        self._stat_change_listeners.append(listener)
    
    def remove_stat_change_listener(self, listener: Callable[[StatType, float, float], None]) -> None:
        """Remove a stat change listener."""
        if listener in self._stat_change_listeners:
            self._stat_change_listeners.remove(listener)
    
    def _notify_stat_change(self, stat_type: StatType, old_value: float, new_value: float) -> None:
        """Notify all listeners of a stat change."""
        if old_value != new_value:
            for listener in self._stat_change_listeners:
                try:
                    listener(stat_type, old_value, new_value)
                except Exception as e:
                    # Log error but don't break other listeners
                    print(f"Error in stat change listener: {e}")
    
    # Utility methods
    def copy_stats_from(self, other_container: IStatContainer) -> None:
        """Copy all base stats from another container."""
        if hasattr(other_container, 'get_all_stats'):
            stats = other_container.get_all_stats()
            for stat_type, value in stats.items():
                self.set_stat(stat_type, value)
    
    def reset_to_defaults(self) -> None:
        """Reset all stats to default values."""
        self.clear_modifiers()
        for stat_type in StatType:
            self.set_stat(stat_type, 0.0)
