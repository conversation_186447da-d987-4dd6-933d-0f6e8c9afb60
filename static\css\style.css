/* WoW Simulator - Main Stylesheet */

/* CSS Variables for WoW-themed colors */
:root {
    --primary-gold: #f4d03f;
    --primary-blue: #2980b9;
    --primary-purple: #8e44ad;
    --dark-bg: #1a1a2e;
    --darker-bg: #16213e;
    --card-bg: #0f3460;
    --text-light: #ecf0f1;
    --text-muted: #bdc3c7;
    --border-color: #34495e;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --fire-color: #e74c3c;
    --frost-color: #3498db;
    --arcane-color: #9b59b6;
    --shadow-color: #2c3e50;
    --nature-color: #27ae60;
    --holy-color: #f1c40f;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
    color: var(--text-light);
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(90deg, var(--card-bg) 0%, var(--darker-bg) 100%);
    border-bottom: 3px solid var(--primary-gold);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-gold);
    text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);
}

.logo h1 {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-gold);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-left: 0.5rem;
}

/* Navigation */
.nav {
    display: flex;
    gap: 0.5rem;
}

.nav-btn {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-light);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn:hover {
    border-color: var(--primary-gold);
    background: rgba(244, 208, 63, 0.1);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: var(--primary-gold);
    color: var(--dark-bg);
    border-color: var(--primary-gold);
    box-shadow: 0 4px 15px rgba(244, 208, 63, 0.3);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.section-header p {
    color: var(--text-muted);
    font-size: 1.1rem;
}

/* Grid Layout */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Card Styles */
.card {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.card-header {
    background: linear-gradient(90deg, var(--primary-purple) 0%, var(--primary-blue) 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-header h3 {
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    flex: 1;
}

.card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-light);
    font-weight: 600;
    font-size: 0.9rem;
}

input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    background: var(--darker-bg);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-light);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(244, 208, 63, 0.2);
}

input:invalid, select:invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

input:valid:not(:placeholder-shown), select:valid {
    border-color: var(--success-color);
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.form-group.error input,
.form-group.error select {
    border-color: var(--danger-color);
    background: rgba(231, 76, 60, 0.05);
}

.form-group.success input,
.form-group.success select {
    border-color: var(--success-color);
    background: rgba(46, 204, 113, 0.05);
}

textarea {
    resize: vertical;
    min-height: 80px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-input label {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-gold) 0%, #f1c40f 100%);
    color: var(--dark-bg);
    box-shadow: 0 4px 15px rgba(244, 208, 63, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 208, 63, 0.4);
}

.btn-secondary {
    background: var(--primary-blue);
    color: white;
    box-shadow: 0 4px 15px rgba(41, 128, 185, 0.3);
}

.btn-secondary:hover {
    background: #3498db;
    transform: translateY(-2px);
}

.btn-special {
    background: linear-gradient(45deg, var(--arcane-color) 0%, #a29bfe 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.btn-special:hover {
    background: linear-gradient(45deg, #9b59b6 0%, #8e44ad 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

.btn-icon {
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    min-height: 36px;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Character Summary */
.character-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--darker-bg);
    border-radius: 6px;
    border-left: 4px solid var(--primary-gold);
}

.summary-item .label {
    color: var(--text-muted);
    font-weight: 600;
}

.summary-item .value {
    color: var(--primary-gold);
    font-weight: 700;
}

/* Spell List */
.spell-list {
    display: grid;
    gap: 1rem;
}

.spell-item {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.spell-item:hover {
    border-color: var(--primary-gold);
    transform: translateX(4px);
}

.spell-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.spell-name {
    font-weight: 700;
    color: var(--primary-gold);
    font-size: 1.1rem;
    flex: 1;
}

.spell-header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.spell-school {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.spell-school.fire { background: var(--fire-color); color: white; }
.spell-school.frost { background: var(--frost-color); color: white; }
.spell-school.arcane { background: var(--arcane-color); color: white; }
.spell-school.shadow { background: var(--shadow-color); color: white; }
.spell-school.nature { background: var(--nature-color); color: white; }
.spell-school.holy { background: var(--holy-color); color: var(--dark-bg); }

.spell-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.spell-stat {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.spell-stat strong {
    color: var(--text-light);
}

/* Empty State */
.empty-state {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

/* Spell Selection */
.spell-selection {
    display: grid;
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.spell-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--darker-bg);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.spell-checkbox:hover {
    background: var(--border-color);
}

.spell-checkbox input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Rotation Results */
.rotation-results {
    margin-top: 1rem;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric {
    background: var(--darker-bg);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-gold);
}

.metric-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.metric-value {
    display: block;
    color: var(--primary-gold);
    font-size: 1.5rem;
    font-weight: 700;
}

/* Rotation Timeline */
.rotation-timeline {
    background: var(--darker-bg);
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.3s ease;
}

.timeline-item:hover {
    background: var(--card-bg);
}

.timeline-time {
    font-weight: 700;
    color: var(--primary-gold);
    min-width: 60px;
}

.timeline-spell {
    flex: 1;
    color: var(--text-light);
}

.timeline-damage {
    color: var(--success-color);
    font-weight: 600;
}

.timeline-mana {
    color: var(--primary-blue);
    font-size: 0.9rem;
}

/* Test Results */
.test-results {
    margin-top: 1rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.result-item {
    background: var(--darker-bg);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-blue);
}

.result-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.result-value {
    display: block;
    color: var(--primary-blue);
    font-size: 1.3rem;
    font-weight: 700;
}

/* Test History */
.test-history {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.history-item:hover {
    border-color: var(--primary-gold);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-spell {
    font-weight: 700;
    color: var(--primary-gold);
}

.history-timestamp {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    font-size: 0.9rem;
}

.history-stat {
    color: var(--text-muted);
}

.history-stat strong {
    color: var(--text-light);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: var(--primary-gold);
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: 700;
    color: var(--text-light);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.2rem;
}

.notification-message {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Range Input Styling */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
    background: var(--border-color);
    height: 6px;
    border-radius: 3px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: var(--primary-gold);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
}

input[type="range"]::-moz-range-track {
    background: var(--border-color);
    height: 6px;
    border-radius: 3px;
    border: none;
}

input[type="range"]::-moz-range-thumb {
    background: var(--primary-gold);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Advanced Spell Builder */
.effect-item, .condition-item {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.effect-item:hover, .condition-item:hover {
    border-color: var(--primary-gold);
}

.advanced-effect-properties {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Advanced Templates Grid */
.advanced-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.template-card {
    background: var(--darker-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-card:hover {
    border-color: var(--primary-gold);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(244, 208, 63, 0.2);
}

.template-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
    padding: 1rem;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-icon.fire {
    background: linear-gradient(45deg, var(--fire-color), #ff6b6b);
}

.template-icon.frost {
    background: linear-gradient(45deg, var(--frost-color), #74b9ff);
}

.template-icon.arcane {
    background: linear-gradient(45deg, var(--arcane-color), #a29bfe);
}

.template-icon.nature {
    background: linear-gradient(45deg, var(--nature-color), #00b894);
}

.template-icon.shadow {
    background: linear-gradient(45deg, var(--shadow-color), #636e72);
}

.template-icon.holy {
    background: linear-gradient(45deg, var(--holy-color), #fdcb6e);
}

.template-icon.physical {
    background: linear-gradient(45deg, #95a5a6, #bdc3c7);
}

.template-card h4 {
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    font-family: 'Cinzel', serif;
}

.template-card p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Effect and Condition Styling */
.effect-type, .condition-type {
    background: var(--card-bg);
}

.effect-item .form-row, .condition-item .form-row {
    align-items: end;
}

.effect-item .btn-icon, .condition-item .btn-icon {
    margin-top: 1.5rem;
}

/* Advanced Mode Toggle */
.advanced-mode-active {
    background: var(--primary-gold) !important;
    color: var(--dark-bg) !important;
}

/* Checkbox Styling */
input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
    accent-color: var(--primary-gold);
}

label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
}

/* Effect Value Inputs */
.effect-value, .effect-duration, .effect-chance, .effect-tick-interval,
.effect-max-stacks, .effect-max-targets, .effect-proc-spell,
.condition-value {
    background: var(--card-bg);
}

/* Advanced Effects Visualization */
.advanced-effects-results {
    margin-top: 1rem;
}

.effects-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.effect-metric {
    background: var(--darker-bg);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--arcane-color);
    transition: all 0.3s ease;
}

.effect-metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.2);
}

.effect-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.effect-value {
    display: block;
    color: var(--arcane-color);
    font-size: 1.3rem;
    font-weight: 700;
}

/* Effects Timeline */
.effects-timeline {
    background: var(--darker-bg);
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.effect-iteration {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.effect-iteration:hover {
    border-color: var(--arcane-color);
    transform: translateX(4px);
}

.iteration-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.iteration-number {
    font-weight: 700;
    color: var(--primary-gold);
    font-size: 1.1rem;
}

.iteration-damage {
    color: var(--danger-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.effect-events {
    display: grid;
    gap: 0.5rem;
}

.effect-event {
    background: var(--darker-bg);
    padding: 0.75rem;
    border-radius: 4px;
    border-left: 3px solid var(--primary-blue);
    transition: all 0.3s ease;
}

.effect-event.base-damage {
    border-left-color: var(--fire-color);
}

.effect-event.advanced-effect {
    border-left-color: var(--arcane-color);
}

.effect-event.chain-effect {
    border-left-color: var(--nature-color);
}

.effect-event.stacking-effect {
    border-left-color: var(--primary-gold);
}

.effect-event.proc-effect {
    border-left-color: var(--frost-color);
}

.effect-event:hover {
    background: var(--card-bg);
    transform: translateX(2px);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.event-type {
    font-weight: 600;
    color: var(--text-light);
    text-transform: capitalize;
}

.event-damage {
    color: var(--danger-color);
    font-weight: 600;
}

.event-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Effect Type Badges */
.effect-type-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.effect-type-badge.chain {
    background: var(--nature-color);
    color: white;
}

.effect-type-badge.stacking {
    background: var(--primary-gold);
    color: var(--dark-bg);
}

.effect-type-badge.proc {
    background: var(--frost-color);
    color: white;
}

.effect-type-badge.conditional {
    background: var(--arcane-color);
    color: white;
}

.effect-type-badge.synergy {
    background: var(--primary-purple);
    color: white;
}

/* Intuitive Spell Builder */
.builder-subtitle {
    text-align: center;
    color: var(--text-secondary);
    margin: 0.5rem 0 1rem 0;
    font-style: italic;
}

.builder-progress {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
    gap: 1rem;
}

/* Quick Spell Types */
.quick-spell-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.spell-type-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.spell-type-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.spell-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.spell-type-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.5rem;
    color: white;
}

.spell-type-card h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.spell-type-card p {
    margin: 0 0 1rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.spell-examples {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-style: italic;
}

.spell-examples span {
    background: rgba(52, 152, 219, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin: 0 0.2rem;
}

/* Spell Effect Options */
.spell-type-details {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--darker-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.spell-effect-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.effect-option-btn {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.effect-option-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.effect-option-btn.selected {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.effect-option-btn i {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
}

.effect-option-btn .effect-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.effect-option-btn .effect-desc {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Super Simple Spell Builder */
.spell-preview-card {
    background: var(--darker-bg);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.spell-preview-card h4 {
    margin: 0 0 1.5rem 0;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.spell-preview-name {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.spell-preview-description {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-style: italic;
}

.spell-preview-stats {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 1rem;
}

.preview-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.3rem;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: bold;
    color: var(--primary-color);
}

.simple-controls {
    max-width: 600px;
    margin: 0 auto;
}

.simple-control-group {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.simple-control-group label {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.simple-control-group label i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.simple-control-group input[type="text"] {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--darker-bg);
    color: var(--text-primary);
    font-size: 1rem;
}

.simple-control-group input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
}

.simple-spell-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
}

.simple-type-btn {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--darker-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    text-align: center;
}

.simple-type-btn:hover {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.simple-type-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.simple-type-btn i {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.simple-slider-control {
    position: relative;
}

.simple-slider-control input[type="range"] {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: var(--border-color);
    outline: none;
    margin: 1rem 0;
}

.simple-slider-control input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.simple-control-group select {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--darker-bg);
    color: var(--text-primary);
    font-size: 1rem;
}

.simple-control-group select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.btn-large {
    width: 100%;
    padding: 1.2rem 2rem;
    font-size: 1.2rem;
    font-weight: bold;
}

/* Trigger Configuration */
.trigger-config {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.trigger-section {
    margin-bottom: 1.5rem;
}

.trigger-section h6 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
}

.trigger-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0.8rem 0;
}

.trigger-row label {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.trigger-row select,
.trigger-row input {
    flex: 2;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--card-bg);
    color: var(--text-primary);
}

.trigger-condition-group {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.trigger-condition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.trigger-condition-type {
    font-weight: 600;
    color: var(--text-primary);
}

.trigger-remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.3rem 0.6rem;
    cursor: pointer;
    font-size: 0.8rem;
}

.trigger-add-btn {
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    margin-top: 1rem;
}

.trigger-logic-operator {
    text-align: center;
    margin: 0.5rem 0;
    font-weight: 600;
    color: var(--text-secondary);
}

.event-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
    margin: 1rem 0;
}

.event-type-btn {
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--card-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 0.9rem;
}

.event-type-btn:hover {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.event-type-btn.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.trigger-preview {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.trigger-preview h6 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
}

.trigger-preview-text {
    font-style: italic;
    color: var(--text-secondary);
}

.wow-condition-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.wow-condition-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.wow-condition-card.selected {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.2);
}

.wow-condition-card.recommended {
    border-color: var(--success-color);
    background: rgba(46, 204, 113, 0.05);
}

.wow-condition-card.recommended:hover {
    border-color: var(--success-color);
    background: rgba(46, 204, 113, 0.1);
}

.condition-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.wow-condition-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.5rem;
    color: white;
}

.wow-condition-card.recommended .wow-condition-icon {
    background: var(--success-color);
}

.wow-condition-card h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.wow-condition-card p {
    margin: 0 0 1rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.3;
    flex-grow: 1;
}

.wow-condition-examples {
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    border-radius: 6px;
    margin-top: auto;
}

.wow-condition-examples span {
    background: rgba(52, 152, 219, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    margin: 0 0.2rem;
    font-weight: 500;
}

/* WoW Condition Configuration */
.wow-condition-config {
    margin-top: 2rem;
}

.wow-config-card {
    background: var(--darker-bg);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.wow-config-card h5 {
    margin: 0 0 1.5rem 0;
    color: var(--primary-color);
    font-size: 1.2rem;
    text-align: center;
}

.wow-config-content {
    margin: 1.5rem 0;
}

.wow-config-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.wow-config-section h6 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.wow-config-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0.5rem 0;
}

.wow-config-row label {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.wow-config-row select,
.wow-config-row input {
    flex: 2;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--card-bg);
    color: var(--text-primary);
}

.wow-config-slider {
    margin: 1rem 0;
}

.wow-config-slider label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.wow-config-slider input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
    margin: 0.5rem 0;
}

.wow-config-slider .slider-value {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: bold;
}

.wow-config-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.condition-option-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.condition-option-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.condition-option-card.selected {
    border-color: var(--secondary-color);
    background: rgba(155, 89, 182, 0.1);
}

.condition-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.5rem;
    color: white;
}

.condition-option-card h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.condition-option-card p {
    margin: 0 0 1rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.condition-examples {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-style: italic;
}

.condition-examples span {
    background: rgba(155, 89, 182, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin: 0 0.2rem;
}

/* Condition Details */
.condition-details {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--darker-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.condition-config {
    margin-top: 1rem;
}

.condition-slider {
    margin: 1rem 0;
}

.condition-slider label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.condition-slider input[type="range"] {
    width: 100%;
    margin: 0.5rem 0;
}

.condition-slider .slider-value {
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-top: 0.5rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    opacity: 0.5;
}

.progress-step.active {
    opacity: 1;
    background: var(--darker-bg);
    border: 2px solid var(--primary-gold);
}

.progress-step.completed {
    opacity: 0.8;
    background: var(--darker-bg);
    border: 2px solid var(--success-color);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--primary-gold);
    color: var(--dark-bg);
    font-weight: 700;
    font-size: 1.1rem;
}

.progress-step.completed .step-number {
    background: var(--success-color);
    color: white;
}

.progress-step.error {
    border-color: var(--danger-color);
    background: rgba(231, 76, 60, 0.1);
}

.progress-step.error .step-number {
    background: var(--danger-color);
    color: white;
}

.progress-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.progress-step.active:hover {
    transform: none;
}

.step-label {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.9rem;
}

.builder-step {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.builder-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-gold);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-shake {
    animation: shake 0.5s ease-in-out;
}

.success-pulse {
    animation: pulse 0.6s ease-in-out;
}

.step-description {
    color: var(--text-muted);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.5;
}

/* Effect Categories */
.effect-categories {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
}

.effect-category {
    background: var(--darker-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.effect-category h5 {
    color: var(--primary-gold);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.effect-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.effect-btn {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.effect-btn:hover {
    border-color: var(--primary-gold);
    background: var(--darker-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(244, 208, 63, 0.2);
}

.effect-btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.effect-btn:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

.effect-btn.selected {
    border-color: var(--primary-gold);
    background: var(--primary-gold);
    color: var(--dark-bg);
}

.effect-btn i {
    font-size: 1.5rem;
    color: var(--primary-gold);
}

.effect-btn.selected i {
    color: var(--dark-bg);
}

.effect-btn span {
    font-weight: 600;
    font-size: 1rem;
}

.effect-btn small {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.effect-btn.selected small {
    color: var(--dark-bg);
    opacity: 0.8;
}

/* Selected Effects */
.selected-effects {
    background: var(--darker-bg);
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
}

.selected-effects h5 {
    color: var(--primary-gold);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.effects-list {
    display: grid;
    gap: 1rem;
}

.effect-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.effect-item:hover {
    border-color: var(--primary-gold);
}

.effect-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.effect-name {
    font-weight: 600;
    color: var(--text-light);
}

.effect-desc {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.effect-actions {
    display: flex;
    gap: 0.5rem;
}

/* Conditions Builder */
.conditions-builder {
    background: var(--darker-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.condition-group {
    margin-bottom: 1.5rem;
}

.condition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.condition-label {
    font-weight: 600;
    color: var(--primary-gold);
    font-size: 1.1rem;
}

.logic-operator {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem 1rem;
    color: var(--text-light);
    font-weight: 600;
}

.conditions-list {
    display: grid;
    gap: 1rem;
    margin-bottom: 1rem;
}

.condition-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.condition-item:hover {
    border-color: var(--primary-gold);
    box-shadow: 0 2px 8px rgba(244, 208, 63, 0.1);
}

.condition-item.invalid {
    border-color: var(--danger-color);
    background: rgba(231, 76, 60, 0.05);
}

.condition-item.invalid::before {
    content: '⚠️';
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    color: var(--danger-color);
    font-size: 0.9rem;
}

.condition-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
}

.condition-description {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.9rem;
    font-style: italic;
}

.condition-preview {
    background: var(--card-bg);
    border: 1px solid var(--primary-gold);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.condition-preview h6 {
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logic-preview {
    font-family: 'Courier New', monospace;
    background: var(--darker-bg);
    padding: 0.75rem;
    border-radius: 4px;
    border-left: 3px solid var(--primary-gold);
}

.logic-text {
    color: var(--text-light);
    font-weight: 600;
}

.logic-text.incomplete {
    color: var(--warning-color);
    font-style: italic;
}

.logic-text.error {
    color: var(--danger-color);
}

.condition-part {
    display: inline-block;
    background: var(--darker-bg);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin: 0.125rem;
    border: 1px solid var(--border-color);
}

.condition-name {
    color: var(--primary-gold);
    font-weight: 600;
}

.condition-operator {
    color: var(--text-muted);
    margin: 0 0.25rem;
}

.condition-value {
    color: var(--primary-blue);
    font-weight: 600;
}

.logic-op {
    color: var(--primary-purple);
    font-weight: 700;
    margin: 0 0.5rem;
    text-transform: uppercase;
}

/* Effect Properties */
.effect-properties {
    display: grid;
    gap: 1.5rem;
}

.property-group {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
}

.property-group h6 {
    color: var(--primary-gold);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.property-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Builder Navigation */
.builder-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .results-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .results-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .notifications {
        left: 20px;
        right: 20px;
        max-width: none;
    }

    /* Advanced Builder Mobile Fixes */
    .builder-progress {
        flex-direction: column;
        gap: 0.5rem;
    }

    .progress-step {
        flex-direction: row;
        justify-content: flex-start;
        padding: 0.75rem;
    }

    .step-number {
        margin-right: 0.5rem;
    }

    .effect-categories {
        gap: 1rem;
    }

    .effect-buttons {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .effect-btn {
        padding: 1rem 0.75rem;
    }

    .conditions-builder {
        padding: 1rem;
    }

    .condition-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .builder-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .builder-navigation button {
        width: 100%;
    }
}

/* Additional responsive breakpoints */
@media (max-width: 1200px) {
    .effect-buttons {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .builder-progress {
        gap: 1rem;
    }

    .condition-row {
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .demo-title {
        font-size: 2rem;
    }

    .effect-btn {
        padding: 0.75rem;
    }

    .effect-btn i {
        font-size: 1.2rem;
    }

    .condition-item {
        padding: 0.75rem;
    }

    .property-grid {
        grid-template-columns: 1fr;
    }

    .nav-btn span {
        display: none;
    }

    .nav-btn {
        padding: 0.75rem 0.5rem;
    }
}
