/* WoW Simulator - Main Stylesheet */

/* CSS Variables for WoW-themed colors */
:root {
    --primary-gold: #f4d03f;
    --primary-blue: #2980b9;
    --primary-purple: #8e44ad;
    --dark-bg: #1a1a2e;
    --darker-bg: #16213e;
    --card-bg: #0f3460;
    --text-light: #ecf0f1;
    --text-muted: #bdc3c7;
    --border-color: #34495e;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --fire-color: #e74c3c;
    --frost-color: #3498db;
    --arcane-color: #9b59b6;
    --shadow-color: #2c3e50;
    --nature-color: #27ae60;
    --holy-color: #f1c40f;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
    color: var(--text-light);
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(90deg, var(--card-bg) 0%, var(--darker-bg) 100%);
    border-bottom: 3px solid var(--primary-gold);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-gold);
    text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);
}

.logo h1 {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-gold);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-left: 0.5rem;
}

/* Navigation */
.nav {
    display: flex;
    gap: 0.5rem;
}

.nav-btn {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-light);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn:hover {
    border-color: var(--primary-gold);
    background: rgba(244, 208, 63, 0.1);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: var(--primary-gold);
    color: var(--dark-bg);
    border-color: var(--primary-gold);
    box-shadow: 0 4px 15px rgba(244, 208, 63, 0.3);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.section-header p {
    color: var(--text-muted);
    font-size: 1.1rem;
}

/* Grid Layout */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Card Styles */
.card {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.card-header {
    background: linear-gradient(90deg, var(--primary-purple) 0%, var(--primary-blue) 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-light);
    font-weight: 600;
    font-size: 0.9rem;
}

input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    background: var(--darker-bg);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-light);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(244, 208, 63, 0.2);
}

textarea {
    resize: vertical;
    min-height: 80px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-input label {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-gold) 0%, #f1c40f 100%);
    color: var(--dark-bg);
    box-shadow: 0 4px 15px rgba(244, 208, 63, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 208, 63, 0.4);
}

.btn-secondary {
    background: var(--primary-blue);
    color: white;
    box-shadow: 0 4px 15px rgba(41, 128, 185, 0.3);
}

.btn-secondary:hover {
    background: #3498db;
    transform: translateY(-2px);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Character Summary */
.character-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--darker-bg);
    border-radius: 6px;
    border-left: 4px solid var(--primary-gold);
}

.summary-item .label {
    color: var(--text-muted);
    font-weight: 600;
}

.summary-item .value {
    color: var(--primary-gold);
    font-weight: 700;
}

/* Spell List */
.spell-list {
    display: grid;
    gap: 1rem;
}

.spell-item {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.spell-item:hover {
    border-color: var(--primary-gold);
    transform: translateX(4px);
}

.spell-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.spell-name {
    font-weight: 700;
    color: var(--primary-gold);
    font-size: 1.1rem;
}

.spell-school {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.spell-school.fire { background: var(--fire-color); color: white; }
.spell-school.frost { background: var(--frost-color); color: white; }
.spell-school.arcane { background: var(--arcane-color); color: white; }
.spell-school.shadow { background: var(--shadow-color); color: white; }
.spell-school.nature { background: var(--nature-color); color: white; }
.spell-school.holy { background: var(--holy-color); color: var(--dark-bg); }

.spell-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.spell-stat {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.spell-stat strong {
    color: var(--text-light);
}

/* Empty State */
.empty-state {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

/* Spell Selection */
.spell-selection {
    display: grid;
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.spell-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--darker-bg);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.spell-checkbox:hover {
    background: var(--border-color);
}

.spell-checkbox input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Rotation Results */
.rotation-results {
    margin-top: 1rem;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric {
    background: var(--darker-bg);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-gold);
}

.metric-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.metric-value {
    display: block;
    color: var(--primary-gold);
    font-size: 1.5rem;
    font-weight: 700;
}

/* Rotation Timeline */
.rotation-timeline {
    background: var(--darker-bg);
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.3s ease;
}

.timeline-item:hover {
    background: var(--card-bg);
}

.timeline-time {
    font-weight: 700;
    color: var(--primary-gold);
    min-width: 60px;
}

.timeline-spell {
    flex: 1;
    color: var(--text-light);
}

.timeline-damage {
    color: var(--success-color);
    font-weight: 600;
}

.timeline-mana {
    color: var(--primary-blue);
    font-size: 0.9rem;
}

/* Test Results */
.test-results {
    margin-top: 1rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.result-item {
    background: var(--darker-bg);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-blue);
}

.result-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.result-value {
    display: block;
    color: var(--primary-blue);
    font-size: 1.3rem;
    font-weight: 700;
}

/* Test History */
.test-history {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.history-item:hover {
    border-color: var(--primary-gold);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-spell {
    font-weight: 700;
    color: var(--primary-gold);
}

.history-timestamp {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    font-size: 0.9rem;
}

.history-stat {
    color: var(--text-muted);
}

.history-stat strong {
    color: var(--text-light);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: var(--primary-gold);
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: 700;
    color: var(--text-light);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.2rem;
}

.notification-message {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Range Input Styling */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
    background: var(--border-color);
    height: 6px;
    border-radius: 3px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: var(--primary-gold);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
}

input[type="range"]::-moz-range-track {
    background: var(--border-color);
    height: 6px;
    border-radius: 3px;
    border: none;
}

input[type="range"]::-moz-range-thumb {
    background: var(--primary-gold);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .results-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .results-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .notifications {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}
