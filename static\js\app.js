// WoW Simulator - Main JavaScript Application

// Global state
let currentCharacter = null;
let createdSpells = [];
let spellTemplates = {};
let advancedTemplates = {};
let sessionId = null;
let backendAvailable = false;

// API Base URL - adjust this based on your backend setup
const API_BASE = 'http://localhost:5000/api';

// API Configuration
const API_CONFIG = {
    credentials: 'include',  // Include cookies for session management
    headers: {
        'Content-Type': 'application/json',
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
async function initializeApp() {
    try {
        showLoading(true);

        // Check backend availability and initialize session
        await initializeSession();

        // Set up navigation
        const navButtons = document.querySelectorAll('.nav-btn');
        navButtons.forEach(btn => {
            btn.addEventListener('click', () => switchSection(btn.dataset.section));
        });

        // Set up form listeners
        setupFormListeners();

        // Load initial data
        await loadSpellTemplates();
        await loadExistingData();

        // Initialize character with default values
        updateCharacterSummary();

        // Check for demo template from advanced effects demo
        checkForDemoTemplate();

        // Initialize spell builder
        initializeSpellBuilder();

        showNotification('Application initialized successfully!', 'success');

    } catch (error) {
        console.error('Error initializing application:', error);
        showNotification('Failed to initialize application. Some features may not work.', 'error');
    } finally {
        showLoading(false);
    }
}

// Initialize session with backend
async function initializeSession() {
    try {
        const response = await fetch(`${API_BASE}/session`, API_CONFIG);
        const result = await response.json();

        if (result.success) {
            sessionId = result.session_id;
            backendAvailable = true;
            console.log('Session initialized:', sessionId);

            // Update UI to show session info
            updateSessionInfo(result.session_info);
        } else {
            throw new Error(result.error);
        }

    } catch (error) {
        console.error('Backend not available:', error);
        backendAvailable = false;
        showNotification('Backend unavailable - using local storage mode', 'warning');
    }
}

// Update session info in UI
function updateSessionInfo(sessionInfo) {
    // You can add a session info display in the header if desired
    console.log('Session info:', sessionInfo);
}

// Load existing data from backend
async function loadExistingData() {
    if (!backendAvailable) return;

    try {
        // Load characters
        const charactersResponse = await fetch(`${API_BASE}/characters`, API_CONFIG);
        if (charactersResponse.ok) {
            const charactersResult = await charactersResponse.json();
            if (charactersResult.success && charactersResult.characters.length > 0) {
                // Set the first character as current
                const firstChar = charactersResult.characters[0];
                currentCharacter = firstChar.character;
                updateCharacterSummary();
            }
        }

        // Load spells
        const spellsResponse = await fetch(`${API_BASE}/spells`, API_CONFIG);
        if (spellsResponse.ok) {
            const spellsResult = await spellsResponse.json();
            if (spellsResult.success) {
                createdSpells = spellsResult.spells.map(item => ({
                    ...item.spell,
                    id: item.id,
                    created_at: item.created_at
                }));
                updateSpellList();
            }
        }

    } catch (error) {
        console.error('Error loading existing data:', error);
    }
}

// Setup form event listeners
function setupFormListeners() {
    // Character form inputs
    const charInputs = ['char-name', 'health', 'mana', 'spell-power', 'crit-chance', 'haste', 'hit-chance'];
    charInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateCharacterSummary);
        }
    });

    // Spell form inputs
    const spellInputs = ['spell-name', 'spell-description', 'spell-school', 'target-type', 
                        'cast-time', 'cooldown', 'mana-cost', 'base-damage-min', 
                        'base-damage-max', 'spell-power-coeff'];
    spellInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', validateSpellForm);
        }
    });
}

// Navigation functions
function switchSection(sectionName) {
    try {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        } else {
            console.warn(`Section not found: ${sectionName}-section`);
            return;
        }

        // Update navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const navButton = document.querySelector(`[data-section="${sectionName}"]`);
        if (navButton) {
            navButton.classList.add('active');
        } else {
            console.warn(`Navigation button not found for section: ${sectionName}`);
        }
    } catch (error) {
        console.error('Error switching section:', error);
    }
    
    // Load section-specific data
    if (sectionName === 'rotation') {
        updateAvailableSpells();
    } else if (sectionName === 'testing') {
        updateTestSpellOptions();
    }
}

// Character management functions
async function updateCharacter() {
    const characterData = {
        name: document.getElementById('char-name').value,
        class: document.getElementById('char-class').value,
        health: parseInt(document.getElementById('health').value),
        mana: parseInt(document.getElementById('mana').value),
        spell_power: parseInt(document.getElementById('spell-power').value),
        crit_chance: parseFloat(document.getElementById('crit-chance').value) / 100,
        haste: parseFloat(document.getElementById('haste').value) / 100,
        hit_chance: parseFloat(document.getElementById('hit-chance').value) / 100
    };

    if (!backendAvailable) {
        // Fallback to local storage
        currentCharacter = characterData;
        updateCharacterSummary();
        showNotification('Character updated locally (backend unavailable)', 'warning');
        return;
    }

    try {
        showLoading(true);

        const response = await fetch(`${API_BASE}/character`, {
            ...API_CONFIG,
            method: 'POST',
            body: JSON.stringify(characterData)
        });

        const result = await response.json();

        if (result.success) {
            currentCharacter = result.character;
            updateCharacterSummary();
            showNotification('Character updated successfully!', 'success');
        } else {
            showNotification(`Failed to update character: ${result.error}`, 'error');
        }

    } catch (error) {
        console.error('Error updating character:', error);
        showNotification('Failed to update character', 'error');
    } finally {
        showLoading(false);
    }
}

function updateCharacterSummary() {
    const name = document.getElementById('char-name').value;
    const health = document.getElementById('health').value;
    const mana = document.getElementById('mana').value;
    const spellPower = document.getElementById('spell-power').value;
    const critChance = document.getElementById('crit-chance').value;
    
    document.getElementById('summary-name').textContent = name;
    document.getElementById('summary-health').textContent = health;
    document.getElementById('summary-mana').textContent = mana;
    document.getElementById('summary-spell-power').textContent = spellPower;
    document.getElementById('summary-crit').textContent = critChance + '%';
}

// Spell management functions
async function loadSpellTemplates() {
    if (!backendAvailable) {
        // Use fallback templates when backend is unavailable
        loadFallbackTemplates();
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/spell-templates`, API_CONFIG);
        const result = await response.json();

        if (result.success) {
            spellTemplates = result.templates;
            console.log('Spell templates loaded from backend');
        } else {
            throw new Error(result.error);
        }

    } catch (error) {
        console.error('Error loading spell templates:', error);
        loadFallbackTemplates();
    }
}

function loadFallbackTemplates() {
    // Fallback to hardcoded templates
    spellTemplates = {
            direct_damage: {
                name: "New Direct Damage Spell",
                description: "A spell that deals instant damage to a target.",
                school: "fire",
                cast_time: 2.5,
                cooldown: 0.0,
                mana_cost: 200,
                target_type: "single_enemy",
                base_damage: [200, 300],
                spell_power_coefficient: 1.0,
                can_crit: true
            },
            damage_over_time: {
                name: "New DoT Spell",
                description: "A spell that deals damage over time.",
                school: "shadow",
                cast_time: 1.5,
                cooldown: 0.0,
                mana_cost: 150,
                target_type: "single_enemy",
                base_damage: [50, 75],
                spell_power_coefficient: 0.5,
                can_crit: false,
                effects: [{
                    type: "damage_over_time",
                    value: 25,
                    duration: 12.0,
                    tick_interval: 3.0
                }]
            },
            aoe_damage: {
                name: "New AoE Spell",
                description: "A spell that damages all enemies in an area.",
                school: "fire",
                cast_time: 3.0,
                cooldown: 0.0,
                mana_cost: 400,
                target_type: "ground_target",
                base_damage: [300, 400],
                spell_power_coefficient: 0.8,
                can_crit: true,
                aoe_radius: 8.0
            },
            instant_nuke: {
                name: "New Instant Spell",
                description: "An instant damage spell.",
                school: "arcane",
                cast_time: 0.0,
                cooldown: 6.0,
                mana_cost: 180,
                target_type: "single_enemy",
                base_damage: [150, 200],
                spell_power_coefficient: 0.6,
                can_crit: true
            }
        };
    console.log('Using fallback spell templates');
}

function loadSpellTemplate() {
    const templateName = document.getElementById('spell-template').value;
    if (!templateName || !spellTemplates[templateName]) return;
    
    const template = spellTemplates[templateName];
    
    // Populate form with template data
    document.getElementById('spell-name').value = template.name;
    document.getElementById('spell-description').value = template.description;
    document.getElementById('spell-school').value = template.school;
    document.getElementById('target-type').value = template.target_type;
    document.getElementById('cast-time').value = template.cast_time;
    document.getElementById('cooldown').value = template.cooldown;
    document.getElementById('mana-cost').value = template.mana_cost;
    
    if (Array.isArray(template.base_damage)) {
        document.getElementById('base-damage-min').value = template.base_damage[0];
        document.getElementById('base-damage-max').value = template.base_damage[1];
    } else {
        document.getElementById('base-damage-min').value = template.base_damage;
        document.getElementById('base-damage-max').value = template.base_damage;
    }
    
    document.getElementById('spell-power-coeff').value = template.spell_power_coefficient;
    
    showNotification(`Loaded template: ${template.name}`, 'success');
}

async function loadAdvancedTemplates() {
    try {
        showLoading(true);
        // This would call the backend API to get advanced templates
        // For now, show a placeholder message
        showNotification('Advanced templates feature coming soon!', 'warning');
    } catch (error) {
        console.error('Error loading advanced templates:', error);
        showNotification('Failed to load advanced templates', 'error');
    } finally {
        showLoading(false);
    }
}

function validateSpellForm() {
    try {
        const nameElement = document.getElementById('spell-name');
        const minDamageElement = document.getElementById('base-damage-min');
        const maxDamageElement = document.getElementById('base-damage-max');

        if (!nameElement || !minDamageElement || !maxDamageElement) {
            console.warn('Some form elements not found during validation');
            return false;
        }

        const name = nameElement.value;
        const minDamage = parseInt(minDamageElement.value) || 0;
        const maxDamage = parseInt(maxDamageElement.value) || 0;

        // Basic validation
        let isValid = true;
        let errors = [];

        if (!name.trim()) {
            errors.push('Spell name is required');
            isValid = false;
        }

        if (minDamage > maxDamage) {
            errors.push('Min damage cannot be greater than max damage');
            isValid = false;
        }

        // Update UI based on validation
        const createBtn = document.querySelector('button[onclick="createSpell()"]');
        if (createBtn) {
            createBtn.disabled = !isValid;
        }

        return isValid;
    } catch (error) {
        console.error('Error validating spell form:', error);
        return false;
    }
}

async function createSpell() {
    if (!validateSpellForm()) {
        showNotification('Please fix form errors before creating spell', 'error');
        return;
    }
    
    const spellData = {
        name: document.getElementById('spell-name').value,
        description: document.getElementById('spell-description').value,
        school: document.getElementById('spell-school').value,
        target_type: document.getElementById('target-type').value,
        cast_time: parseFloat(document.getElementById('cast-time').value),
        cooldown: parseFloat(document.getElementById('cooldown').value),
        mana_cost: parseInt(document.getElementById('mana-cost').value),
        base_damage: [
            parseInt(document.getElementById('base-damage-min').value),
            parseInt(document.getElementById('base-damage-max').value)
        ],
        spell_power_coefficient: parseFloat(document.getElementById('spell-power-coeff').value),
        can_crit: true
    };

    // Add advanced properties if in advanced mode
    if (advancedModeActive) {
        // Collect effects
        const effects = [];
        const effectItems = document.querySelectorAll('.effect-item');
        effectItems.forEach(item => {
            const effectType = item.querySelector('.effect-type').value;
            if (effectType) {
                const effect = {
                    type: effectType,
                    value: parseFloat(item.querySelector('.effect-value').value) || 0,
                    duration: parseFloat(item.querySelector('.effect-duration').value) || 0,
                    chance: (parseFloat(item.querySelector('.effect-chance').value) || 100) / 100
                };

                // Add advanced properties based on effect type
                if (['damage_over_time', 'heal_over_time'].includes(effectType)) {
                    effect.tick_interval = parseFloat(item.querySelector('.effect-tick-interval').value) || 3.0;
                }

                if (effectType === 'stacking') {
                    effect.max_stacks = parseInt(item.querySelector('.effect-max-stacks').value) || 1;
                }

                if (effectType === 'chain') {
                    effect.max_targets = parseInt(item.querySelector('.effect-max-targets').value) || 1;
                }

                if (effectType === 'proc') {
                    effect.proc_spell = item.querySelector('.effect-proc-spell').value || '';
                }

                effects.push(effect);
            }
        });

        if (effects.length > 0) {
            spellData.effects = effects;
        }

        // Collect conditions
        const conditions = [];
        const conditionItems = document.querySelectorAll('.condition-item');
        conditionItems.forEach(item => {
            const conditionType = item.querySelector('.condition-type').value;
            if (conditionType) {
                conditions.push({
                    condition_type: conditionType,
                    operator: item.querySelector('.condition-operator').value,
                    value: item.querySelector('.condition-value').value
                });
            }
        });

        if (conditions.length > 0) {
            spellData.conditions = conditions;
        }

        // Add channeling properties
        const channeledElement = document.getElementById('spell-channeled');
        const channelTicksElement = document.getElementById('channel-ticks');
        const interruptibleElement = document.getElementById('spell-interruptible');

        if (channeledElement) {
            spellData.channeled = channeledElement.checked;
            if (spellData.channeled && channelTicksElement) {
                spellData.channel_ticks = parseInt(channelTicksElement.value) || 1;
            }
        }

        if (interruptibleElement) {
            spellData.interruptible = interruptibleElement.checked;
        }
    }
    
    if (!backendAvailable) {
        // Fallback to local storage
        createdSpells.push({
            ...spellData,
            id: Date.now(),
            created_at: new Date().toISOString()
        });

        updateSpellList();
        clearSpellForm();
        showNotification(`Spell "${spellData.name}" created locally (backend unavailable)`, 'warning');
        return;
    }

    try {
        showLoading(true);

        const response = await fetch(`${API_BASE}/spell`, {
            ...API_CONFIG,
            method: 'POST',
            body: JSON.stringify(spellData)
        });

        const result = await response.json();

        if (result.success) {
            // Add to local array for immediate UI update
            createdSpells.push({
                ...result.spell,
                id: result.spell_id,
                created_at: result.spell.created_at || new Date().toISOString()
            });

            updateSpellList();
            clearSpellForm();
            showNotification(`Spell "${spellData.name}" created successfully!`, 'success');
        } else {
            if (result.validation_errors) {
                showNotification(`Validation errors: ${result.validation_errors.join(', ')}`, 'error');
            } else {
                showNotification(`Failed to create spell: ${result.error}`, 'error');
            }
        }

    } catch (error) {
        console.error('Error creating spell:', error);
        showNotification('Failed to create spell', 'error');
    } finally {
        showLoading(false);
    }
}

async function validateSpell() {
    if (!validateSpellForm()) {
        showNotification('Please fix form errors first', 'error');
        return;
    }
    
    showNotification('Spell configuration is valid!', 'success');
}

function clearSpellForm() {
    const inputs = ['spell-name', 'spell-description', 'cast-time', 'cooldown', 
                   'mana-cost', 'base-damage-min', 'base-damage-max', 'spell-power-coeff'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.value = '';
    });
    
    // Reset selects to first option
    document.getElementById('spell-school').selectedIndex = 0;
    document.getElementById('target-type').selectedIndex = 0;
    document.getElementById('spell-template').selectedIndex = 0;
}

function updateSpellList() {
    const spellList = document.getElementById('spell-list');

    if (createdSpells.length === 0) {
        spellList.innerHTML = '<p class="empty-state">No spells created yet. Use the form above to create your first spell!</p>';
        return;
    }

    spellList.innerHTML = createdSpells.map((spell, index) => `
        <div class="spell-item" data-spell-id="${spell.id}">
            <div class="spell-header">
                <span class="spell-name">${spell.name}</span>
                <div class="spell-header-actions">
                    <span class="spell-school ${spell.school}">${spell.school}</span>
                    <button class="btn-icon btn-danger" onclick="removeSpell(${index}, '${spell.id}')" title="Delete spell">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <p style="color: var(--text-muted); margin-bottom: 0.5rem;">${spell.description || 'No description'}</p>
            <div class="spell-stats">
                <div class="spell-stat">Cast: <strong>${spell.cast_time}s</strong></div>
                <div class="spell-stat">Damage: <strong>${spell.base_damage[0]}-${spell.base_damage[1]}</strong></div>
                <div class="spell-stat">Mana: <strong>${spell.mana_cost}</strong></div>
                <div class="spell-stat">Cooldown: <strong>${spell.cooldown}s</strong></div>
            </div>
        </div>
    `).join('');
}

async function removeSpell(index, spellId) {
    const spell = createdSpells[index];

    if (!spell) {
        showNotification('Spell not found', 'error');
        return;
    }

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to delete "${spell.name}"?`)) {
        return;
    }

    try {
        showLoading(true);

        // Remove from local array first for immediate UI feedback
        createdSpells.splice(index, 1);
        updateSpellList();
        updateAvailableSpells();
        updateTestSpellOptions();

        // If backend is available, also delete from server
        if (backendAvailable) {
            await deleteSpellFromBackend(spellId);
        }

        showNotification(`Spell "${spell.name}" deleted successfully`, 'success');

    } catch (error) {
        console.error('Error removing spell:', error);
        showNotification('Failed to delete spell', 'error');

        // Restore spell to local array if backend deletion failed
        if (backendAvailable) {
            createdSpells.splice(index, 0, spell);
            updateSpellList();
            updateAvailableSpells();
            updateTestSpellOptions();
        }
    } finally {
        showLoading(false);
    }
}

async function deleteSpellFromBackend(spellId) {
    try {
        const response = await fetch(`${API_BASE}/spell/${spellId}`, {
            ...API_CONFIG,
            method: 'DELETE'
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error);
        }

        console.log('Spell deleted from backend successfully');

    } catch (error) {
        console.error('Backend deletion failed:', error);
        throw error;  // Re-throw to handle in calling function
    }
}

function clearAllSpells() {
    if (createdSpells.length === 0) {
        showNotification('No spells to clear', 'warning');
        return;
    }

    const spellCount = createdSpells.length;

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to delete all ${spellCount} spells? This action cannot be undone.`)) {
        return;
    }

    try {
        // Clear the array
        createdSpells.length = 0;

        // Update all dependent UI elements
        updateSpellList();
        updateAvailableSpells();
        updateTestSpellOptions();

        showNotification(`All ${spellCount} spells cleared successfully`, 'success');

        // TODO: In the future, also call backend API to clear all spells
        // clearAllSpellsFromBackend();

    } catch (error) {
        console.error('Error clearing spells:', error);
        showNotification('Failed to clear spells', 'error');
    }
}

// Utility functions
function showLoading(show) {
    const overlay = document.getElementById('loading-overlay');
    if (!overlay) {
        console.warn('Loading overlay element not found');
        return;
    }

    if (show) {
        overlay.classList.add('active');
    } else {
        overlay.classList.remove('active');
    }
}

function showNotification(message, type = 'info') {
    const notifications = document.getElementById('notifications');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    notification.innerHTML = `
        <div class="notification-header">
            <span class="notification-title">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
        <div class="notification-message">${message}</div>
    `;

    notifications.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Rotation optimization functions
function updateMovementDisplay() {
    const movementTime = document.getElementById('movement-time').value;
    document.getElementById('movement-display').textContent = movementTime + '%';
}

function updateAvailableSpells() {
    const availableSpells = document.getElementById('available-spells');

    if (createdSpells.length === 0) {
        availableSpells.innerHTML = '<p class="empty-state">Create some spells first to optimize rotations!</p>';
        return;
    }

    availableSpells.innerHTML = createdSpells.map(spell => `
        <div class="spell-checkbox">
            <input type="checkbox" id="spell-${spell.id}" value="${spell.id}" checked>
            <label for="spell-${spell.id}">
                <strong>${spell.name}</strong> (${spell.school}) -
                ${spell.base_damage[0]}-${spell.base_damage[1]} damage,
                ${spell.cast_time}s cast, ${spell.mana_cost} mana
            </label>
        </div>
    `).join('');
}

async function optimizeRotation() {
    const selectedSpells = getSelectedSpells();
    if (selectedSpells.length === 0) {
        showNotification('Please select at least one spell for optimization', 'warning');
        return;
    }

    if (!currentCharacter) {
        showNotification('Please configure your character first', 'warning');
        return;
    }

    const optimizationData = {
        character: currentCharacter,
        spells: selectedSpells,
        goal: document.getElementById('optimization-goal').value,
        duration: parseInt(document.getElementById('fight-duration').value),
        target_count: parseInt(document.getElementById('target-count').value),
        movement_time: parseFloat(document.getElementById('movement-time').value) / 100
    };

    try {
        showLoading(true);

        // For now, simulate optimization results. Later this will call the backend API
        const mockResults = simulateOptimization(optimizationData);
        displayRotationResults(mockResults);

        showNotification('Rotation optimization completed!', 'success');

    } catch (error) {
        console.error('Error optimizing rotation:', error);
        showNotification('Failed to optimize rotation', 'error');
    } finally {
        showLoading(false);
    }
}

function getSelectedSpells() {
    const checkboxes = document.querySelectorAll('#available-spells input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => {
        const spellId = parseInt(cb.value);
        return createdSpells.find(spell => spell.id === spellId);
    }).filter(spell => spell);
}

function simulateOptimization(data) {
    // Mock optimization results for demonstration
    const { spells, duration, character } = data;

    let totalDamage = 0;
    let totalMana = 0;
    const timeline = [];
    let currentTime = 0;

    // Simple rotation simulation
    while (currentTime < duration) {
        for (const spell of spells) {
            if (currentTime >= duration) break;

            const avgDamage = (spell.base_damage[0] + spell.base_damage[1]) / 2;
            const spellPowerBonus = avgDamage * spell.spell_power_coefficient * (character.spell_power / 100);
            const finalDamage = Math.round(avgDamage + spellPowerBonus);

            totalDamage += finalDamage;
            totalMana += spell.mana_cost;

            timeline.push({
                time: currentTime.toFixed(1),
                spell: spell.name,
                damage: finalDamage,
                mana: spell.mana_cost
            });

            currentTime += Math.max(spell.cast_time, 1.5); // GCD minimum

            if (totalMana >= character.mana) break; // Out of mana
        }

        if (totalMana >= character.mana) break;
    }

    const dps = totalDamage / duration;
    const efficiency = totalDamage / totalMana;

    return {
        total_dps: dps.toFixed(1),
        total_damage: totalDamage,
        mana_used: totalMana,
        efficiency: efficiency.toFixed(2),
        timeline: timeline
    };
}

function displayRotationResults(results) {
    document.getElementById('total-dps').textContent = results.total_dps;
    document.getElementById('total-damage').textContent = results.total_damage.toLocaleString();
    document.getElementById('mana-used').textContent = results.mana_used.toLocaleString();
    document.getElementById('efficiency').textContent = results.efficiency + ' DPM';

    const timeline = document.getElementById('rotation-timeline');
    if (results.timeline.length === 0) {
        timeline.innerHTML = '<p class="empty-state">No rotation data available</p>';
        return;
    }

    timeline.innerHTML = results.timeline.map(item => `
        <div class="timeline-item">
            <span class="timeline-time">${item.time}s</span>
            <span class="timeline-spell">${item.spell}</span>
            <span class="timeline-damage">${item.damage} dmg</span>
            <span class="timeline-mana">-${item.mana} mana</span>
        </div>
    `).join('');
}

// Testing functions
function updateTestSpellOptions() {
    const testSpell = document.getElementById('test-spell');

    testSpell.innerHTML = '<option value="">Choose a spell to test...</option>';

    createdSpells.forEach(spell => {
        const option = document.createElement('option');
        option.value = spell.id;
        option.textContent = `${spell.name} (${spell.school})`;
        testSpell.appendChild(option);
    });
}

async function testSpell() {
    const spellId = document.getElementById('test-spell').value;
    if (!spellId) {
        showNotification('Please select a spell to test', 'warning');
        return;
    }

    const spell = createdSpells.find(s => s.id == spellId);
    if (!spell) {
        showNotification('Spell not found', 'error');
        return;
    }

    const iterations = parseInt(document.getElementById('test-iterations').value);
    const targetArmor = parseInt(document.getElementById('target-armor').value);

    try {
        showLoading(true);

        // Simulate spell testing
        const results = simulateSpellTest(spell, iterations, targetArmor);
        displayTestResults(results);
        addToTestHistory(spell, results);

        showNotification(`Tested ${spell.name} ${iterations} times`, 'success');

    } catch (error) {
        console.error('Error testing spell:', error);
        showNotification('Failed to test spell', 'error');
    } finally {
        showLoading(false);
    }
}

function simulateSpellTest(spell, iterations, targetArmor) {
    const damages = [];
    let crits = 0;

    for (let i = 0; i < iterations; i++) {
        const baseDamage = Math.random() * (spell.base_damage[1] - spell.base_damage[0]) + spell.base_damage[0];
        const spellPowerBonus = baseDamage * spell.spell_power_coefficient * (currentCharacter.spell_power / 100);
        let damage = baseDamage + spellPowerBonus;

        // Check for crit
        if (spell.can_crit && Math.random() < currentCharacter.crit_chance) {
            damage *= 2;
            crits++;
        }

        // Apply armor reduction (simplified)
        const armorReduction = targetArmor / (targetArmor + 400);
        damage *= (1 - armorReduction);

        damages.push(Math.round(damage));
    }

    const avgDamage = damages.reduce((a, b) => a + b, 0) / damages.length;
    const minDamage = Math.min(...damages);
    const maxDamage = Math.max(...damages);
    const critRate = (crits / iterations) * 100;
    const dps = avgDamage / Math.max(spell.cast_time, 1.5);
    const dpm = avgDamage / spell.mana_cost;

    return {
        avg_damage: avgDamage.toFixed(1),
        min_damage: minDamage,
        max_damage: maxDamage,
        crit_rate: critRate.toFixed(1) + '%',
        dps: dps.toFixed(1),
        dpm: dpm.toFixed(2),
        iterations: iterations
    };
}

function displayTestResults(results) {
    document.getElementById('avg-damage').textContent = results.avg_damage;
    document.getElementById('min-damage').textContent = results.min_damage;
    document.getElementById('max-damage').textContent = results.max_damage;
    document.getElementById('crit-rate').textContent = results.crit_rate;
    document.getElementById('spell-dps').textContent = results.dps;
    document.getElementById('spell-dpm').textContent = results.dpm;
}

function addToTestHistory(spell, results) {
    const history = document.getElementById('test-history');
    const timestamp = new Date().toLocaleTimeString();

    const historyItem = document.createElement('div');
    historyItem.className = 'history-item';
    historyItem.innerHTML = `
        <div class="history-header">
            <span class="history-spell">${spell.name}</span>
            <span class="history-timestamp">${timestamp}</span>
        </div>
        <div class="history-stats">
            <div class="history-stat">Avg: <strong>${results.avg_damage}</strong></div>
            <div class="history-stat">Min: <strong>${results.min_damage}</strong></div>
            <div class="history-stat">Max: <strong>${results.max_damage}</strong></div>
            <div class="history-stat">Crit: <strong>${results.crit_rate}</strong></div>
            <div class="history-stat">DPS: <strong>${results.dps}</strong></div>
        </div>
    `;

    // Remove empty state if it exists
    const emptyState = history.querySelector('.empty-state');
    if (emptyState) {
        emptyState.remove();
    }

    history.insertBefore(historyItem, history.firstChild);
}

async function testAllSpells() {
    if (createdSpells.length === 0) {
        showNotification('No spells to test', 'warning');
        return;
    }

    showNotification(`Testing all ${createdSpells.length} spells...`, 'info');

    for (const spell of createdSpells) {
        document.getElementById('test-spell').value = spell.id;
        await testSpell();
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    showNotification('All spells tested!', 'success');
}

// Advanced Effects Simulation
async function simulateAdvancedEffects() {
    const spellId = document.getElementById('test-spell').value;
    if (!spellId) {
        showNotification('Please select a spell to simulate', 'warning');
        return;
    }

    if (!backendAvailable) {
        showNotification('Advanced effects simulation requires backend connection', 'error');
        return;
    }

    const spell = createdSpells.find(s => s.id == spellId);
    if (!spell) {
        showNotification('Spell not found', 'error');
        return;
    }

    // Check if spell has advanced effects
    if (!spell.advanced_effects && !spell.effects) {
        showNotification('This spell has no advanced effects to simulate', 'warning');
        return;
    }

    try {
        showLoading(true);

        const simulationData = {
            spell_id: spellId,
            character_id: currentCharacter?.name || 'default',
            iterations: 10
        };

        const response = await fetch(`${API_BASE}/spell/simulate-advanced`, {
            ...API_CONFIG,
            method: 'POST',
            body: JSON.stringify(simulationData)
        });

        const result = await response.json();

        if (result.success) {
            displayAdvancedEffectsResults(result.simulation, spell);
            showNotification(`Advanced effects simulation completed for ${spell.name}!`, 'success');
        } else {
            showNotification(`Simulation failed: ${result.error}`, 'error');
        }

    } catch (error) {
        console.error('Error simulating advanced effects:', error);
        showNotification('Failed to simulate advanced effects', 'error');
    } finally {
        showLoading(false);
    }
}

function displayAdvancedEffectsResults(simulation, spell) {
    // Update summary metrics
    document.getElementById('effects-total-damage').textContent =
        simulation.total_damage?.toLocaleString() || '-';
    document.getElementById('effects-avg-damage').textContent =
        simulation.average_damage?.toFixed(1) || '-';
    document.getElementById('effects-triggered').textContent =
        simulation.effects_count || '0';

    const mostActive = simulation.summary?.most_triggered_effect;
    document.getElementById('most-active-effect').textContent =
        mostActive ? `${mostActive[0]} (${mostActive[1]}x)` : '-';

    // Display detailed timeline
    const timeline = document.getElementById('effects-timeline');

    if (!simulation.detailed_log || simulation.detailed_log.length === 0) {
        timeline.innerHTML = '<p class="empty-state">No detailed simulation data available</p>';
        return;
    }

    timeline.innerHTML = simulation.detailed_log.map(iteration => `
        <div class="effect-iteration">
            <div class="iteration-header">
                <span class="iteration-number">Iteration ${iteration.iteration}</span>
                <span class="iteration-damage">${iteration.total_damage} total damage</span>
            </div>
            <div class="effect-events">
                ${iteration.events.map(event => createEffectEventHTML(event)).join('')}
            </div>
        </div>
    `).join('');

    // Add summary information
    const summaryHTML = `
        <div class="simulation-summary">
            <h4>🎯 Simulation Summary</h4>
            <p><strong>Spell:</strong> ${spell.name}</p>
            <p><strong>Total Iterations:</strong> ${simulation.total_iterations}</p>
            <p><strong>Effects Analyzed:</strong> ${Object.keys(simulation.effect_triggers || {}).length}</p>
            ${Object.keys(simulation.effect_triggers || {}).length > 0 ? `
                <h5>Effect Trigger Counts:</h5>
                <ul>
                    ${Object.entries(simulation.effect_triggers).map(([name, count]) =>
                        `<li><strong>${name}:</strong> ${count} times</li>`
                    ).join('')}
                </ul>
            ` : ''}
        </div>
    `;

    timeline.insertAdjacentHTML('afterbegin', summaryHTML);
}

function createEffectEventHTML(event) {
    const eventTypeClass = event.type.replace('_', '-');
    let eventContent = '';

    if (event.type === 'base_damage') {
        eventContent = `
            <div class="event-header">
                <span class="event-type">Base Spell Damage</span>
                <span class="event-damage">${event.damage} damage</span>
            </div>
            <p class="event-description">Direct damage from ${event.spell}</p>
        `;
    } else if (event.type === 'advanced_effect') {
        const result = event.result || {};
        let damageText = '';

        if (result.damage_dealt) {
            damageText = `${result.damage_dealt} damage`;
        } else if (result.total_value) {
            damageText = `${result.total_value} value`;
        } else if (result.stacks) {
            damageText = `${result.stacks} stacks`;
        }

        eventContent = `
            <div class="event-header">
                <span class="event-type">
                    ${event.effect_name}
                    <span class="effect-type-badge ${event.effect_type}">${event.effect_type}</span>
                </span>
                ${damageText ? `<span class="event-damage">${damageText}</span>` : ''}
            </div>
            <p class="event-description">
                ${getEffectDescription(event.effect_type, result)}
            </p>
        `;
    }

    return `<div class="effect-event ${eventTypeClass}">${eventContent}</div>`;
}

function getEffectDescription(effectType, result) {
    switch (effectType) {
        case 'chain_effect':
            return `Lightning chains to nearby enemies, reducing damage with each jump`;
        case 'stacking_effect':
            return `Effect stacks increase power (${result.stacks || 0} stacks active)`;
        case 'proc_effect':
            return `Triggered additional effect based on spell conditions`;
        case 'conditional_effect':
            return `Conditional effect activated based on game state`;
        case 'synergy_effect':
            return `Synergy bonus applied to enhance other spells`;
        default:
            return `Advanced spell effect triggered`;
    }
}

// Demo template loading
function checkForDemoTemplate() {
    const demoTemplate = localStorage.getItem('demoTemplate');
    if (demoTemplate) {
        localStorage.removeItem('demoTemplate');

        // Switch to spells section
        switchSection('spells');

        // Enable advanced mode
        if (!advancedModeActive) {
            toggleAdvancedMode();
        }

        // Load the demo template
        setTimeout(() => {
            loadAdvancedTemplate(demoTemplate);
            showNotification(`🎭 Demo template "${demoTemplate}" loaded! Customize and create your spell.`, 'success');
        }, 1000);
    }
}

// User-Friendly Spell Builder
let currentBuilderStep = 1;
let selectedEffects = [];
let builderConditions = [];

function initializeSpellBuilder() {
    // Initialize builder state
    currentBuilderStep = 1;
    selectedEffects = [];
    builderConditions = [];

    // Initialize first condition
    builderConditions.push({
        type: '',
        operator: '==',
        value: '',
        description: ''
    });

    updateBuilderStep();

    // Add keyboard navigation
    document.addEventListener('keydown', handleBuilderKeyboard);

    // Initialize conditions UI
    setTimeout(() => {
        initializeConditionsUI();
    }, 100);
}

function initializeConditionsUI() {
    try {
        // Ensure the conditions list has the initial condition
        const conditionsList = document.getElementById('conditions-list');
        if (conditionsList && conditionsList.children.length === 0) {
            addCondition();
        }

        // Initialize logic preview
        updateLogicPreview();

    } catch (error) {
        console.error('Error initializing conditions UI:', error);
    }
}

function handleBuilderKeyboard(event) {
    // Only handle if advanced builder is active
    const advancedConfig = document.getElementById('advanced-spell-config');
    if (!advancedConfig || advancedConfig.style.display === 'none') {
        return;
    }

    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
            case 'ArrowLeft':
                event.preventDefault();
                if (currentBuilderStep > 1) {
                    previousStep();
                }
                break;
            case 'ArrowRight':
                event.preventDefault();
                if (currentBuilderStep < 3) {
                    nextStep();
                }
                break;
            case 'Enter':
                event.preventDefault();
                if (currentBuilderStep === 3) {
                    finishBuilder();
                }
                break;
        }
    }
}

function addQuickEffect(effectType) {
    try {
        const effectConfig = getEffectConfig(effectType);

        // Add visual feedback
        const button = event.target.closest('.effect-btn');
        if (button) {
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);
        }

        // Check if effect already exists
        const existingIndex = selectedEffects.findIndex(e => e.type === effectType);
        if (existingIndex >= 0) {
            // Remove if already selected
            selectedEffects.splice(existingIndex, 1);
            showNotification(`${effectConfig.name} removed`, 'info');
        } else {
            // Add new effect
            selectedEffects.push({
                type: effectType,
                name: effectConfig.name,
                description: effectConfig.description,
                config: effectConfig.defaultConfig
            });
            showNotification(`${effectConfig.name} added!`, 'success');
        }

        updateEffectsList();
        updateEffectButtons();
        updatePropertiesStep();

    } catch (error) {
        console.error('Error adding effect:', error);
        showNotification('Failed to add effect. Please try again.', 'error');
    }
}

function getEffectConfig(effectType) {
    const configs = {
        instant_damage: {
            name: "Instant Damage",
            description: "Deal immediate damage to target",
            defaultConfig: {
                value: 100,
                spell_power_coefficient: 1.0,
                can_crit: true
            }
        },
        damage_over_time: {
            name: "Damage Over Time",
            description: "Burn or poison target over time",
            defaultConfig: {
                value: 50,
                duration: 12.0,
                tick_interval: 3.0,
                spell_power_coefficient: 0.2
            }
        },
        heal_over_time: {
            name: "Heal Over Time",
            description: "Heal target over time",
            defaultConfig: {
                value: 75,
                duration: 15.0,
                tick_interval: 3.0,
                spell_power_coefficient: 0.4
            }
        },
        chain_damage: {
            name: "Chain Damage",
            description: "Jump between nearby enemies",
            defaultConfig: {
                value: 1.0,
                max_targets: 3,
                damage_reduction_per_jump: 0.3,
                max_range: 8.0
            }
        },
        aoe_damage: {
            name: "Area Damage",
            description: "Hit multiple targets in area",
            defaultConfig: {
                value: 80,
                radius: 8.0,
                max_targets: 8,
                spell_power_coefficient: 0.8
            }
        },
        proc_effect: {
            name: "Proc Effect",
            description: "Trigger other spells or effects",
            defaultConfig: {
                chance: 0.3,
                proc_spell: "Lightning Bolt",
                internal_cooldown: 1.0
            }
        },
        stacking_effect: {
            name: "Stacking Effect",
            description: "Build up power with repeated casts",
            defaultConfig: {
                value: 25,
                max_stacks: 5,
                duration: 10.0,
                stack_bonus: 1.0
            }
        },
        buff_effect: {
            name: "Buff",
            description: "Enhance caster's abilities",
            defaultConfig: {
                stat_bonus: "spell_power",
                value: 50,
                duration: 30.0
            }
        },
        debuff_effect: {
            name: "Debuff",
            description: "Weaken target's abilities",
            defaultConfig: {
                stat_penalty: "damage_reduction",
                value: 0.2,
                duration: 15.0
            }
        },
        stun_effect: {
            name: "Stun",
            description: "Disable target completely",
            defaultConfig: {
                duration: 3.0,
                break_on_damage: true
            }
        },
        slow_effect: {
            name: "Slow",
            description: "Reduce target movement speed",
            defaultConfig: {
                slow_percentage: 0.5,
                duration: 8.0
            }
        },
        transform_effect: {
            name: "Transform",
            description: "Change target into different form",
            defaultConfig: {
                transform_type: "sheep",
                duration: 8.0,
                break_on_damage: true
            }
        },
        silence_effect: {
            name: "Silence",
            description: "Prevent target from casting spells",
            defaultConfig: {
                duration: 4.0,
                spell_schools: ["all"]
            }
        }
    };

    return configs[effectType] || {
        name: "Unknown Effect",
        description: "Unknown effect type",
        defaultConfig: {}
    };
}

function updateEffectsList() {
    const effectsList = document.getElementById('effects-list');

    if (selectedEffects.length === 0) {
        effectsList.innerHTML = '<p class="empty-state">No effects selected. Choose effects above to add them.</p>';
        return;
    }

    effectsList.innerHTML = selectedEffects.map((effect, index) => `
        <div class="effect-item">
            <div class="effect-info">
                <div class="effect-name">${effect.name}</div>
                <div class="effect-desc">${effect.description}</div>
            </div>
            <div class="effect-actions">
                <button class="btn-icon btn-secondary" onclick="editEffect(${index})" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon btn-danger" onclick="removeEffect(${index})" title="Remove">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function updateEffectButtons() {
    // Update button states to show selected effects
    document.querySelectorAll('.effect-btn').forEach(btn => {
        const effectType = btn.getAttribute('onclick').match(/addQuickEffect\('([^']+)'\)/)?.[1];
        if (effectType) {
            const isSelected = selectedEffects.some(e => e.type === effectType);
            btn.classList.toggle('selected', isSelected);
        }
    });
}

function removeEffect(index) {
    const effect = selectedEffects[index];
    selectedEffects.splice(index, 1);
    updateEffectsList();
    updateEffectButtons();
    updatePropertiesStep();
    showNotification(`${effect.name} removed`, 'info');
}

function editEffect(index) {
    // Switch to properties step to edit the effect
    currentBuilderStep = 3;
    updateBuilderStep();

    // Highlight the effect being edited
    setTimeout(() => {
        const propertyGroups = document.querySelectorAll('.property-group');
        if (propertyGroups[index]) {
            propertyGroups[index].scrollIntoView({ behavior: 'smooth' });
            propertyGroups[index].style.border = '2px solid var(--primary-gold)';
            setTimeout(() => {
                propertyGroups[index].style.border = '1px solid var(--border-color)';
            }, 2000);
        }
    }, 300);
}

// Conditions System
function addCondition() {
    const conditionsList = document.getElementById('conditions-list');
    const conditionIndex = builderConditions.length;

    const conditionHtml = `
        <div class="condition-item">
            <div class="condition-row">
                <select class="condition-type" onchange="updateConditionOptions(this)" data-index="${conditionIndex}">
                    <option value="">Always trigger (no conditions)</option>
                    <optgroup label="Spell Conditions">
                        <option value="spell_critical">Spell critically hits</option>
                        <option value="spell_hit">Spell hits target</option>
                        <option value="spell_miss">Spell misses target</option>
                        <option value="spell_school">Spell is specific school</option>
                        <option value="spell_type">Spell is specific type</option>
                        <option value="spell_damage_type">Damage type (physical/magical)</option>
                        <option value="spell_cast_time">Spell cast time</option>
                        <option value="spell_mana_cost">Spell mana cost</option>
                        <option value="spell_on_cooldown">Spell is on cooldown</option>
                        <option value="spell_recently_cast">Spell cast recently</option>
                        <option value="spell_power_above">Spell power above threshold</option>
                        <option value="spell_interrupted">Spell was interrupted</option>
                    </optgroup>
                    <optgroup label="Target Conditions">
                        <option value="target_health_below">Target health below %</option>
                        <option value="target_health_above">Target health above %</option>
                        <option value="target_type">Target is specific type</option>
                        <option value="target_has_buff">Target has buff</option>
                        <option value="target_has_debuff">Target has debuff</option>
                        <option value="target_level">Target level</option>
                        <option value="target_armor">Target armor value</option>
                        <option value="target_resistance">Target resistance</option>
                        <option value="target_moving">Target is moving</option>
                        <option value="target_casting">Target is casting</option>
                        <option value="target_stunned">Target is stunned</option>
                        <option value="target_silenced">Target is silenced</option>
                        <option value="target_feared">Target is feared</option>
                        <option value="target_rooted">Target is rooted</option>
                        <option value="target_polymorphed">Target is polymorphed</option>
                        <option value="target_distance">Distance to target</option>
                        <option value="target_behind">Target is behind caster</option>
                        <option value="target_facing">Target is facing caster</option>
                    </optgroup>
                    <optgroup label="Caster Conditions">
                        <option value="caster_health_below">Caster health below %</option>
                        <option value="caster_health_above">Caster health above %</option>
                        <option value="caster_mana_below">Caster mana below %</option>
                        <option value="caster_mana_above">Caster mana above %</option>
                        <option value="caster_has_buff">Caster has buff</option>
                        <option value="caster_has_debuff">Caster has debuff</option>
                        <option value="buff_present">Specific buff is active</option>
                        <option value="buff_stacks">Buff has X stacks</option>
                        <option value="caster_level">Caster level</option>
                        <option value="caster_class">Caster class</option>
                        <option value="caster_race">Caster race</option>
                        <option value="caster_moving">Caster is moving</option>
                        <option value="caster_casting">Caster is casting</option>
                        <option value="caster_in_combat">Caster is in combat</option>
                        <option value="caster_stealthed">Caster is stealthed</option>
                        <option value="caster_mounted">Caster is mounted</option>
                        <option value="caster_weapon_equipped">Weapon equipped</option>
                        <option value="caster_stance">Caster stance/form</option>
                    </optgroup>
                    <optgroup label="Proc Conditions">
                        <option value="proc_triggered">When specific proc triggers</option>
                        <option value="proc_chance">Random proc chance</option>
                        <option value="proc_cooldown_ready">Proc cooldown is ready</option>
                        <option value="consecutive_procs">Consecutive proc triggers</option>
                        <option value="proc_stacks">Proc effect has stacks</option>
                        <option value="proc_rate">Proc trigger rate</option>
                    </optgroup>
                    <optgroup label="Combat Conditions">
                        <option value="combat_time">Combat duration</option>
                        <option value="enemies_nearby">Enemies within range</option>
                        <option value="allies_nearby">Allies within range</option>
                        <option value="effect_expires">When effect expires</option>
                        <option value="spell_combo">Spell cast in sequence</option>
                        <option value="resource_threshold">Mana/Health threshold</option>
                        <option value="threat_level">Threat/Aggro level</option>
                        <option value="group_size">Party/Raid size</option>
                        <option value="combat_rating">Combat rating (hit/crit/haste)</option>
                        <option value="weapon_skill">Weapon skill level</option>
                        <option value="combo_points">Combo points (rogue/druid)</option>
                        <option value="rage_energy">Rage/Energy amount</option>
                        <option value="holy_power">Holy Power (paladin)</option>
                        <option value="soul_shards">Soul Shards (warlock)</option>
                    </optgroup>
                    <optgroup label="Environmental Conditions">
                        <option value="time_of_day">Time of day</option>
                        <option value="zone_type">Zone type (indoor/outdoor)</option>
                        <option value="weather">Weather conditions</option>
                        <option value="pvp_zone">PvP zone active</option>
                        <option value="sanctuary_zone">Sanctuary zone</option>
                        <option value="instance_type">Instance type (dungeon/raid)</option>
                        <option value="boss_encounter">Boss encounter active</option>
                        <option value="elite_target">Target is elite</option>
                        <option value="player_target">Target is player</option>
                        <option value="npc_target">Target is NPC</option>
                    </optgroup>
                    <optgroup label="Advanced Conditions">
                        <option value="damage_taken_recently">Damage taken recently</option>
                        <option value="healing_received">Healing received recently</option>
                        <option value="spell_reflect_active">Spell reflect active</option>
                        <option value="magic_immunity">Magic immunity active</option>
                        <option value="physical_immunity">Physical immunity active</option>
                        <option value="crowd_control_active">Crowd control active</option>
                        <option value="dispel_available">Dispel effect available</option>
                        <option value="interrupt_available">Interrupt available</option>
                        <option value="line_of_sight">Line of sight to target</option>
                        <option value="facing_target">Facing target</option>
                        <option value="behind_target">Behind target</option>
                        <option value="flanking_target">Flanking target</option>
                    </optgroup>
                </select>

                <select class="condition-operator" style="display: none;" data-index="${conditionIndex}">
                    <option value="==">=</option>
                    <option value="<"><</option>
                    <option value=">">></option>
                </select>

                <input type="text" class="condition-value" placeholder="Value" style="display: none;" data-index="${conditionIndex}">

                <button type="button" class="btn-icon btn-danger" onclick="removeCondition(this)" ${conditionIndex === 0 ? 'style="display: none;"' : ''}>
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="condition-description">
                <i class="fas fa-info-circle"></i>
                <span>Select a condition to see its description.</span>
            </div>
        </div>
    `;

    if (conditionIndex === 0) {
        conditionsList.innerHTML = conditionHtml;
    } else {
        conditionsList.insertAdjacentHTML('beforeend', conditionHtml);
    }

    builderConditions.push({
        type: '',
        operator: '==',
        value: '',
        description: ''
    });

    updateLogicPreview();
}

function removeCondition(button) {
    try {
        const conditionItem = button.closest('.condition-item');
        const conditionsList = conditionItem.parentNode;
        const index = Array.from(conditionsList.children).indexOf(conditionItem);

        if (index > 0) {  // Don't remove the first condition
            conditionItem.remove();
            builderConditions.splice(index, 1);

            // Update data-index attributes for remaining conditions
            updateConditionIndices();
            updateLogicPreview();

            showNotification('Condition removed', 'info');
        } else {
            // Clear the first condition instead of removing it
            clearCondition(conditionItem, 0);
        }

    } catch (error) {
        console.error('Error removing condition:', error);
        showNotification('Error removing condition', 'error');
    }
}

function clearCondition(conditionItem, index) {
    const typeSelect = conditionItem.querySelector('.condition-type');
    const operatorSelect = conditionItem.querySelector('.condition-operator');
    const valueInput = conditionItem.querySelector('.condition-value');
    const description = conditionItem.querySelector('.condition-description span');

    // Reset form elements
    typeSelect.value = '';
    operatorSelect.style.display = 'none';
    valueInput.style.display = 'none';
    valueInput.value = '';
    description.textContent = 'This effect will always trigger when the spell is cast.';

    // Reset condition data
    if (builderConditions[index]) {
        builderConditions[index] = {
            type: '',
            operator: '==',
            value: '',
            description: ''
        };
    }

    // Remove validation states
    conditionItem.classList.remove('invalid');

    updateLogicPreview();
}

function updateConditionIndices() {
    const conditionItems = document.querySelectorAll('.condition-item');
    conditionItems.forEach((item, index) => {
        const typeSelect = item.querySelector('.condition-type');
        const operatorSelect = item.querySelector('.condition-operator');
        const valueInput = item.querySelector('.condition-value');

        if (typeSelect) typeSelect.dataset.index = index;
        if (operatorSelect) operatorSelect.dataset.index = index;
        if (valueInput) valueInput.dataset.index = index;
    });
}

function updateConditionOptions(select) {
    try {
        const index = select.dataset.index || 0;
        const conditionType = select.value;
        const conditionItem = select.closest('.condition-item');
        const operator = conditionItem.querySelector('.condition-operator');
        const value = conditionItem.querySelector('.condition-value');
        const description = conditionItem.querySelector('.condition-description span');

        // Update condition in array
        if (builderConditions[index]) {
            builderConditions[index].type = conditionType;
            builderConditions[index].operator = '==';
            builderConditions[index].value = '';
        }

        if (conditionType === '') {
            operator.style.display = 'none';
            value.style.display = 'none';
            description.textContent = 'This effect will always trigger when the spell is cast.';

            // Remove validation error if present
            conditionItem.classList.remove('invalid');
        } else {
            operator.style.display = 'block';
            value.style.display = 'block';

            const config = getConditionConfig(conditionType);
            description.textContent = config.description;
            value.placeholder = config.placeholder;
            value.type = config.inputType;

            // Set up operator options based on condition type
            updateOperatorOptions(operator, config);

            // Add event listeners for value changes
            value.oninput = () => {
                if (builderConditions[index]) {
                    builderConditions[index].value = value.value;
                }
                validateCondition(conditionItem, conditionType, value.value);
                updateLogicPreview();
            };

            operator.onchange = () => {
                if (builderConditions[index]) {
                    builderConditions[index].operator = operator.value;
                }
                updateLogicPreview();
            };
        }

        updateLogicPreview();

    } catch (error) {
        console.error('Error updating condition options:', error);
        showNotification('Error updating condition options', 'error');
    }
}

function updateOperatorOptions(operatorSelect, config) {
    const operators = config.operators || [
        { value: "==", label: "=" },
        { value: "!=", label: "≠" },
        { value: "<", label: "<" },
        { value: "<=", label: "≤" },
        { value: ">", label: ">" },
        { value: ">=", label: "≥" }
    ];

    operatorSelect.innerHTML = operators.map(op =>
        `<option value="${op.value}">${op.label}</option>`
    ).join('');
}

function validateCondition(conditionItem, conditionType, value) {
    const config = getConditionConfig(conditionType);
    let isValid = true;

    if (conditionType && (!value || value.trim() === '')) {
        isValid = false;
    }

    // Type-specific validation
    if (config.inputType === 'number' && value) {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            isValid = false;
        }
    }

    // Update visual state
    conditionItem.classList.toggle('invalid', !isValid);

    return isValid;
}

function getConditionConfig(conditionType) {
    const configs = {
        // Spell Conditions
        spell_critical: {
            description: "Triggers when the spell critically hits the target",
            placeholder: "fire, frost, arcane, etc.",
            inputType: "text",
            operators: [
                { value: "==", label: "is school" },
                { value: "!=", label: "is not school" }
            ]
        },
        spell_hit: {
            description: "Triggers when the spell successfully hits the target",
            placeholder: "any",
            inputType: "text"
        },
        spell_miss: {
            description: "Triggers when the spell misses the target",
            placeholder: "any",
            inputType: "text"
        },
        spell_school: {
            description: "Triggers when spell is of specific magic school",
            placeholder: "fire, frost, arcane, shadow, nature, holy",
            inputType: "text"
        },
        spell_type: {
            description: "Triggers when spell is of specific type",
            placeholder: "instant, channeled, dot, aoe",
            inputType: "text"
        },
        spell_damage_type: {
            description: "Triggers based on damage type",
            placeholder: "physical, magical",
            inputType: "text"
        },
        spell_cast_time: {
            description: "Triggers based on spell cast time",
            placeholder: "2.5",
            inputType: "number"
        },
        spell_mana_cost: {
            description: "Triggers based on spell mana cost",
            placeholder: "200",
            inputType: "number"
        },
        spell_on_cooldown: {
            description: "Triggers when specific spell is on cooldown",
            placeholder: "Fireball",
            inputType: "text"
        },
        spell_recently_cast: {
            description: "Triggers when spell was cast recently",
            placeholder: "Fireball",
            inputType: "text"
        },
        spell_power_above: {
            description: "Triggers when spell power is above threshold",
            placeholder: "300",
            inputType: "number"
        },
        spell_interrupted: {
            description: "Triggers when spell casting was interrupted",
            placeholder: "any",
            inputType: "text"
        },

        // Target Conditions
        target_health_below: {
            description: "Triggers when target's health is below specified percentage",
            placeholder: "35",
            inputType: "number",
            operators: [
                { value: "<", label: "<" },
                { value: "<=", label: "≤" }
            ]
        },
        target_health_above: {
            description: "Triggers when target's health is above specified percentage",
            placeholder: "80",
            inputType: "number",
            operators: [
                { value: ">", label: ">" },
                { value: ">=", label: "≥" }
            ]
        },
        target_has_buff: {
            description: "Triggers when target has specific buff active",
            placeholder: "Blessing of Kings",
            inputType: "text"
        },
        target_has_debuff: {
            description: "Triggers when target has specific debuff active",
            placeholder: "Ignite",
            inputType: "text"
        },
        target_level: {
            description: "Triggers based on target level",
            placeholder: "60",
            inputType: "number"
        },
        target_armor: {
            description: "Triggers based on target armor value",
            placeholder: "5000",
            inputType: "number"
        },
        target_resistance: {
            description: "Triggers based on target resistance",
            placeholder: "fire:100",
            inputType: "text"
        },
        target_moving: {
            description: "Triggers when target is moving",
            placeholder: "true",
            inputType: "text"
        },
        target_casting: {
            description: "Triggers when target is casting a spell",
            placeholder: "any",
            inputType: "text"
        },
        target_stunned: {
            description: "Triggers when target is stunned",
            placeholder: "true",
            inputType: "text"
        },
        target_silenced: {
            description: "Triggers when target is silenced",
            placeholder: "true",
            inputType: "text"
        },
        target_feared: {
            description: "Triggers when target is feared",
            placeholder: "true",
            inputType: "text"
        },
        target_rooted: {
            description: "Triggers when target is rooted/immobilized",
            placeholder: "true",
            inputType: "text"
        },
        target_polymorphed: {
            description: "Triggers when target is polymorphed",
            placeholder: "true",
            inputType: "text"
        },
        target_distance: {
            description: "Triggers based on distance to target",
            placeholder: "30",
            inputType: "number"
        },
        target_behind: {
            description: "Triggers when target is behind caster",
            placeholder: "true",
            inputType: "text"
        },
        target_facing: {
            description: "Triggers when target is facing caster",
            placeholder: "true",
            inputType: "text"
        },

        // Caster Conditions
        caster_health_below: {
            description: "Triggers when caster's health is below specified percentage",
            placeholder: "50",
            inputType: "number"
        },
        caster_health_above: {
            description: "Triggers when caster's health is above specified percentage",
            placeholder: "80",
            inputType: "number"
        },
        caster_mana_below: {
            description: "Triggers when caster's mana is below specified percentage",
            placeholder: "25",
            inputType: "number"
        },
        caster_mana_above: {
            description: "Triggers when caster's mana is above specified percentage",
            placeholder: "75",
            inputType: "number"
        },
        caster_has_debuff: {
            description: "Triggers when caster has specific debuff",
            placeholder: "Curse of Elements",
            inputType: "text"
        },
        caster_level: {
            description: "Triggers based on caster level",
            placeholder: "60",
            inputType: "number"
        },
        caster_class: {
            description: "Triggers based on caster class",
            placeholder: "mage, priest, warlock",
            inputType: "text"
        },
        caster_race: {
            description: "Triggers based on caster race",
            placeholder: "human, orc, undead",
            inputType: "text"
        },
        caster_moving: {
            description: "Triggers when caster is moving",
            placeholder: "true",
            inputType: "text"
        },
        caster_casting: {
            description: "Triggers when caster is casting",
            placeholder: "true",
            inputType: "text"
        },
        caster_in_combat: {
            description: "Triggers when caster is in combat",
            placeholder: "true",
            inputType: "text"
        },
        caster_stealthed: {
            description: "Triggers when caster is stealthed",
            placeholder: "true",
            inputType: "text"
        },
        caster_mounted: {
            description: "Triggers when caster is mounted",
            placeholder: "true",
            inputType: "text"
        },
        caster_weapon_equipped: {
            description: "Triggers based on equipped weapon",
            placeholder: "staff, sword, wand",
            inputType: "text"
        },
        caster_stance: {
            description: "Triggers based on caster stance/form",
            placeholder: "bear, cat, defensive",
            inputType: "text"
        },
        buff_present: {
            description: "Triggers when specified buff is active on caster",
            placeholder: "Hot Streak",
            inputType: "text"
        },
        buff_stacks: {
            description: "Triggers when buff has specific number of stacks",
            placeholder: "3",
            inputType: "number"
        },

        // Proc Conditions
        proc_triggered: {
            description: "Triggers when a specific proc effect activates",
            placeholder: "Hot Streak, Clearcasting, Impact",
            inputType: "text"
        },
        proc_chance: {
            description: "Random chance to trigger (independent of other conditions)",
            placeholder: "30 (for 30% chance)",
            inputType: "number"
        },
        proc_cooldown_ready: {
            description: "Triggers when specified proc's internal cooldown is ready",
            placeholder: "Ignite",
            inputType: "text"
        },
        consecutive_procs: {
            description: "Triggers after X consecutive proc activations",
            placeholder: "2",
            inputType: "number"
        },
        proc_stacks: {
            description: "Triggers when proc effect has specific number of stacks",
            placeholder: "5",
            inputType: "number"
        },
        proc_rate: {
            description: "Triggers based on proc frequency (procs per minute)",
            placeholder: "10",
            inputType: "number"
        },

        // Combat Conditions
        combat_time: {
            description: "Triggers after being in combat for specified seconds",
            placeholder: "30",
            inputType: "number"
        },
        enemies_nearby: {
            description: "Triggers when X or more enemies are within range",
            placeholder: "3",
            inputType: "number"
        },
        allies_nearby: {
            description: "Triggers when X or more allies are within range",
            placeholder: "5",
            inputType: "number"
        },
        effect_expires: {
            description: "Triggers when specified effect expires naturally",
            placeholder: "Living Bomb DoT",
            inputType: "text"
        },
        spell_combo: {
            description: "Triggers when spells are cast in specific sequence",
            placeholder: "Fireball,Fire Blast",
            inputType: "text"
        },
        resource_threshold: {
            description: "Triggers when mana or health crosses threshold",
            placeholder: "mana:50 or health:25",
            inputType: "text"
        },
        threat_level: {
            description: "Triggers based on threat/aggro level",
            placeholder: "high, medium, low",
            inputType: "text"
        },
        group_size: {
            description: "Triggers based on party/raid size",
            placeholder: "5",
            inputType: "number"
        },
        combat_rating: {
            description: "Triggers based on combat rating",
            placeholder: "hit:300, crit:200",
            inputType: "text"
        },
        weapon_skill: {
            description: "Triggers based on weapon skill level",
            placeholder: "300",
            inputType: "number"
        },
        combo_points: {
            description: "Triggers based on combo points",
            placeholder: "5",
            inputType: "number"
        },
        rage_energy: {
            description: "Triggers based on rage/energy amount",
            placeholder: "80",
            inputType: "number"
        },
        holy_power: {
            description: "Triggers based on holy power",
            placeholder: "3",
            inputType: "number"
        },
        soul_shards: {
            description: "Triggers based on soul shards",
            placeholder: "5",
            inputType: "number"
        },

        // Environmental Conditions
        time_of_day: {
            description: "Triggers based on time of day",
            placeholder: "day, night, dawn, dusk",
            inputType: "text"
        },
        zone_type: {
            description: "Triggers based on zone type",
            placeholder: "indoor, outdoor",
            inputType: "text"
        },
        weather: {
            description: "Triggers based on weather conditions",
            placeholder: "rain, snow, clear",
            inputType: "text"
        },
        pvp_zone: {
            description: "Triggers when in PvP zone",
            placeholder: "true",
            inputType: "text"
        },
        sanctuary_zone: {
            description: "Triggers when in sanctuary zone",
            placeholder: "true",
            inputType: "text"
        },
        instance_type: {
            description: "Triggers based on instance type",
            placeholder: "dungeon, raid, battleground",
            inputType: "text"
        },
        boss_encounter: {
            description: "Triggers during boss encounter",
            placeholder: "true",
            inputType: "text"
        },
        elite_target: {
            description: "Triggers when target is elite",
            placeholder: "true",
            inputType: "text"
        },
        player_target: {
            description: "Triggers when target is player",
            placeholder: "true",
            inputType: "text"
        },
        npc_target: {
            description: "Triggers when target is NPC",
            placeholder: "true",
            inputType: "text"
        },

        // Advanced Conditions
        damage_taken_recently: {
            description: "Triggers when damage was taken recently",
            placeholder: "500",
            inputType: "number"
        },
        healing_received: {
            description: "Triggers when healing was received recently",
            placeholder: "300",
            inputType: "number"
        },
        spell_reflect_active: {
            description: "Triggers when spell reflect is active",
            placeholder: "true",
            inputType: "text"
        },
        magic_immunity: {
            description: "Triggers when magic immunity is active",
            placeholder: "true",
            inputType: "text"
        },
        physical_immunity: {
            description: "Triggers when physical immunity is active",
            placeholder: "true",
            inputType: "text"
        },
        crowd_control_active: {
            description: "Triggers when crowd control is active",
            placeholder: "stun, fear, polymorph",
            inputType: "text"
        },
        dispel_available: {
            description: "Triggers when dispel effect is available",
            placeholder: "magic, curse, poison",
            inputType: "text"
        },
        interrupt_available: {
            description: "Triggers when interrupt is available",
            placeholder: "true",
            inputType: "text"
        },
        line_of_sight: {
            description: "Triggers based on line of sight to target",
            placeholder: "true",
            inputType: "text"
        },
        facing_target: {
            description: "Triggers when facing target",
            placeholder: "true",
            inputType: "text"
        },
        behind_target: {
            description: "Triggers when behind target",
            placeholder: "true",
            inputType: "text"
        },
        flanking_target: {
            description: "Triggers when flanking target",
            placeholder: "true",
            inputType: "text"
        }
    };

    return configs[conditionType] || {
        description: "Custom condition",
        placeholder: "value",
        inputType: "text"
    };
}

function updateLogicPreview() {
    try {
        const logicOperator = document.querySelector('.logic-operator')?.value || 'AND';
        const logicPreview = document.getElementById('logic-preview');

        if (!logicPreview) return;

        const validConditions = builderConditions.filter(c => c.type && c.value);

        if (validConditions.length === 0) {
            const hasIncompleteConditions = builderConditions.some(c => c.type && !c.value);
            if (hasIncompleteConditions) {
                logicPreview.innerHTML = '<span class="logic-text incomplete">Complete condition values to see preview</span>';
            } else {
                logicPreview.innerHTML = '<span class="logic-text">Always trigger</span>';
            }
            return;
        }

        const conditionTexts = validConditions.map(condition => {
            const config = getConditionConfig(condition.type);
            const operatorLabel = getOperatorLabel(condition.operator);
            const displayValue = formatConditionValue(condition.value, config);

            return `<span class="condition-part">
                <span class="condition-name">${condition.type.replace(/_/g, ' ')}</span>
                <span class="condition-operator">${operatorLabel}</span>
                <span class="condition-value">${displayValue}</span>
            </span>`;
        });

        if (validConditions.length === 1) {
            logicPreview.innerHTML = `<span class="logic-text">IF ${conditionTexts[0]}</span>`;
        } else {
            const logicText = conditionTexts.join(` <span class="logic-op">${logicOperator}</span> `);
            logicPreview.innerHTML = `<span class="logic-text">IF ${logicText}</span>`;
        }

    } catch (error) {
        console.error('Error updating logic preview:', error);
        const logicPreview = document.getElementById('logic-preview');
        if (logicPreview) {
            logicPreview.innerHTML = '<span class="logic-text error">Error in condition preview</span>';
        }
    }
}

function getOperatorLabel(operator) {
    const operators = {
        '==': '=',
        '!=': '≠',
        '<': '<',
        '<=': '≤',
        '>': '>',
        '>=': '≥'
    };
    return operators[operator] || operator;
}

function formatConditionValue(value, config) {
    if (config.inputType === 'number') {
        const num = parseFloat(value);
        if (!isNaN(num)) {
            // Add percentage sign for health/mana conditions
            if (config.description.includes('percentage') || config.description.includes('%')) {
                return `${num}%`;
            }
            return num.toString();
        }
    }

    // Capitalize first letter for text values
    if (typeof value === 'string' && value.length > 0) {
        return value.charAt(0).toUpperCase() + value.slice(1);
    }

    return value;
}

// Builder Navigation
function nextStep() {
    if (currentBuilderStep < 3) {
        currentBuilderStep++;
        updateBuilderStep();
    }
}

function previousStep() {
    if (currentBuilderStep > 1) {
        currentBuilderStep--;
        updateBuilderStep();
    }
}

function updateBuilderStep() {
    // Update progress indicators with validation
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active', 'completed', 'error');

        if (stepNumber === currentBuilderStep) {
            step.classList.add('active');
        } else if (stepNumber < currentBuilderStep) {
            step.classList.add('completed');

            // Add validation indicators
            if (stepNumber === 1 && selectedEffects.length === 0) {
                step.classList.add('error');
            }
            if (stepNumber === 2) {
                const incompleteConditions = builderConditions.filter(c =>
                    c.type && (!c.value || c.value.trim() === '')
                );
                if (incompleteConditions.length > 0) {
                    step.classList.add('error');
                }
            }
        }

        // Make steps clickable for navigation
        step.style.cursor = 'pointer';
        step.onclick = () => {
            if (stepNumber <= currentBuilderStep || stepNumber === currentBuilderStep + 1) {
                currentBuilderStep = stepNumber;
                updateBuilderStep();
            }
        };
    });

    // Show/hide step content with animation
    document.querySelectorAll('.builder-step').forEach((step, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentBuilderStep;

        if (isActive && !step.classList.contains('active')) {
            step.style.opacity = '0';
            step.classList.add('active');
            setTimeout(() => {
                step.style.opacity = '1';
            }, 50);
        } else if (!isActive) {
            step.classList.remove('active');
        }
    });

    // Update navigation buttons
    const prevBtn = document.getElementById('prev-step');
    const nextBtn = document.getElementById('next-step');
    const finishBtn = document.getElementById('finish-builder');

    if (prevBtn) prevBtn.style.display = currentBuilderStep > 1 ? 'block' : 'none';
    if (nextBtn) nextBtn.style.display = currentBuilderStep < 3 ? 'block' : 'none';
    if (finishBtn) finishBtn.style.display = currentBuilderStep === 3 ? 'block' : 'none';

    // Update step-specific content
    if (currentBuilderStep === 2) {
        updateLogicPreview();
    } else if (currentBuilderStep === 3) {
        updatePropertiesStep();
    }

    // Update step completion indicators
    updateStepCompletionStatus();
}

function updateStepCompletionStatus() {
    // Step 1: Effects selection
    const step1Complete = selectedEffects.length > 0;

    // Step 2: Conditions (optional, but if started should be complete)
    const hasConditions = builderConditions.some(c => c.type);
    const incompleteConditions = builderConditions.filter(c =>
        c.type && (!c.value || c.value.trim() === '')
    );
    const step2Complete = !hasConditions || incompleteConditions.length === 0;

    // Step 3: Properties (always complete as they have defaults)
    const step3Complete = true;

    // Update visual indicators
    const steps = document.querySelectorAll('.progress-step');
    if (steps[0]) {
        steps[0].classList.toggle('error', !step1Complete);
        steps[0].title = step1Complete ? 'Effects selected' : 'Please select at least one effect';
    }
    if (steps[1]) {
        steps[1].classList.toggle('error', !step2Complete);
        steps[1].title = step2Complete ? 'Conditions configured' : 'Please complete condition values';
    }
    if (steps[2]) {
        steps[2].classList.toggle('error', !step3Complete);
        steps[2].title = 'Properties configured';
    }
}

function updatePropertiesStep() {
    const propertiesContainer = document.getElementById('effect-properties');

    if (selectedEffects.length === 0) {
        propertiesContainer.innerHTML = '<p class="empty-state">Add effects in Step 1 to configure their properties here.</p>';
        return;
    }

    propertiesContainer.innerHTML = selectedEffects.map((effect, index) => `
        <div class="property-group">
            <h6><i class="fas fa-cog"></i> ${effect.name} Properties</h6>
            <div class="property-grid">
                ${generatePropertyFields(effect, index)}
            </div>
        </div>
    `).join('');
}

function generatePropertyFields(effect, index) {
    const config = effect.config;
    let fields = [];

    // Common fields
    if (config.value !== undefined) {
        fields.push(`
            <div class="form-group">
                <label>Damage/Value</label>
                <input type="number" value="${config.value}" onchange="updateEffectProperty(${index}, 'value', this.value)">
            </div>
        `);
    }

    if (config.duration !== undefined) {
        fields.push(`
            <div class="form-group">
                <label>Duration (seconds)</label>
                <input type="number" value="${config.duration}" step="0.1" onchange="updateEffectProperty(${index}, 'duration', this.value)">
            </div>
        `);
    }

    if (config.spell_power_coefficient !== undefined) {
        fields.push(`
            <div class="form-group">
                <label>Spell Power Scaling</label>
                <input type="number" value="${config.spell_power_coefficient}" step="0.1" onchange="updateEffectProperty(${index}, 'spell_power_coefficient', this.value)">
            </div>
        `);
    }

    // Type-specific fields
    if (effect.type === 'damage_over_time') {
        fields.push(`
            <div class="form-group">
                <label>Tick Interval (seconds)</label>
                <input type="number" value="${config.tick_interval || 3}" step="0.1" onchange="updateEffectProperty(${index}, 'tick_interval', this.value)">
            </div>
        `);
    }

    if (effect.type === 'chain_damage') {
        fields.push(`
            <div class="form-group">
                <label>Max Targets</label>
                <input type="number" value="${config.max_targets || 3}" onchange="updateEffectProperty(${index}, 'max_targets', this.value)">
            </div>
            <div class="form-group">
                <label>Damage Reduction per Jump (%)</label>
                <input type="number" value="${(config.damage_reduction_per_jump || 0.3) * 100}" onchange="updateEffectProperty(${index}, 'damage_reduction_per_jump', this.value / 100)">
            </div>
        `);
    }

    if (effect.type === 'stacking_effect') {
        fields.push(`
            <div class="form-group">
                <label>Max Stacks</label>
                <input type="number" value="${config.max_stacks || 5}" onchange="updateEffectProperty(${index}, 'max_stacks', this.value)">
            </div>
        `);
    }

    return fields.join('');
}

function updateEffectProperty(effectIndex, property, value) {
    if (selectedEffects[effectIndex]) {
        selectedEffects[effectIndex].config[property] = parseFloat(value) || value;
    }
}

function finishBuilder() {
    // Validate builder state
    const validation = validateBuilderState();
    if (!validation.isValid) {
        showNotification(validation.message, 'warning');
        if (validation.step) {
            currentBuilderStep = validation.step;
            updateBuilderStep();
        }
        return;
    }

    // Convert builder data to spell format
    const spellData = convertBuilderToSpell();

    // Apply to spell form
    applyBuilderDataToForm(spellData);

    // Close advanced mode
    toggleAdvancedMode();

    showNotification('Spell configuration applied! Review and create your spell.', 'success');
}

function validateBuilderState() {
    // Check if effects are selected
    if (selectedEffects.length === 0) {
        return {
            isValid: false,
            message: 'Please add at least one effect before creating the spell',
            step: 1
        };
    }

    // Check for incomplete conditions
    const incompleteConditions = builderConditions.filter(c =>
        c.type && (!c.value || c.value.trim() === '')
    );

    if (incompleteConditions.length > 0) {
        return {
            isValid: false,
            message: 'Please complete all condition values or remove incomplete conditions',
            step: 2
        };
    }

    // Check for effects with missing required properties
    for (let i = 0; i < selectedEffects.length; i++) {
        const effect = selectedEffects[i];
        if (effect.type === 'damage_over_time' && !effect.config.tick_interval) {
            return {
                isValid: false,
                message: `Please configure tick interval for ${effect.name}`,
                step: 3
            };
        }

        if (effect.type === 'chain_damage' && !effect.config.max_targets) {
            return {
                isValid: false,
                message: `Please configure max targets for ${effect.name}`,
                step: 3
            };
        }
    }

    return { isValid: true };
}

function convertBuilderToSpell() {
    return {
        effects: selectedEffects.map(effect => ({
            type: effect.type,
            ...effect.config
        })),
        conditions: builderConditions.filter(c => c.type),
        advanced_effects: selectedEffects.map(effect => ({
            effect_type: mapEffectTypeToAdvanced(effect.type),
            name: effect.name,
            description: effect.description,
            ...effect.config
        }))
    };
}

function mapEffectTypeToAdvanced(builderType) {
    const mapping = {
        instant_damage: 'conditional_effect',
        damage_over_time: 'conditional_effect',
        chain_damage: 'chain_effect',
        aoe_damage: 'conditional_effect',
        proc_effect: 'proc_effect',
        stacking_effect: 'stacking_effect',
        buff_effect: 'conditional_effect',
        debuff_effect: 'conditional_effect',
        stun_effect: 'conditional_effect',
        slow_effect: 'conditional_effect',
        transform_effect: 'transformation_effect',
        silence_effect: 'conditional_effect'
    };

    return mapping[builderType] || 'conditional_effect';
}

function applyBuilderDataToForm(spellData) {
    try {
        // Apply basic spell data to form
        const spellName = document.getElementById('spell-name');
        const spellDescription = document.getElementById('spell-description');
        const spellSchool = document.getElementById('spell-school');

        if (spellName) spellName.value = spellData.name || 'Custom Spell';
        if (spellDescription) spellDescription.value = spellData.description || 'Created with Advanced Builder';
        if (spellSchool) spellSchool.value = spellData.school || 'arcane';

        // Store advanced data for backend processing
        window.builderSpellData = spellData;

        console.log('Generated spell data:', spellData);

    } catch (error) {
        console.error('Error applying builder data to form:', error);
        showNotification('Error applying spell data', 'error');
    }
}

function loadConditionsFromTemplate(conditions) {
    try {
        if (!conditions || !Array.isArray(conditions)) {
            return;
        }

        // Clear existing conditions
        builderConditions = [];

        // Load template conditions
        conditions.forEach(condition => {
            builderConditions.push({
                type: condition.condition_type || '',
                operator: condition.operator || '==',
                value: condition.value || '',
                description: ''
            });
        });

        // Ensure at least one condition exists
        if (builderConditions.length === 0) {
            builderConditions.push({
                type: '',
                operator: '==',
                value: '',
                description: ''
            });
        }

        // Update UI
        rebuildConditionsUI();
        updateLogicPreview();

    } catch (error) {
        console.error('Error loading conditions from template:', error);
    }
}

function rebuildConditionsUI() {
    const conditionsList = document.getElementById('conditions-list');
    if (!conditionsList) return;

    // Clear existing UI
    conditionsList.innerHTML = '';

    // Rebuild from conditions array
    builderConditions.forEach((condition, index) => {
        if (index === 0) {
            addCondition();
        } else {
            addCondition();
        }

        // Set values
        const conditionItem = conditionsList.children[index];
        if (conditionItem) {
            const typeSelect = conditionItem.querySelector('.condition-type');
            const operatorSelect = conditionItem.querySelector('.condition-operator');
            const valueInput = conditionItem.querySelector('.condition-value');

            if (typeSelect) {
                typeSelect.value = condition.type;
                updateConditionOptions(typeSelect);
            }

            if (operatorSelect && condition.operator) {
                operatorSelect.value = condition.operator;
            }

            if (valueInput && condition.value) {
                valueInput.value = condition.value;
            }
        }
    });
}

// Advanced Spell Builder Functions
let advancedModeActive = false;

function toggleAdvancedMode() {
    advancedModeActive = !advancedModeActive;

    const advancedConfig = document.getElementById('advanced-spell-config');
    const advancedTemplates = document.getElementById('advanced-templates');
    const toggleBtn = document.querySelector('button[onclick="toggleAdvancedMode()"]');
    const toggleText = document.getElementById('advanced-mode-text');

    if (advancedModeActive) {
        advancedConfig.style.display = 'block';
        advancedTemplates.style.display = 'block';
        toggleBtn.classList.add('advanced-mode-active');
        toggleText.textContent = 'Basic Mode';
        showNotification('Advanced spell builder activated!', 'success');
    } else {
        advancedConfig.style.display = 'none';
        advancedTemplates.style.display = 'none';
        toggleBtn.classList.remove('advanced-mode-active');
        toggleText.textContent = 'Advanced Mode';
        showNotification('Switched to basic spell builder', 'info');
    }
}

function addEffect() {
    try {
        const container = document.getElementById('spell-effects-container');
        if (!container) {
            console.error('Effects container not found');
            return;
        }

        const effectItem = document.createElement('div');
    effectItem.className = 'effect-item';
    effectItem.innerHTML = `
        <div class="form-row">
            <div class="form-group">
                <label>Effect Type</label>
                <select class="effect-type" onchange="updateEffectProperties(this)">
                    <option value="">Select effect...</option>
                    <option value="damage_over_time">Damage Over Time</option>
                    <option value="heal_over_time">Heal Over Time</option>
                    <option value="buff">Buff</option>
                    <option value="debuff">Debuff</option>
                    <option value="proc">Proc Effect</option>
                    <option value="conditional">Conditional Effect</option>
                    <option value="chain">Chain Effect</option>
                    <option value="stacking">Stacking Effect</option>
                </select>
            </div>
            <div class="form-group">
                <label>Value</label>
                <input type="number" class="effect-value" placeholder="Effect value">
            </div>
            <div class="form-group">
                <label>Duration (s)</label>
                <input type="number" class="effect-duration" placeholder="0" step="0.1">
            </div>
            <div class="form-group">
                <label>Chance (%)</label>
                <input type="number" class="effect-chance" placeholder="100" min="0" max="100">
            </div>
            <div class="form-group">
                <button type="button" class="btn-icon btn-danger" onclick="removeEffect(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="advanced-effect-properties" style="display: none;">
            <div class="form-row">
                <div class="form-group">
                    <label>Tick Interval (s)</label>
                    <input type="number" class="effect-tick-interval" placeholder="3.0" step="0.1">
                </div>
                <div class="form-group">
                    <label>Max Stacks</label>
                    <input type="number" class="effect-max-stacks" placeholder="1" min="1">
                </div>
                <div class="form-group">
                    <label>Max Targets</label>
                    <input type="number" class="effect-max-targets" placeholder="1" min="1">
                </div>
                <div class="form-group">
                    <label>Proc Spell</label>
                    <input type="text" class="effect-proc-spell" placeholder="Spell name">
                </div>
            </div>
        </div>
    `;

        container.appendChild(effectItem);
    } catch (error) {
        console.error('Error adding effect:', error);
        showNotification('Failed to add effect', 'error');
    }
}

// Duplicate removeEffect function removed - using the user-friendly builder version

function updateEffectProperties(select) {
    try {
        const effectItem = select.closest('.effect-item');
        if (!effectItem) return;

        const advancedProps = effectItem.querySelector('.advanced-effect-properties');
        if (!advancedProps) return;

        const effectType = select.value;

        // Show/hide advanced properties based on effect type
        if (['damage_over_time', 'heal_over_time', 'stacking', 'chain', 'proc'].includes(effectType)) {
            advancedProps.style.display = 'block';
        } else {
            advancedProps.style.display = 'none';
        }
    } catch (error) {
        console.error('Error updating effect properties:', error);
    }
}

// Duplicate functions removed - using the newer user-friendly builder versions above

async function loadAdvancedTemplate(templateName) {
    try {
        showLoading(true);

        // Load from backend if available, otherwise use fallback
        let template = null;

        if (backendAvailable) {
            try {
                const response = await fetch(`${API_BASE}/spell-templates`, API_CONFIG);
                const result = await response.json();

                if (result.success && result.templates[templateName]) {
                    template = result.templates[templateName];
                }
            } catch (error) {
                console.error('Error loading template from backend:', error);
            }
        }

        // Fallback to predefined templates if backend unavailable
        if (!template) {
            const advancedTemplates = {
            living_bomb: {
                name: "Living Bomb",
                description: "DoT that explodes when it expires",
                school: "fire",
                cast_time: 1.5,
                cooldown: 0.0,
                mana_cost: 180,
                target_type: "single_enemy",
                base_damage: [92, 108],
                spell_power_coefficient: 0.4,
                can_crit: false,
                effects: [
                    {
                        type: "damage_over_time",
                        value: 92,
                        duration: 12.0,
                        tick_interval: 3.0,
                        chance: 1.0
                    }
                ],
                advanced_effects: [
                    {
                        effect_type: "conditional_effect",
                        name: "Living Bomb Explosion",
                        description: "Explodes when DoT expires",
                        value: 690,
                        conditions: [
                            {
                                condition_type: "spell_effect_expires",
                                operator: "==",
                                value: "damage_over_time"
                            }
                        ],
                        chance: 1.0
                    }
                ]
            },
            chain_lightning: {
                name: "Chain Lightning",
                description: "Lightning that jumps between targets",
                school: "nature",
                cast_time: 2.5,
                cooldown: 0.0,
                mana_cost: 280,
                target_type: "single_enemy",
                base_damage: [380, 420],
                spell_power_coefficient: 0.714,
                can_crit: true,
                advanced_effects: [
                    {
                        effect_type: "chain_effect",
                        name: "Lightning Chain",
                        description: "Jumps to nearby enemies",
                        value: 0.7,
                        max_targets: 3,
                        damage_reduction_per_jump: 0.3,
                        max_range: 8.0,
                        chance: 1.0
                    }
                ]
            },
            arcane_blast: {
                name: "Arcane Blast",
                description: "Stacking spell that increases damage and mana cost",
                school: "arcane",
                cast_time: 2.5,
                cooldown: 0.0,
                mana_cost: 195,
                target_type: "single_enemy",
                base_damage: [372, 428],
                spell_power_coefficient: 0.714,
                can_crit: true,
                advanced_effects: [
                    {
                        effect_type: "stacking_effect",
                        name: "Arcane Blast Stack",
                        description: "Each stack increases damage and mana cost",
                        value: {damage_bonus: 0.75, mana_multiplier: 1.75},
                        duration: 6.0,
                        max_stacks: 4,
                        chance: 1.0
                    }
                ]
            },
            hot_streak_proc: {
                name: "Hot Streak (Proc)",
                description: "Passive proc - fire spell crits enable instant Pyroblast",
                school: "fire",
                cast_time: 0.0,
                cooldown: 0.0,
                mana_cost: 0,
                target_type: "passive",
                base_damage: [0, 0],
                spell_power_coefficient: 0.0,
                can_crit: false,
                is_passive_proc: true,
                advanced_effects: [
                    {
                        effect_type: "proc_effect",
                        name: "Hot Streak Trigger",
                        description: "Grants Hot Streak buff when fire spells crit",
                        value: {buff_name: "Hot Streak", instant_cast_spell: "Pyroblast"},
                        duration: 10.0,
                        conditions: [
                            {
                                condition_type: "spell_critical",
                                operator: "==",
                                value: "fire"
                            }
                        ],
                        chance: 1.0,
                        trigger_spell_schools: ["fire"]
                    }
                ]
            },
            pyroblast: {
                name: "Pyroblast",
                description: "Devastating fire spell - becomes instant with Hot Streak",
                school: "fire",
                cast_time: 6.0,
                cooldown: 0.0,
                mana_cost: 350,
                target_type: "single_enemy",
                base_damage: [664, 874],
                spell_power_coefficient: 1.15,
                can_crit: true,
                advanced_effects: [
                    {
                        effect_type: "conditional_effect",
                        name: "Hot Streak Instant Cast",
                        description: "Becomes instant cast if Hot Streak buff is active",
                        value: {cast_time_override: 0.0},
                        conditions: [
                            {
                                condition_type: "buff_present",
                                operator: "==",
                                value: "Hot Streak"
                            }
                        ],
                        chance: 1.0,
                        consumes_buff: "Hot Streak"
                    },
                    {
                        effect_type: "damage_over_time",
                        name: "Pyroblast DoT",
                        description: "Burns target over time",
                        value: 56,
                        duration: 12.0,
                        tick_interval: 3.0,
                        chance: 1.0,
                        spell_power_coefficient: 0.05
                    }
                ]
            },
            clearcasting: {
                name: "Clearcasting",
                description: "Arcane mastery - spell casts have chance to make next spell free",
                school: "arcane",
                cast_time: 0.0,
                cooldown: 0.0,
                mana_cost: 0,
                target_type: "passive",
                base_damage: [0, 0],
                spell_power_coefficient: 0.0,
                can_crit: false,
                is_passive_proc: true,
                advanced_effects: [
                    {
                        effect_type: "proc_effect",
                        name: "Clearcasting Trigger",
                        description: "Grants free spell cast",
                        value: {buff_name: "Clearcasting", mana_cost_modifier: 0.0},
                        duration: 15.0,
                        conditions: [
                            {
                                condition_type: "proc_chance",
                                operator: "<=",
                                value: "10"
                            }
                        ],
                        chance: 0.1,
                        internal_cooldown: 0.0
                    }
                ]
            },
            impact: {
                name: "Impact",
                description: "Fire mastery - Fire Blast spreads DoTs when Hot Streak procs",
                school: "fire",
                cast_time: 0.0,
                cooldown: 0.0,
                mana_cost: 0,
                target_type: "passive",
                base_damage: [0, 0],
                spell_power_coefficient: 0.0,
                can_crit: false,
                is_passive_proc: true,
                advanced_effects: [
                    {
                        effect_type: "proc_effect",
                        name: "Impact Spread",
                        description: "Spreads DoTs to nearby enemies",
                        value: {spread_radius: 8.0, max_targets: 3},
                        conditions: [
                            {
                                condition_type: "proc_triggered",
                                operator: "==",
                                value: "Hot Streak"
                            },
                            {
                                condition_type: "spell_school",
                                operator: "==",
                                value: "fire"
                            }
                        ],
                        chance: 1.0,
                        logic_operator: "AND"
                    }
                ]
            },
            ignite: {
                name: "Ignite",
                description: "Fire spell mastery - critical strikes cause burning DoT",
                school: "fire",
                cast_time: 0.0,
                cooldown: 0.0,
                mana_cost: 0,
                target_type: "enemy",
                base_damage: [0, 0],
                spell_power_coefficient: 0.0,
                can_crit: false,
                is_passive_proc: true,
                advanced_effects: [
                    {
                        effect_type: "proc_effect",
                        name: "Ignite DoT",
                        description: "Burns target when fire spells crit",
                        value: 0.4,
                        duration: 4.0,
                        tick_interval: 2.0,
                        conditions: [
                            {
                                condition_type: "spell_critical",
                                operator: "==",
                                value: "fire"
                            }
                        ],
                        chance: 1.0,
                        max_stacks: 5
                    }
                ]
            }
            };
            template = advancedTemplates[templateName];
        }

        if (!template) {
            showNotification(`Template "${templateName}" not found`, 'error');
            return;
        }

        // Load basic properties with error checking
        const setElementValue = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            } else {
                console.warn(`Element with id '${id}' not found`);
            }
        };

        setElementValue('spell-name', template.name);
        setElementValue('spell-description', template.description);
        setElementValue('spell-school', template.school);
        setElementValue('target-type', template.target_type);
        setElementValue('cast-time', template.cast_time);
        setElementValue('cooldown', template.cooldown);
        setElementValue('mana-cost', template.mana_cost);

        if (Array.isArray(template.base_damage)) {
            setElementValue('base-damage-min', template.base_damage[0]);
            setElementValue('base-damage-max', template.base_damage[1]);
        }

        setElementValue('spell-power-coeff', template.spell_power_coefficient);

        // Enable advanced mode if not already active
        if (!advancedModeActive) {
            toggleAdvancedMode();
        }

        // Clear existing effects and load template effects
        const effectsContainer = document.getElementById('spell-effects-container');
        if (effectsContainer) {
            effectsContainer.innerHTML = '';

            if (template.effects) {
                template.effects.forEach(effect => {
                    try {
                        addEffect();
                        const lastEffect = effectsContainer.lastElementChild;
                        if (lastEffect) {
                            const setEffectValue = (selector, value) => {
                                const element = lastEffect.querySelector(selector);
                                if (element) element.value = value;
                            };

                            setEffectValue('.effect-type', effect.type);
                            setEffectValue('.effect-value', effect.value);
                            setEffectValue('.effect-duration', effect.duration);
                            setEffectValue('.effect-chance', (effect.chance * 100));

                            if (effect.tick_interval) {
                                setEffectValue('.effect-tick-interval', effect.tick_interval);
                            }

                            const effectTypeElement = lastEffect.querySelector('.effect-type');
                            if (effectTypeElement) {
                                updateEffectProperties(effectTypeElement);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading effect:', error);
                    }
                });
            }
        } else {
            console.warn('Effects container not found');
        }

        showNotification(`Loaded advanced template: ${template.name}`, 'success');

    } catch (error) {
        console.error('Error loading advanced template:', error);
        showNotification('Failed to load advanced template', 'error');
    } finally {
        showLoading(false);
    }
}
