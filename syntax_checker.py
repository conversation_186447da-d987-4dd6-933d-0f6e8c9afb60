"""
Syntax checker - compile all Python files to find syntax errors.
"""

import os
import py_compile
import sys

def check_file_syntax(filepath):
    """Check syntax of a single Python file."""
    try:
        py_compile.compile(filepath, doraise=True)
        return True, None
    except py_compile.PyCompileError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def check_directory_syntax(directory):
    """Check syntax of all Python files in a directory."""
    errors = []
    success_count = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                success, error = check_file_syntax(filepath)
                
                if success:
                    success_count += 1
                    print(f"✓ {filepath}")
                else:
                    errors.append((filepath, error))
                    print(f"✗ {filepath}: {error}")
    
    return success_count, errors

def main():
    """Check syntax of all files in the project."""
    print("🔍 Syntax Checker - Compiling all Python files")
    print("=" * 60)
    
    # Check main files
    main_files = [
        "spell_creation_demo.py",
        "advanced_effects_demo.py", 
        "rotation_optimizer_demo.py",
        "cooldown_gcd_demo.py",
        "error_finder.py",
        "minimal_test.py"
    ]
    
    print("\n--- Checking main demo files ---")
    main_success = 0
    main_errors = []
    
    for file in main_files:
        if os.path.exists(file):
            success, error = check_file_syntax(file)
            if success:
                main_success += 1
                print(f"✓ {file}")
            else:
                main_errors.append((file, error))
                print(f"✗ {file}: {error}")
        else:
            print(f"? {file}: File not found")
    
    # Check src directory
    print("\n--- Checking src directory ---")
    src_success, src_errors = check_directory_syntax("src")
    
    # Summary
    total_success = main_success + src_success
    total_errors = len(main_errors) + len(src_errors)
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {total_success} files compiled successfully")
    print(f"         {total_errors} files have syntax errors")
    
    if total_errors > 0:
        print("\nERRORS FOUND:")
        for filepath, error in main_errors + src_errors:
            print(f"  {filepath}: {error}")
    else:
        print("\n🎉 All files have valid syntax!")
    
    return total_errors == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
