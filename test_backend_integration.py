#!/usr/bin/env python3
"""
Test script for the enhanced WoW Simulator Backend Integration
"""

import requests
import json
import time

# Test configuration
BASE_URL = 'http://localhost:5000/api'
session = requests.Session()

def test_session_management():
    """Test session creation and management."""
    print("🧪 Testing session management...")
    
    try:
        # Test session creation
        response = session.get(f'{BASE_URL}/session')
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ Session created: {result['session_id']}")
                return result['session_id']
            else:
                print(f"❌ Session creation failed: {result['error']}")
                return None
        else:
            print(f"❌ Session endpoint failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Session test failed: {e}")
        return None

def test_character_management(session_id):
    """Test character creation and retrieval."""
    print("\n🧪 Testing character management...")
    
    try:
        # Create a test character
        character_data = {
            "name": "Test Mage",
            "class": "mage",
            "health": 1000,
            "mana": 1000,
            "spell_power": 150,
            "crit_chance": 0.05,
            "haste": 0.0,
            "hit_chance": 0.83
        }
        
        response = session.post(f'{BASE_URL}/character', json=character_data)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ Character created: {result['character']['name']}")
                
                # Test character retrieval
                char_response = session.get(f'{BASE_URL}/character/{result["character_id"]}')
                if char_response.status_code == 200:
                    char_result = char_response.json()
                    if char_result['success']:
                        print(f"✅ Character retrieved: {char_result['character']['name']}")
                        return result['character_id']
                    else:
                        print(f"❌ Character retrieval failed: {char_result['error']}")
                else:
                    print(f"❌ Character retrieval failed: {char_response.status_code}")
            else:
                print(f"❌ Character creation failed: {result['error']}")
        else:
            print(f"❌ Character creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Character test failed: {e}")
    
    return None

def test_spell_management(session_id):
    """Test spell creation, retrieval, and deletion."""
    print("\n🧪 Testing spell management...")
    
    try:
        # Create a test spell
        spell_data = {
            "name": "Test Fireball",
            "description": "A basic fireball spell for testing",
            "school": "fire",
            "target_type": "single_enemy",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 200,
            "base_damage": [200, 300],
            "spell_power_coefficient": 1.0,
            "can_crit": True
        }
        
        response = session.post(f'{BASE_URL}/spell', json=spell_data)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                spell_id = result['spell_id']
                print(f"✅ Spell created: {result['spell']['name']} (ID: {spell_id})")
                
                # Test spell retrieval
                spells_response = session.get(f'{BASE_URL}/spells')
                if spells_response.status_code == 200:
                    spells_result = spells_response.json()
                    if spells_result['success'] and len(spells_result['spells']) > 0:
                        print(f"✅ Spells retrieved: {len(spells_result['spells'])} spells")
                        
                        # Test spell deletion
                        delete_response = session.delete(f'{BASE_URL}/spell/{spell_id}')
                        if delete_response.status_code == 200:
                            delete_result = delete_response.json()
                            if delete_result['success']:
                                print(f"✅ Spell deleted successfully")
                                return True
                            else:
                                print(f"❌ Spell deletion failed: {delete_result['error']}")
                        else:
                            print(f"❌ Spell deletion failed: {delete_response.status_code}")
                    else:
                        print(f"❌ Spell retrieval failed: {spells_result.get('error', 'No spells found')}")
                else:
                    print(f"❌ Spell retrieval failed: {spells_response.status_code}")
            else:
                print(f"❌ Spell creation failed: {result['error']}")
        else:
            print(f"❌ Spell creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Spell test failed: {e}")
    
    return False

def test_health_endpoint():
    """Test the health check endpoint."""
    print("\n🧪 Testing health endpoint...")
    
    try:
        response = session.get(f'{BASE_URL}/health')
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'healthy':
                print(f"✅ Health check passed: {result['message']}")
                print(f"   Active sessions: {result.get('active_sessions', 0)}")
                return True
            else:
                print(f"❌ Health check failed: {result}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health test failed: {e}")
    
    return False

def main():
    """Run all backend integration tests."""
    print("🧙‍♂️ WoW Simulator Backend Integration Test")
    print("=" * 50)
    
    # Test health endpoint first
    if not test_health_endpoint():
        print("\n❌ Backend is not running or not responding.")
        print("   Please start the backend with: python web_backend.py")
        return 1
    
    # Test session management
    session_id = test_session_management()
    if not session_id:
        print("\n❌ Session management failed.")
        return 1
    
    # Test character management
    character_id = test_character_management(session_id)
    if not character_id:
        print("\n❌ Character management failed.")
        return 1
    
    # Test spell management
    if not test_spell_management(session_id):
        print("\n❌ Spell management failed.")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 All backend integration tests passed!")
    print("\n💡 The enhanced backend is working correctly:")
    print("   ✅ Session management")
    print("   ✅ Character persistence")
    print("   ✅ Spell CRUD operations")
    print("   ✅ Data isolation per session")
    print("\n🚀 Frontend should now work with full backend integration!")
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
