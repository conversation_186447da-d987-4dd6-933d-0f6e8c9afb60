<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conditions System Test</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: var(--darker-bg);
            border-radius: 8px;
            border-left: 4px solid var(--primary-gold);
        }
        
        .test-result {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 6px;
            font-weight: 600;
        }
        
        .test-result.success {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .test-result.error {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
        }
        
        .condition-example {
            background: var(--card-bg);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            font-family: 'Courier New', monospace;
        }

        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-overlay.active {
            display: flex;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f4d03f;
            border-top: 4px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notifications styles */
        .notifications {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }

        .notification {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 10px;
            padding: 1rem;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.warning {
            border-color: var(--warning-color);
            background: rgba(244, 208, 63, 0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Conditions System Test</h1>
        <p>Testing the comprehensive conditions system with all 100+ condition types.</p>
        
        <div class="test-section">
            <h3>📊 Condition Categories Test</h3>
            <div id="categories-test"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Logic Preview Test</h3>
            <div id="logic-test"></div>
        </div>
        
        <div class="test-section">
            <h3>✅ Validation Test</h3>
            <div id="validation-test"></div>
        </div>
        
        <div class="test-section">
            <h3>🎮 Real Examples Test</h3>
            <div id="examples-test"></div>
        </div>
        
        <button onclick="runAllTests()" class="btn btn-primary">
            <i class="fas fa-play"></i> Run All Tests
        </button>
        
        <div id="test-results"></div>
    </div>

    <!-- Loading overlay for app.js compatibility -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Notifications container for app.js compatibility -->
    <div id="notifications" class="notifications"></div>

    <script src="static/js/app.js"></script>
    <script>
        function runAllTests() {
            console.log('🧪 Running Conditions System Tests...');
            
            const results = [];
            
            // Test 1: Condition Categories
            results.push(testConditionCategories());
            
            // Test 2: Logic Preview
            results.push(testLogicPreview());
            
            // Test 3: Validation
            results.push(testValidation());
            
            // Test 4: Real Examples
            results.push(testRealExamples());
            
            displayTestResults(results);
        }
        
        function testConditionCategories() {
            try {
                const categories = [
                    'spell_critical', 'spell_hit', 'spell_school',
                    'target_health_below', 'target_has_buff', 'target_distance',
                    'caster_mana_below', 'caster_stealthed', 'caster_class',
                    'proc_triggered', 'proc_chance', 'consecutive_procs',
                    'combat_time', 'enemies_nearby', 'threat_level',
                    'time_of_day', 'weather', 'pvp_zone',
                    'damage_taken_recently', 'spell_reflect_active', 'line_of_sight'
                ];
                
                let validConfigs = 0;
                categories.forEach(category => {
                    const config = getConditionConfig(category);
                    if (config && config.description) {
                        validConfigs++;
                    }
                });
                
                const success = validConfigs === categories.length;
                
                document.getElementById('categories-test').innerHTML = `
                    <div class="test-result ${success ? 'success' : 'error'}">
                        ${success ? '✅' : '❌'} Condition Categories: ${validConfigs}/${categories.length} valid
                    </div>
                    <div class="condition-example">
                        Example: ${JSON.stringify(getConditionConfig('spell_critical'), null, 2)}
                    </div>
                `;
                
                return { name: 'Condition Categories', success, details: `${validConfigs}/${categories.length} valid` };
                
            } catch (error) {
                return { name: 'Condition Categories', success: false, error: error.message };
            }
        }
        
        function testLogicPreview() {
            try {
                // Mock conditions
                builderConditions = [
                    { type: 'spell_critical', operator: '==', value: 'fire' },
                    { type: 'target_health_below', operator: '<=', value: '35' }
                ];
                
                // Test logic preview generation
                updateLogicPreview();
                
                const preview = document.getElementById('logic-preview');
                const hasPreview = preview && preview.innerHTML.includes('IF');
                
                document.getElementById('logic-test').innerHTML = `
                    <div class="test-result ${hasPreview ? 'success' : 'error'}">
                        ${hasPreview ? '✅' : '❌'} Logic Preview Generation
                    </div>
                    <div class="condition-example">
                        Generated: ${preview ? preview.innerHTML : 'No preview'}
                    </div>
                `;
                
                return { name: 'Logic Preview', success: hasPreview };
                
            } catch (error) {
                return { name: 'Logic Preview', success: false, error: error.message };
            }
        }
        
        function testValidation() {
            try {
                // Test validation functions
                const mockItem = document.createElement('div');
                mockItem.className = 'condition-item';
                
                const validResult = validateCondition(mockItem, 'target_health_below', '35');
                const invalidResult = validateCondition(mockItem, 'target_health_below', '');
                
                const success = validResult === true && invalidResult === false;
                
                document.getElementById('validation-test').innerHTML = `
                    <div class="test-result ${success ? 'success' : 'error'}">
                        ${success ? '✅' : '❌'} Condition Validation
                    </div>
                    <div class="condition-example">
                        Valid input: ${validResult}<br>
                        Invalid input: ${invalidResult}
                    </div>
                `;
                
                return { name: 'Validation', success };
                
            } catch (error) {
                return { name: 'Validation', success: false, error: error.message };
            }
        }
        
        function testRealExamples() {
            try {
                const examples = [
                    {
                        name: 'Fire Mage Combustion',
                        conditions: [
                            { type: 'target_has_debuff', operator: '==', value: 'Ignite' },
                            { type: 'spell_school', operator: '==', value: 'fire' },
                            { type: 'caster_has_buff', operator: '==', value: 'Combustion' }
                        ]
                    },
                    {
                        name: 'Rogue Cheap Shot',
                        conditions: [
                            { type: 'caster_stealthed', operator: '==', value: 'true' },
                            { type: 'target_type', operator: '==', value: 'humanoid' },
                            { type: 'target_distance', operator: '<=', value: '5' }
                        ]
                    },
                    {
                        name: 'Night Elf Shadowmeld',
                        conditions: [
                            { type: 'time_of_day', operator: '==', value: 'night' },
                            { type: 'caster_race', operator: '==', value: 'night elf' },
                            { type: 'caster_stealthed', operator: '==', value: 'true' }
                        ]
                    }
                ];
                
                let validExamples = 0;
                let exampleHTML = '';
                
                examples.forEach(example => {
                    const allValid = example.conditions.every(condition => {
                        const config = getConditionConfig(condition.type);
                        return config && config.description;
                    });
                    
                    if (allValid) validExamples++;
                    
                    exampleHTML += `
                        <div class="condition-example">
                            <strong>${example.name}</strong> ${allValid ? '✅' : '❌'}<br>
                            ${example.conditions.map(c => `${c.type} ${c.operator} ${c.value}`).join(' AND ')}
                        </div>
                    `;
                });
                
                const success = validExamples === examples.length;
                
                document.getElementById('examples-test').innerHTML = `
                    <div class="test-result ${success ? 'success' : 'error'}">
                        ${success ? '✅' : '❌'} Real Examples: ${validExamples}/${examples.length} valid
                    </div>
                    ${exampleHTML}
                `;
                
                return { name: 'Real Examples', success, details: `${validExamples}/${examples.length} valid` };
                
            } catch (error) {
                return { name: 'Real Examples', success: false, error: error.message };
            }
        }
        
        function displayTestResults(results) {
            const passed = results.filter(r => r.success).length;
            const total = results.length;
            const allPassed = passed === total;
            
            const resultsHTML = `
                <div class="test-section">
                    <h3>📊 Test Results</h3>
                    <div class="test-result ${allPassed ? 'success' : 'error'}">
                        ${allPassed ? '🎉' : '⚠️'} ${passed}/${total} tests passed
                    </div>
                    ${results.map(result => `
                        <div class="test-result ${result.success ? 'success' : 'error'}">
                            ${result.success ? '✅' : '❌'} ${result.name}
                            ${result.details ? ` - ${result.details}` : ''}
                            ${result.error ? ` - Error: ${result.error}` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
            
            document.getElementById('test-results').innerHTML = resultsHTML;
            
            console.log(`🧪 Tests completed: ${passed}/${total} passed`);
        }
        
        // Initialize test environment
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize builder state for testing
            currentBuilderStep = 2;
            selectedEffects = [];
            builderConditions = [];
            
            console.log('🧪 Conditions System Test Environment Ready');
        });
    </script>
</body>
</html>
