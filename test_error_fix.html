<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Fix Test</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        
        .test-result {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 6px;
            font-weight: 600;
        }
        
        .test-result.success {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .test-result.error {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
        }
        
        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-overlay.active {
            display: flex;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f4d03f;
            border-top: 4px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Error Fix Test</h1>
        <p>Testing the JavaScript error fixes for null element access.</p>
        
        <div id="test-results"></div>
        
        <button onclick="runErrorTests()" class="btn btn-primary">
            <i class="fas fa-play"></i> Run Error Tests
        </button>
    </div>

    <!-- Loading overlay for app.js compatibility -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <script src="static/js/app.js"></script>
    <script>
        function runErrorTests() {
            const results = [];
            
            // Test 1: showLoading function with existing element
            try {
                showLoading(true);
                showLoading(false);
                results.push({
                    name: 'showLoading with existing element',
                    success: true,
                    message: 'Function executed without errors'
                });
            } catch (error) {
                results.push({
                    name: 'showLoading with existing element',
                    success: false,
                    message: error.message
                });
            }
            
            // Test 2: showLoading function with missing element (simulate)
            try {
                const originalElement = document.getElementById('loading-overlay');
                originalElement.id = 'temp-hidden';
                
                showLoading(true); // Should not throw error
                
                originalElement.id = 'loading-overlay'; // Restore
                
                results.push({
                    name: 'showLoading with missing element',
                    success: true,
                    message: 'Function handled missing element gracefully'
                });
            } catch (error) {
                results.push({
                    name: 'showLoading with missing element',
                    success: false,
                    message: error.message
                });
            }
            
            // Test 3: switchSection function
            try {
                // This will fail gracefully since sections don't exist in this test
                switchSection('nonexistent');
                results.push({
                    name: 'switchSection with missing section',
                    success: true,
                    message: 'Function handled missing section gracefully'
                });
            } catch (error) {
                results.push({
                    name: 'switchSection with missing section',
                    success: false,
                    message: error.message
                });
            }
            
            // Test 4: Check console for errors
            const hasConsoleErrors = window.console && window.console.error;
            results.push({
                name: 'Console error handling',
                success: hasConsoleErrors,
                message: hasConsoleErrors ? 'Console error handling available' : 'Console error handling not available'
            });
            
            displayTestResults(results);
        }
        
        function displayTestResults(results) {
            const container = document.getElementById('test-results');
            const passed = results.filter(r => r.success).length;
            const total = results.length;
            
            let html = `
                <h3>📊 Error Fix Test Results</h3>
                <p><strong>Passed:</strong> ${passed}/${total} tests</p>
            `;
            
            results.forEach(result => {
                html += `
                    <div class="test-result ${result.success ? 'success' : 'error'}">
                        ${result.success ? '✅' : '❌'} <strong>${result.name}</strong><br>
                        ${result.message}
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            if (passed === total) {
                console.log('🎉 All error fix tests passed!');
            } else {
                console.log(`⚠️ ${total - passed} tests failed`);
            }
        }
        
        // Test initialization
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Error Fix Test Environment Ready');
            
            // Run a quick initialization test
            setTimeout(() => {
                const initTest = document.getElementById('loading-overlay');
                if (initTest) {
                    console.log('✅ Loading overlay element found');
                } else {
                    console.log('❌ Loading overlay element missing');
                }
            }, 100);
        });
    </script>
</body>
</html>
