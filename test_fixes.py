#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes are working correctly.
"""

import os
import re
import json
from pathlib import Path

def test_javascript_syntax():
    """Test JavaScript for syntax issues."""
    print("🧪 Testing JavaScript syntax...")
    
    js_file = Path("static/js/app.js")
    if not js_file.exists():
        print("❌ app.js not found")
        return False
    
    content = js_file.read_text(encoding='utf-8')
    
    # Check for balanced braces
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        print(f"❌ Unbalanced braces: {open_braces} open, {close_braces} close")
        return False
    
    # Check for duplicate function definitions
    function_pattern = r'function\s+(\w+)\s*\('
    functions = re.findall(function_pattern, content)
    duplicates = []
    seen = set()
    
    for func in functions:
        if func in seen:
            duplicates.append(func)
        seen.add(func)
    
    if duplicates:
        print(f"❌ Duplicate functions found: {duplicates}")
        return False
    
    print("✅ JavaScript syntax looks good")
    return True

def test_html_structure():
    """Test HTML for structural issues."""
    print("🧪 Testing HTML structure...")
    
    html_file = Path("index.html")
    if not html_file.exists():
        print("❌ index.html not found")
        return False
    
    content = html_file.read_text(encoding='utf-8')
    
    # Check for required elements
    required_elements = [
        'id="advanced-spell-config"',
        'id="builder-step-1"',
        'id="builder-step-2"',
        'id="builder-step-3"',
        'id="effects-list"',
        'id="conditions-list"',
        'id="logic-preview"',
        'class="builder-progress"'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in content:
            missing_elements.append(element)
    
    if missing_elements:
        print(f"❌ Missing HTML elements: {missing_elements}")
        return False
    
    print("✅ HTML structure looks good")
    return True

def test_css_completeness():
    """Test CSS for required styles."""
    print("🧪 Testing CSS completeness...")
    
    css_file = Path("static/css/style.css")
    if not css_file.exists():
        print("❌ style.css not found")
        return False
    
    content = css_file.read_text(encoding='utf-8')
    
    # Check for required CSS classes
    required_classes = [
        '.builder-step',
        '.progress-step',
        '.effect-btn',
        '.condition-item',
        '.logic-preview',
        '.effect-categories',
        '.builder-navigation'
    ]
    
    missing_classes = []
    for css_class in required_classes:
        if css_class not in content:
            missing_classes.append(css_class)
    
    if missing_classes:
        print(f"❌ Missing CSS classes: {missing_classes}")
        return False
    
    print("✅ CSS completeness looks good")
    return True

def test_backend_imports():
    """Test backend imports."""
    print("🧪 Testing backend imports...")
    
    try:
        # Test basic imports
        from web_backend import app
        print("✅ Flask app imports successfully")
        
        # Test advanced effects
        from src.spells.advanced_effects import AdvancedEffectProcessor
        print("✅ Advanced effects import successfully")
        
        # Test spell builder
        from src.spells.spell_builder import SpellBuilder
        print("✅ Spell builder imports successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend import error: {e}")
        return False

def test_template_consistency():
    """Test that templates are consistent between backend and frontend."""
    print("🧪 Testing template consistency...")
    
    try:
        # Check if backend templates exist
        from web_backend import get_advanced_spell_templates
        backend_templates = get_advanced_spell_templates()
        
        # Check JavaScript file for template references
        js_file = Path("static/js/app.js")
        js_content = js_file.read_text(encoding='utf-8')
        
        # Look for template names in JavaScript
        template_names = [
            'living_bomb', 'chain_lightning', 'hot_streak_proc', 
            'pyroblast', 'arcane_blast', 'ignite', 'clearcasting', 'impact'
        ]
        
        missing_in_js = []
        for template in template_names:
            if template not in js_content:
                missing_in_js.append(template)
        
        if missing_in_js:
            print(f"⚠️  Templates missing in JavaScript: {missing_in_js}")
        
        print("✅ Template consistency looks good")
        return True
        
    except Exception as e:
        print(f"❌ Template consistency error: {e}")
        return False

def test_function_references():
    """Test that all function references are valid."""
    print("🧪 Testing function references...")
    
    js_file = Path("static/js/app.js")
    js_content = js_file.read_text(encoding='utf-8')
    
    # Find all function calls in onclick attributes and JavaScript
    onclick_pattern = r'onclick="([^"]*)"'
    onclick_calls = re.findall(onclick_pattern, js_content)
    
    # Find all function definitions
    function_pattern = r'function\s+(\w+)\s*\('
    defined_functions = set(re.findall(function_pattern, js_content))
    
    # Check if all onclick functions are defined
    missing_functions = []
    for call in onclick_calls:
        # Extract function name from call
        func_match = re.match(r'(\w+)\s*\(', call)
        if func_match:
            func_name = func_match.group(1)
            if func_name not in defined_functions:
                missing_functions.append(func_name)
    
    if missing_functions:
        print(f"❌ Missing function definitions: {set(missing_functions)}")
        return False
    
    print("✅ Function references look good")
    return True

def main():
    """Run all tests."""
    print("🔧 WoW Simulator - Code Fix Verification")
    print("=" * 50)
    
    tests = [
        test_javascript_syntax,
        test_html_structure,
        test_css_completeness,
        test_backend_imports,
        test_template_consistency,
        test_function_references
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The code fixes are working correctly.")
        print("\n✨ The WoW Simulator should now work without issues:")
        print("   ✅ No duplicate functions")
        print("   ✅ All HTML elements present")
        print("   ✅ CSS styles complete")
        print("   ✅ Backend imports working")
        print("   ✅ Templates consistent")
        print("   ✅ Function references valid")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
