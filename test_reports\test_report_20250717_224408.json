{"timestamp": "2025-07-17T22:44:08.326924", "summary": {"total_tests": 0, "passed": 0, "failed": 0, "errors": 0, "success_rate": 0}, "suites": {"backend": {"success": false, "tests_run": 0, "failures": 0, "errors": 0, "output": "Warning: Could not import some modules: No module named 'spell_validator'\nSome tests may be skipped.\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Ny mapp\\WoW Simulator\\tests\\test_backend.py\", line 388, in <module>\n    run_test_suite()\n    ~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\Ny mapp\\WoW Simulator\\tests\\test_backend.py\", line 341, in run_test_suite\n    print(\"\\U0001f9ea Starting WoW Simulator Backend Test Suite...\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f9ea' in position 0: character maps to <undefined>\n", "duration": 0.15754914283752441}}}