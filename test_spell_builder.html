<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spell Builder Test</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            padding: 2rem;
            background: var(--bg-color);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .test-result {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 6px;
            font-weight: 600;
        }
        
        .test-result.success {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .test-result.error {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Spell Builder Test</h1>
        <p>Testing the spell builder components to identify issues.</p>
        
        <div class="test-section">
            <h2>Step 1: Spell Type Selection</h2>
            <div class="quick-spell-types">
                <div class="spell-type-card" onclick="testSelectSpellType('damage')">
                    <div class="spell-type-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <h5>Deal Damage</h5>
                    <p>Hurt your enemies</p>
                    <div class="spell-examples">
                        <span>Fireball</span> • <span>Lightning Bolt</span>
                    </div>
                </div>
            </div>
            
            <div id="test-spell-type-details" class="spell-type-details" style="display: none;">
                <h5>Great choice! Now pick the specific effect:</h5>
                <div id="test-spell-effect-options" class="spell-effect-options">
                    <!-- Dynamic content -->
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Step 2: Simple Condition Selection</h2>
            <div class="super-simple-conditions">
                <div class="condition-simple-card recommended" onclick="testSelectSimpleCondition('normal')">
                    <div class="condition-badge">👍 RECOMMENDED</div>
                    <div class="condition-simple-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5>Normal Spell</h5>
                    <p><strong>Works every time you cast it</strong></p>
                    <div class="condition-simple-examples">
                        Like: Fireball, Heal, Lightning Bolt
                    </div>
                </div>
                
                <div class="condition-simple-card" onclick="testSelectSimpleCondition('finisher')">
                    <div class="condition-simple-icon">
                        <i class="fas fa-skull"></i>
                    </div>
                    <h5>Finisher Move</h5>
                    <p>Only works on low-health enemies</p>
                    <div class="condition-simple-examples">
                        Like: Execute, Killing Blow
                    </div>
                </div>
            </div>
            
            <div id="test-simple-condition-config" class="simple-condition-config" style="display: none;">
                <div class="config-card">
                    <h5 id="test-config-title">Configure your spell:</h5>
                    <div id="test-config-content">
                        <!-- Dynamic content -->
                    </div>
                    <div class="config-actions">
                        <button class="btn btn-primary" onclick="testNextStep()">
                            <i class="fas fa-arrow-right"></i> Next: Set Power Level
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="test-results">
                <p>Click the cards above to test functionality...</p>
            </div>
        </div>
    </div>

    <!-- Required elements for app.js compatibility -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>
    <div id="notifications" class="notifications"></div>

    <script src="static/js/app.js"></script>
    <script>
        // Test functions
        function testSelectSpellType(type) {
            addTestResult('Spell Type Selection', `Selected: ${type}`, 'success');
            
            // Show effect options
            const detailsContainer = document.getElementById('test-spell-type-details');
            const optionsContainer = document.getElementById('test-spell-effect-options');
            
            if (detailsContainer && optionsContainer) {
                detailsContainer.style.display = 'block';
                optionsContainer.innerHTML = `
                    <div class="effect-option-btn" onclick="testSelectEffect('instant_damage')">
                        <i class="fas fa-bolt"></i>
                        <div class="effect-name">Instant Hit</div>
                        <div class="effect-desc">Deal damage right away</div>
                    </div>
                `;
                addTestResult('Effect Options', 'Effect options displayed successfully', 'success');
            } else {
                addTestResult('Effect Options', 'Failed to find effect containers', 'error');
            }
        }
        
        function testSelectEffect(effectType) {
            addTestResult('Effect Selection', `Selected: ${effectType}`, 'success');
        }
        
        function testSelectSimpleCondition(conditionType) {
            addTestResult('Condition Selection', `Selected: ${conditionType}`, 'success');
            
            // Update UI
            document.querySelectorAll('.condition-simple-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.condition-simple-card').classList.add('selected');
            
            // Show configuration
            const configContainer = document.getElementById('test-simple-condition-config');
            const titleElement = document.getElementById('test-config-title');
            const contentElement = document.getElementById('test-config-content');
            
            if (configContainer && titleElement && contentElement) {
                configContainer.style.display = 'block';
                
                if (conditionType === 'normal') {
                    titleElement.textContent = "Perfect! Your spell will work every time you cast it.";
                    contentElement.innerHTML = `
                        <div class="config-simple">
                            <p><i class="fas fa-check-circle" style="color: var(--success-color); font-size: 2rem;"></i></p>
                            <p style="font-size: 1.1rem; margin: 1rem 0;">
                                <strong>This is the most common type of spell.</strong><br>
                                It will work every single time you cast it.
                            </p>
                        </div>
                    `;
                } else if (conditionType === 'finisher') {
                    titleElement.textContent = "Configure your finisher move:";
                    contentElement.innerHTML = `
                        <div class="config-slider">
                            <label>Only works when target health is below:</label>
                            <input type="range" min="10" max="35" value="20" 
                                   oninput="testUpdateHealth(this.value)">
                            <div class="slider-value" id="test-health-threshold">20%</div>
                        </div>
                    `;
                }
                
                addTestResult('Condition Config', 'Configuration displayed successfully', 'success');
            } else {
                addTestResult('Condition Config', 'Failed to find config containers', 'error');
            }
        }
        
        function testUpdateHealth(value) {
            const element = document.getElementById('test-health-threshold');
            if (element) {
                element.textContent = value + '%';
                addTestResult('Slider Update', `Health threshold: ${value}%`, 'success');
            }
        }
        
        function testNextStep() {
            addTestResult('Navigation', 'Next step button clicked', 'success');
        }
        
        function addTestResult(category, message, type) {
            const resultsContainer = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `
                ${type === 'success' ? '✅' : '❌'} <strong>${category}:</strong> ${message}
            `;
            resultsContainer.appendChild(result);
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', () => {
            addTestResult('Initialization', 'Test page loaded successfully', 'success');
            
            // Test if CSS classes exist
            const testCard = document.querySelector('.condition-simple-card');
            if (testCard) {
                addTestResult('CSS Styles', 'Condition card styles loaded', 'success');
            } else {
                addTestResult('CSS Styles', 'Condition card styles missing', 'error');
            }
            
            // Test if JavaScript functions exist
            if (typeof selectSpellType === 'function') {
                addTestResult('JavaScript Functions', 'Main app.js functions loaded', 'success');
            } else {
                addTestResult('JavaScript Functions', 'Main app.js functions missing', 'error');
            }
        });
    </script>
</body>
</html>
