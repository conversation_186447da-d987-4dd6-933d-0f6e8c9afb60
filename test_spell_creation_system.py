"""
Comprehensive test of the spell creation system.
Verifies all components work together correctly.
"""

import json
import os
from src.spells.spell_builder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SpellTemplateLibrary
from src.spells.spell_validator import SpellValidator
from src.spells.spell_schema import create_spell_config_from_dict
from src.character import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.stats.standalone_stats import StatType


def test_spell_templates():
    """Test that all spell templates are valid."""
    print("Testing spell templates...")
    
    templates = SpellTemplateLibrary.get_all_templates()
    validator = SpellValidator()
    builder = SpellBuilder()
    
    passed = 0
    total = len(templates)
    
    for template_name, template_data in templates.items():
        try:
            # Validate template
            is_valid, errors, config = validator.validate_spell_from_dict(template_data)
            
            if is_valid:
                # Try to build spell
                spell = builder.build_spell_from_dict(template_data)
                print(f"  ✓ {template_name}: {spell.name}")
                passed += 1
            else:
                print(f"  ✗ {template_name}: Validation failed - {errors[0] if errors else 'Unknown'}")
        
        except Exception as e:
            print(f"  ✗ {template_name}: Exception - {e}")
    
    print(f"Templates: {passed}/{total} passed")
    return passed == total


def test_spell_validation():
    """Test spell validation system."""
    print("\nTesting spell validation...")
    
    validator = SpellValidator()
    
    # Test valid spell
    valid_spell = {
        "name": "Test Spell",
        "description": "A test spell",
        "school": "fire",
        "cast_time": 2.0,
        "cooldown": 0.0,
        "mana_cost": 200,
        "target_type": "single_enemy",
        "base_damage": 400,
        "spell_power_coefficient": 1.0,
        "can_crit": True
    }
    
    is_valid, errors, config = validator.validate_spell_from_dict(valid_spell)
    if is_valid:
        print("  ✓ Valid spell validation passed")
    else:
        print(f"  ✗ Valid spell failed: {errors}")
        return False
    
    # Test invalid spell (overpowered)
    invalid_spell = valid_spell.copy()
    invalid_spell.update({
        "base_damage": 5000,  # Way too high
        "mana_cost": 1,       # Way too low
        "cast_time": 0.0      # Instant + high damage
    })
    
    is_valid, errors, config = validator.validate_spell_from_dict(invalid_spell)
    if not is_valid and len(errors) > 0:
        print("  ✓ Invalid spell correctly rejected")
    else:
        print("  ✗ Invalid spell was accepted")
        return False
    
    print("Validation: All tests passed")
    return True


def test_spell_building():
    """Test spell building from configurations."""
    print("\nTesting spell building...")
    
    builder = SpellBuilder()
    
    # Test building from template
    templates = SpellTemplateLibrary.get_all_templates()
    fireball_template = templates['direct_damage'].copy()
    fireball_template['name'] = "Test Fireball"
    
    try:
        spell = builder.build_spell_from_dict(fireball_template)
        
        if spell.name == "Test Fireball":
            print("  ✓ Spell building from template passed")
        else:
            print(f"  ✗ Spell name mismatch: expected 'Test Fireball', got '{spell.name}'")
            return False
    
    except Exception as e:
        print(f"  ✗ Spell building failed: {e}")
        return False
    
    # Test spell with effects
    spell_with_effects = {
        "name": "Test DoT",
        "description": "A test DoT spell",
        "school": "shadow",
        "cast_time": 1.5,
        "cooldown": 0.0,
        "mana_cost": 150,
        "target_type": "single_enemy",
        "base_damage": 100,
        "spell_power_coefficient": 0.5,
        "can_crit": True,
        "effects": [
            {
                "type": "damage_over_time",
                "value": 50,
                "duration": 12.0,
                "tick_interval": 3.0,
                "chance": 1.0
            }
        ]
    }
    
    try:
        spell = builder.build_spell_from_dict(spell_with_effects)
        print("  ✓ Spell with effects building passed")
    except Exception as e:
        print(f"  ✗ Spell with effects failed: {e}")
        return False
    
    print("Building: All tests passed")
    return True


def test_spell_functionality():
    """Test that created spells work correctly."""
    print("\nTesting spell functionality...")
    
    builder = SpellBuilder()
    
    # Create test characters
    mage = ModularCharacter("Test Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 200)
    mage.stats.set_stat(StatType.CRIT_CHANCE, 0.1)
    mage.current_mana = 5000
    
    target = ModularCharacter("Test Target")
    target.stats.set_stat(StatType.HEALTH, 2000)
    target.current_health = target.get_max_health()
    
    # Test direct damage spell
    fireball_data = {
        "name": "Test Fireball",
        "description": "Test fireball",
        "school": "fire",
        "cast_time": 2.5,
        "cooldown": 0.0,
        "mana_cost": 200,
        "target_type": "single_enemy",
        "base_damage": [400, 500],
        "spell_power_coefficient": 1.0,
        "can_crit": True
    }
    
    try:
        fireball = builder.build_spell_from_dict(fireball_data)
        
        # Test damage calculation
        damage = fireball.calculate_damage(mage, target)
        expected_min = 400 + (200 * 1.0)  # base + spell_power * coefficient
        expected_max = 500 + (200 * 1.0)
        
        if expected_min <= damage <= expected_max:
            print(f"  ✓ Damage calculation correct: {damage}")
        else:
            print(f"  ✗ Damage calculation wrong: {damage} (expected {expected_min}-{expected_max})")
            return False
        
        # Test spell casting
        initial_mana = mage.current_mana
        initial_health = target.current_health
        
        result = fireball.cast(mage, target)
        
        if result.success:
            print("  ✓ Spell casting successful")
        else:
            print("  ✗ Spell casting failed")
            return False
        
        if mage.current_mana < initial_mana:
            print("  ✓ Mana consumption correct")
        else:
            print("  ✗ Mana not consumed")
            return False
        
        if target.current_health < initial_health:
            print("  ✓ Damage application correct")
        else:
            print("  ✗ Damage not applied")
            return False
    
    except Exception as e:
        print(f"  ✗ Spell functionality test failed: {e}")
        return False
    
    print("Functionality: All tests passed")
    return True


def test_file_operations():
    """Test saving and loading spells."""
    print("\nTesting file operations...")
    
    # Test loading example spells
    try:
        if os.path.exists('data/spell_examples.json'):
            with open('data/spell_examples.json', 'r') as f:
                spell_data = json.load(f)
            
            builder = SpellBuilder()
            validator = SpellValidator()
            
            loaded_spells = 0
            for spell_name, spell_config in spell_data.items():
                try:
                    is_valid, errors, config = validator.validate_spell_from_dict(spell_config)
                    if is_valid:
                        spell = builder.build_spell_from_dict(spell_config)
                        loaded_spells += 1
                except:
                    pass
            
            if loaded_spells > 0:
                print(f"  ✓ Loaded {loaded_spells} spells from example file")
            else:
                print("  ✗ No spells loaded from example file")
                return False
        else:
            print("  ⚠ Example spell file not found, skipping file test")
    
    except Exception as e:
        print(f"  ✗ File operations failed: {e}")
        return False
    
    print("File operations: Tests passed")
    return True


def test_integration():
    """Test integration with character system."""
    print("\nTesting integration with character system...")
    
    builder = SpellBuilder()
    
    # Create a character with equipment and buffs
    mage = ModularCharacter("Geared Mage")
    mage.stats.set_stat(StatType.SPELL_POWER, 150)
    
    # Add equipment
    mage.equip_item("Test Staff", {
        StatType.SPELL_POWER: 75,
        StatType.CRIT_CHANCE: 0.02
    })
    
    # Add buffs
    mage.stats.add_flat_bonus("arcane_intellect", {
        StatType.SPELL_POWER: 25
    })
    
    mage.current_mana = 5000
    
    # Create target
    target = ModularCharacter("Test Target")
    target.stats.set_stat(StatType.HEALTH, 3000)
    target.current_health = target.get_max_health()
    
    # Create and test spell
    spell_data = {
        "name": "Integration Test Spell",
        "description": "Testing integration",
        "school": "arcane",
        "cast_time": 2.0,
        "cooldown": 0.0,
        "mana_cost": 250,
        "target_type": "single_enemy",
        "base_damage": 300,
        "spell_power_coefficient": 1.0,
        "can_crit": True
    }
    
    try:
        spell = builder.build_spell_from_dict(spell_data)
        
        # Test that spell uses character's effective stats
        total_spell_power = mage.get_spell_power()  # Should include equipment and buffs
        expected_damage = 300 + total_spell_power  # base + spell_power
        
        actual_damage = spell.calculate_damage(mage, target)
        
        if abs(actual_damage - expected_damage) <= 1:  # Allow for rounding
            print(f"  ✓ Integration test passed: {actual_damage} damage with {total_spell_power} spell power")
        else:
            print(f"  ✗ Integration test failed: {actual_damage} vs expected {expected_damage}")
            return False
    
    except Exception as e:
        print(f"  ✗ Integration test failed: {e}")
        return False
    
    print("Integration: All tests passed")
    return True


def main():
    """Run all tests."""
    print("🧪 Spell Creation System - Comprehensive Test")
    print("=" * 50)
    
    tests = [
        test_spell_templates,
        test_spell_validation,
        test_spell_building,
        test_spell_functionality,
        test_file_operations,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All tests passed! The spell creation system is working perfectly.")
        print("\n✅ System is ready for:")
        print("• Creating custom spells from templates")
        print("• Validating spell balance automatically")
        print("• Building spells with complex effects")
        print("• Testing spells with different character builds")
        print("• Saving and loading spell libraries")
        print("• Full integration with the character system")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
