"""
Simple test of the modular stats system.
"""

from src.interfaces import StatType
from src.stats.stat_manager import Stat<PERSON>ana<PERSON>

def test_basic_stats():
    """Test basic stat operations."""
    print("Testing basic stats...")
    
    # Create a stat manager
    stats = StatManager()
    
    # Set some base stats
    stats.set_stat(StatType.HEALTH, 1000)
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    print(f"Health: {stats.get_stat(StatType.HEALTH)}")
    print(f"Spell Power: {stats.get_stat(StatType.SPELL_POWER)}")
    
    # Add a flat bonus
    stats.add_flat_bonus("weapon", {
        StatType.SPELL_POWER: 50
    })
    
    print(f"Spell Power after weapon: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    
    print("Basic stats test passed!")

if __name__ == "__main__":
    test_basic_stats()
