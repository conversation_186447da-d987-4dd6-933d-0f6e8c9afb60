#!/usr/bin/env python3
"""
Test script for the WoW Simulator Web Backend
"""

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from flask import Flask
        print("✅ Flask import OK")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        from flask_cors import CORS
        print("✅ Flask-CORS import OK")
    except ImportError as e:
        print(f"❌ Flask-CORS import failed: {e}")
        return False
    
    try:
        from src.character import ModularCharacter
        print("✅ ModularCharacter import OK")
    except ImportError as e:
        print(f"❌ ModularCharacter import failed: {e}")
        return False
    
    try:
        from src.spells.spell_builder import SpellBuilder
        print("✅ SpellBuilder import OK")
    except ImportError as e:
        print(f"❌ SpellBuilder import failed: {e}")
        return False
    
    try:
        from src.optimization.rotation_optimizer import RotationOptimizer
        print("✅ RotationOptimizer import OK")
    except ImportError as e:
        print(f"❌ RotationOptimizer import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic WoW Simulator functionality."""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test character creation
        from src.character import ModularCharacter
        character = ModularCharacter("Test Character")
        print("✅ Character creation OK")
        
        # Test spell builder
        from src.spells.spell_builder import SpellBuilder
        builder = SpellBuilder()
        print("✅ SpellBuilder creation OK")
        
        # Test spell templates
        from src.spells.spell_builder import SpellTemplateLibrary
        templates = SpellTemplateLibrary.get_all_templates()
        print(f"✅ Spell templates loaded: {len(templates)} templates")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_flask_app():
    """Test Flask app creation."""
    print("\n🧪 Testing Flask app...")
    
    try:
        from flask import Flask
        from flask_cors import CORS
        
        app = Flask(__name__)
        CORS(app)
        
        @app.route('/test')
        def test_route():
            return {"status": "ok"}
        
        print("✅ Flask app creation OK")
        return True
        
    except Exception as e:
        print(f"❌ Flask app test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧙‍♂️ WoW Simulator Web Backend Test")
    print("=" * 40)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test basic functionality
    if not test_basic_functionality():
        all_passed = False
    
    # Test Flask app
    if not test_flask_app():
        all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All tests passed! Web backend should work correctly.")
        print("\n💡 Next steps:")
        print("   1. Run: python run_web_gui.py")
        print("   2. Or manually start: python web_backend.py")
        print("   3. Open index.html in your browser")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure all dependencies are installed")
        print("   2. Check that the WoW Simulator modules are working")
        print("   3. Verify Flask and Flask-CORS are installed")
    
    return 0 if all_passed else 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
