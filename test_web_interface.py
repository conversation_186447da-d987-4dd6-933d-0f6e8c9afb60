#!/usr/bin/env python3
"""
Test script for the WoW Simulator Web Interface
"""

import os
import re
from pathlib import Path

def test_html_structure():
    """Test HTML file structure and syntax."""
    print("🧪 Testing HTML structure...")
    
    html_file = Path('index.html')
    if not html_file.exists():
        print("❌ index.html not found")
        return False
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for basic HTML structure
        if not content.startswith('<!DOCTYPE html>'):
            print("❌ Missing DOCTYPE declaration")
            return False
        
        # Check for required sections
        required_sections = [
            'character-section',
            'spells-section', 
            'rotation-section',
            'testing-section'
        ]
        
        for section in required_sections:
            if section not in content:
                print(f"❌ Missing section: {section}")
                return False
        
        # Check for balanced tags (accounting for HTML5 void elements)
        void_elements = ['input', 'img', 'br', 'hr', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr']

        # Count all opening tags
        all_open_tags = re.findall(r'<(?!/)(?!!)(?!\?)[^>]*>', content)

        # Count void elements (self-closing in HTML5)
        void_count = 0
        for tag in all_open_tags:
            tag_name = re.match(r'<(\w+)', tag)
            if tag_name and tag_name.group(1).lower() in void_elements:
                void_count += 1

        # Count closing tags
        close_tags = len(re.findall(r'</[^>]*>', content))

        # Calculate expected closing tags (total open tags minus void elements)
        expected_close_tags = len(all_open_tags) - void_count

        # Check balance with some tolerance
        if abs(expected_close_tags - close_tags) > 3:
            print(f"❌ Unbalanced HTML tags: {expected_close_tags} expected close, {close_tags} actual close")
            print(f"   (Total open: {len(all_open_tags)}, Void elements: {void_count})")
            return False
        
        print("✅ HTML structure looks good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading HTML file: {e}")
        return False

def test_css_files():
    """Test CSS file existence and basic syntax."""
    print("\n🧪 Testing CSS files...")
    
    css_file = Path('static/css/style.css')
    if not css_file.exists():
        print("❌ style.css not found")
        return False
    
    try:
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for CSS variables
        if ':root' not in content:
            print("❌ CSS variables not found")
            return False
        
        # Check for basic selectors
        required_selectors = [
            '.header',
            '.nav-btn',
            '.card',
            '.btn',
            '.spell-item'
        ]
        
        for selector in required_selectors:
            if selector not in content:
                print(f"❌ Missing CSS selector: {selector}")
                return False
        
        # Basic brace balance check
        open_braces = content.count('{')
        close_braces = content.count('}')
        
        if open_braces != close_braces:
            print(f"❌ Unbalanced CSS braces: {open_braces} open, {close_braces} close")
            return False
        
        print("✅ CSS files look good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading CSS file: {e}")
        return False

def test_js_files():
    """Test JavaScript file existence and basic syntax."""
    print("\n🧪 Testing JavaScript files...")
    
    js_file = Path('static/js/app.js')
    if not js_file.exists():
        print("❌ app.js not found")
        return False
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required functions
        required_functions = [
            'updateCharacter',
            'createSpell',
            'optimizeRotation',
            'testSpell',
            'toggleAdvancedMode'
        ]
        
        for func in required_functions:
            if f'function {func}' not in content and f'{func} =' not in content:
                print(f"❌ Missing JavaScript function: {func}")
                return False
        
        # Check for API_BASE constant
        if 'API_BASE' not in content:
            print("❌ Missing API_BASE constant")
            return False
        
        # Basic syntax check - count braces and parentheses
        open_braces = content.count('{')
        close_braces = content.count('}')
        open_parens = content.count('(')
        close_parens = content.count(')')
        
        if open_braces != close_braces:
            print(f"❌ Unbalanced JavaScript braces: {open_braces} open, {close_braces} close")
            return False
        
        if abs(open_parens - close_parens) > 10:  # Some tolerance for strings containing parens
            print(f"❌ Likely unbalanced JavaScript parentheses: {open_parens} open, {close_parens} close")
            return False
        
        print("✅ JavaScript files look good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading JavaScript file: {e}")
        return False

def test_file_structure():
    """Test overall file structure."""
    print("\n🧪 Testing file structure...")
    
    required_files = [
        'index.html',
        'static/css/style.css',
        'static/js/app.js',
        'web_backend.py',
        'run_web_gui.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files present")
    return True

def main():
    """Run all tests."""
    print("🧙‍♂️ WoW Simulator Web Interface Test")
    print("=" * 40)
    
    all_passed = True
    
    # Test file structure
    if not test_file_structure():
        all_passed = False
    
    # Test HTML
    if not test_html_structure():
        all_passed = False
    
    # Test CSS
    if not test_css_files():
        all_passed = False
    
    # Test JavaScript
    if not test_js_files():
        all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All web interface tests passed!")
        print("\n💡 The web interface should work correctly.")
        print("   Run 'python run_web_gui.py' to start the application.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return 0 if all_passed else 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
