#!/usr/bin/env python3
"""
Comprehensive Backend Test Suite for WoW Simulator
Tests all Python backend functionality including spell creation, validation, and simulation.
"""

import unittest
import json
import sys
import os
from unittest.mock import patch, MagicMock
import tempfile
import time

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from spell_validator import SpellValidator
    from spell_simulator import SpellSimulator
    from character import Character
    from spell import Spell
    import web_backend
except ImportError as e:
    print(f"Warning: Could not import some modules: {e}")
    print("Some tests may be skipped.")

class TestSpellValidator(unittest.TestCase):
    """Test the spell validation system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.validator = SpellValidator()
    
    def test_basic_spell_validation(self):
        """Test basic spell validation with valid data."""
        valid_spell = {
            "name": "Test Fireball",
            "description": "A basic fire spell",
            "school": "fire",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 200,
            "target_type": "single_enemy",
            "base_damage": [200, 300],
            "spell_power_coefficient": 1.0,
            "can_crit": True
        }
        
        is_valid, errors, config = self.validator.validate_spell_from_dict(valid_spell)
        self.assertTrue(is_valid, f"Valid spell failed validation: {errors}")
        self.assertEqual(len(errors), 0)
        self.assertIsNotNone(config)
    
    def test_invalid_spell_validation(self):
        """Test spell validation with invalid data."""
        invalid_spell = {
            "name": "",  # Empty name should fail
            "school": "invalid_school",  # Invalid school
            "cast_time": -1,  # Negative cast time
            "mana_cost": "not_a_number"  # Invalid type
        }
        
        is_valid, errors, config = self.validator.validate_spell_from_dict(invalid_spell)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_advanced_effects_validation(self):
        """Test validation of advanced spell effects."""
        spell_with_effects = {
            "name": "Complex Spell",
            "description": "A spell with advanced effects",
            "school": "arcane",
            "cast_time": 1.5,
            "cooldown": 10.0,
            "mana_cost": 300,
            "target_type": "single_enemy",
            "base_damage": [150, 250],
            "spell_power_coefficient": 0.8,
            "can_crit": True,
            "advanced_effects": [
                {
                    "effect_type": "conditional_effect",
                    "name": "Critical Bonus",
                    "description": "Extra damage on critical hits",
                    "value": {"damage_multiplier": 1.5},
                    "conditions": [
                        {
                            "condition_type": "spell_critical",
                            "operator": "==",
                            "value": "fire"
                        }
                    ],
                    "chance": 1.0,
                    "logic_operator": "AND"
                }
            ]
        }
        
        is_valid, errors, config = self.validator.validate_spell_from_dict(spell_with_effects)
        self.assertTrue(is_valid, f"Advanced effects spell failed validation: {errors}")

class TestSpellSimulator(unittest.TestCase):
    """Test the spell simulation engine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.simulator = SpellSimulator()
        self.character = Character("Test Mage", "mage", 60)
        self.character.spell_power = 300
        self.character.crit_chance = 0.15
    
    def test_basic_spell_simulation(self):
        """Test basic spell damage simulation."""
        spell_config = {
            "name": "Test Fireball",
            "school": "fire",
            "base_damage": [200, 300],
            "spell_power_coefficient": 1.0,
            "can_crit": True,
            "cast_time": 2.5,
            "mana_cost": 200
        }
        
        spell = Spell(spell_config)
        results = self.simulator.simulate_spell(spell, self.character, iterations=100)
        
        self.assertIsInstance(results, dict)
        self.assertIn('average_damage', results)
        self.assertIn('dps', results)
        self.assertIn('mana_efficiency', results)
        self.assertGreater(results['average_damage'], 0)
    
    def test_spell_with_conditions(self):
        """Test spell simulation with conditional effects."""
        spell_config = {
            "name": "Conditional Spell",
            "school": "fire",
            "base_damage": [100, 150],
            "spell_power_coefficient": 0.8,
            "can_crit": True,
            "cast_time": 1.5,
            "mana_cost": 150,
            "advanced_effects": [
                {
                    "effect_type": "conditional_effect",
                    "name": "Low Health Bonus",
                    "value": {"damage_multiplier": 2.0},
                    "conditions": [
                        {
                            "condition_type": "target_health_below",
                            "operator": "<=",
                            "value": "35"
                        }
                    ],
                    "chance": 1.0
                }
            ]
        }
        
        spell = Spell(spell_config)
        
        # Test with high health target
        results_high = self.simulator.simulate_spell(spell, self.character, 
                                                   target_health_percent=80, iterations=50)
        
        # Test with low health target
        results_low = self.simulator.simulate_spell(spell, self.character, 
                                                  target_health_percent=20, iterations=50)
        
        # Low health should do more damage due to conditional effect
        self.assertGreater(results_low['average_damage'], results_high['average_damage'])
    
    def test_rotation_simulation(self):
        """Test spell rotation simulation."""
        spell1_config = {
            "name": "Fireball",
            "school": "fire",
            "base_damage": [200, 300],
            "spell_power_coefficient": 1.0,
            "cast_time": 2.5,
            "mana_cost": 200
        }
        
        spell2_config = {
            "name": "Fire Blast",
            "school": "fire",
            "base_damage": [150, 200],
            "spell_power_coefficient": 0.6,
            "cast_time": 0.0,
            "mana_cost": 150,
            "cooldown": 8.0
        }
        
        rotation = [Spell(spell1_config), Spell(spell2_config)]
        results = self.simulator.simulate_rotation(rotation, self.character, duration=60)
        
        self.assertIsInstance(results, dict)
        self.assertIn('total_damage', results)
        self.assertIn('dps', results)
        self.assertIn('mana_used', results)
        self.assertIn('spell_breakdown', results)

class TestWebBackend(unittest.TestCase):
    """Test the web backend API endpoints."""
    
    def setUp(self):
        """Set up test client."""
        web_backend.app.config['TESTING'] = True
        self.client = web_backend.app.test_client()
    
    def test_health_endpoint(self):
        """Test the health check endpoint."""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
    
    def test_spell_creation_endpoint(self):
        """Test spell creation API endpoint."""
        spell_data = {
            "name": "API Test Spell",
            "description": "Testing spell creation via API",
            "school": "arcane",
            "cast_time": 2.0,
            "cooldown": 5.0,
            "mana_cost": 250,
            "target_type": "single_enemy",
            "base_damage": [180, 280],
            "spell_power_coefficient": 0.9,
            "can_crit": True
        }
        
        response = self.client.post('/api/spell', 
                                  data=json.dumps(spell_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('spell_id', data)
    
    def test_spell_simulation_endpoint(self):
        """Test spell simulation API endpoint."""
        simulation_data = {
            "spell_id": "test_spell",
            "character": {
                "name": "Test Character",
                "class": "mage",
                "level": 60,
                "spell_power": 300,
                "crit_chance": 0.15
            },
            "iterations": 100
        }
        
        response = self.client.post('/api/simulate', 
                                  data=json.dumps(simulation_data),
                                  content_type='application/json')
        
        # Note: This might fail if spell doesn't exist, which is expected
        # The test is to ensure the endpoint exists and handles requests
        self.assertIn(response.status_code, [200, 400, 404])

class TestPerformance(unittest.TestCase):
    """Test performance and load handling."""
    
    def test_spell_validation_performance(self):
        """Test spell validation performance with large datasets."""
        validator = SpellValidator()
        
        spell_data = {
            "name": "Performance Test Spell",
            "description": "Testing validation performance",
            "school": "fire",
            "cast_time": 2.5,
            "cooldown": 0.0,
            "mana_cost": 200,
            "target_type": "single_enemy",
            "base_damage": [200, 300],
            "spell_power_coefficient": 1.0,
            "can_crit": True
        }
        
        start_time = time.time()
        
        # Validate 1000 spells
        for _ in range(1000):
            validator.validate_spell_from_dict(spell_data)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete 1000 validations in under 5 seconds
        self.assertLess(duration, 5.0, f"Validation took too long: {duration:.2f}s")
    
    def test_simulation_performance(self):
        """Test simulation performance with complex spells."""
        simulator = SpellSimulator()
        character = Character("Perf Test", "mage", 60)
        
        complex_spell_config = {
            "name": "Complex Performance Spell",
            "school": "arcane",
            "base_damage": [150, 250],
            "spell_power_coefficient": 1.2,
            "can_crit": True,
            "cast_time": 2.0,
            "mana_cost": 300,
            "advanced_effects": [
                {
                    "effect_type": "conditional_effect",
                    "name": "Multi-Condition Effect",
                    "value": {"damage_multiplier": 1.5},
                    "conditions": [
                        {"condition_type": "spell_critical", "operator": "==", "value": "arcane"},
                        {"condition_type": "target_health_below", "operator": "<=", "value": "50"}
                    ],
                    "chance": 0.3,
                    "logic_operator": "AND"
                }
            ]
        }
        
        spell = Spell(complex_spell_config)
        
        start_time = time.time()
        results = simulator.simulate_spell(spell, character, iterations=1000)
        end_time = time.time()
        
        duration = end_time - start_time
        
        # Should complete 1000 simulations in under 10 seconds
        self.assertLess(duration, 10.0, f"Simulation took too long: {duration:.2f}s")
        self.assertIsInstance(results, dict)

def run_test_suite():
    """Run the complete test suite and return results."""
    print("🧪 Starting WoW Simulator Backend Test Suite...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestSpellValidator,
        TestSpellSimulator,
        TestWebBackend,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\n⚠️ Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('\\n')[-2]}")
    
    if not result.failures and not result.errors:
        print("✅ All tests passed!")
    
    return result

if __name__ == '__main__':
    run_test_suite()
