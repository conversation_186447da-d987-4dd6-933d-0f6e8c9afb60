#!/usr/bin/env python3
"""
Browser Automation Test Suite for WoW Simulator
Comprehensive browser testing using Selenium WebDriver for real user interaction simulation.
"""

import unittest
import time
import os
import sys
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException

class BrowserTestSuite:
    """Browser automation test utilities."""
    
    def __init__(self):
        self.driver = None
        self.base_url = None
        self.setup_driver()
    
    def setup_driver(self):
        """Set up Chrome WebDriver with optimal settings."""
        try:
            chrome_options = Options()
            
            # Performance optimizations
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")  # Faster loading
            
            # Window settings
            chrome_options.add_argument("--window-size=1920,1080")
            
            # For CI/CD environments
            if os.environ.get('CI') or os.environ.get('HEADLESS'):
                chrome_options.add_argument("--headless")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            
            # Set up base URL
            base_dir = Path(__file__).parent.parent
            self.base_url = f"file://{base_dir.absolute()}/index.html"
            
            print(f"✅ Browser automation initialized")
            print(f"🌐 Base URL: {self.base_url}")
            
        except WebDriverException as e:
            print(f"❌ Error setting up WebDriver: {e}")
            print("💡 Make sure ChromeDriver is installed and in PATH")
            raise
    
    def teardown(self):
        """Clean up browser resources."""
        if self.driver:
            self.driver.quit()
            print("🧹 Browser closed")

class TestUserInteractions(unittest.TestCase):
    """Test real user interactions with the interface."""
    
    @classmethod
    def setUpClass(cls):
        """Set up browser for all tests."""
        cls.browser = BrowserTestSuite()
        cls.driver = cls.browser.driver
        cls.base_url = cls.browser.base_url
    
    @classmethod
    def tearDownClass(cls):
        """Clean up browser."""
        cls.browser.teardown()
    
    def setUp(self):
        """Load the page before each test."""
        self.driver.get(self.base_url)
        # Wait for page to fully load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
    
    def test_page_load_and_navigation(self):
        """Test page loading and basic navigation."""
        # Check page title
        self.assertIn("WoW Simulator", self.driver.title)
        
        # Test navigation tabs
        nav_sections = ['overview', 'spells', 'rotations', 'character']
        
        for section in nav_sections:
            try:
                nav_button = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, f'[data-section="{section}"]'))
                )
                nav_button.click()
                time.sleep(0.5)  # Allow transition
                
                # Verify section is active
                active_section = self.driver.find_element(By.CSS_SELECTOR, '.section.active')
                self.assertEqual(active_section.get_attribute('id'), section)
                
            except TimeoutException:
                self.fail(f"Navigation to {section} failed")
        
        print("✅ Page navigation working correctly")
    
    def test_spell_creation_workflow(self):
        """Test complete spell creation workflow."""
        # Navigate to spells section
        spells_tab = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-section="spells"]'))
        )
        spells_tab.click()
        
        # Fill in basic spell information
        spell_name = self.driver.find_element(By.ID, "spell-name")
        spell_name.clear()
        spell_name.send_keys("Automated Test Spell")
        
        spell_description = self.driver.find_element(By.ID, "spell-description")
        spell_description.clear()
        spell_description.send_keys("A spell created by automated testing")
        
        # Select spell school
        school_select = Select(self.driver.find_element(By.ID, "spell-school"))
        school_select.select_by_value("fire")
        
        # Set cast time
        cast_time = self.driver.find_element(By.ID, "cast-time")
        cast_time.clear()
        cast_time.send_keys("2.5")
        
        # Set mana cost
        mana_cost = self.driver.find_element(By.ID, "mana-cost")
        mana_cost.clear()
        mana_cost.send_keys("200")
        
        # Submit spell creation
        create_button = self.driver.find_element(By.CSS_SELECTOR, 'button[onclick="createSpell()"]')
        create_button.click()
        
        # Wait for response (notification or spell in list)
        time.sleep(2)
        
        # Check for success indicators
        notifications = self.driver.find_elements(By.CLASS_NAME, "notification")
        spell_items = self.driver.find_elements(By.CLASS_NAME, "spell-item")
        
        success = len(notifications) > 0 or len(spell_items) > 0
        self.assertTrue(success, "Spell creation did not show success indicators")
        
        print("✅ Spell creation workflow completed successfully")
    
    def test_advanced_spell_builder(self):
        """Test advanced spell builder functionality."""
        # Navigate to spells section
        spells_tab = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-section="spells"]'))
        )
        spells_tab.click()
        
        # Activate advanced mode
        advanced_button = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[onclick="toggleAdvancedMode()"]'))
        )
        advanced_button.click()
        
        # Wait for advanced builder to appear
        advanced_config = WebDriverWait(self.driver, 10).until(
            EC.visibility_of_element_located((By.ID, "advanced-spell-config"))
        )
        
        # Test effect selection (Step 1)
        effect_buttons = self.driver.find_elements(By.CLASS_NAME, "effect-btn")
        self.assertGreater(len(effect_buttons), 0, "No effect buttons found")
        
        # Click first effect
        effect_buttons[0].click()
        time.sleep(0.5)
        
        # Navigate to conditions step
        next_button = self.driver.find_element(By.ID, "next-step")
        next_button.click()
        time.sleep(0.5)
        
        # Test condition addition (Step 2)
        add_condition_button = self.driver.find_element(By.CSS_SELECTOR, 'button[onclick="addCondition()"]')
        add_condition_button.click()
        time.sleep(0.5)
        
        # Select a condition type
        condition_selects = self.driver.find_elements(By.CLASS_NAME, "condition-type")
        if condition_selects:
            condition_select = Select(condition_selects[0])
            # Select a non-empty option
            options = [opt for opt in condition_select.options if opt.get_attribute('value')]
            if options:
                condition_select.select_by_value(options[0].get_attribute('value'))
                time.sleep(0.5)
        
        # Navigate to properties step
        next_button = self.driver.find_element(By.ID, "next-step")
        next_button.click()
        time.sleep(0.5)
        
        # Finish builder
        finish_button = self.driver.find_element(By.ID, "finish-builder")
        finish_button.click()
        time.sleep(1)
        
        print("✅ Advanced spell builder workflow completed")
    
    def test_template_loading(self):
        """Test spell template loading functionality."""
        # Navigate to spells section
        spells_tab = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-section="spells"]'))
        )
        spells_tab.click()
        
        # Find template cards
        template_cards = self.driver.find_elements(By.CLASS_NAME, "template-card")
        self.assertGreater(len(template_cards), 0, "No template cards found")
        
        # Click first template
        template_cards[0].click()
        time.sleep(2)
        
        # Check if template loaded (form should be populated)
        spell_name = self.driver.find_element(By.ID, "spell-name")
        name_value = spell_name.get_attribute("value")
        
        self.assertNotEqual(name_value, "", "Template did not populate spell name")
        
        print(f"✅ Template loaded successfully: {name_value}")
    
    def test_responsive_design(self):
        """Test responsive design at different screen sizes."""
        screen_sizes = [
            (1920, 1080, "Desktop"),
            (1024, 768, "Tablet"),
            (375, 667, "Mobile")
        ]
        
        for width, height, device in screen_sizes:
            self.driver.set_window_size(width, height)
            time.sleep(1)  # Allow layout to adjust
            
            # Check if navigation is accessible
            nav_elements = self.driver.find_elements(By.CLASS_NAME, "nav-btn")
            self.assertGreater(len(nav_elements), 0, f"Navigation not found on {device}")
            
            # Check if main content is visible
            main_content = self.driver.find_element(By.TAG_NAME, "main")
            self.assertTrue(main_content.is_displayed(), f"Main content not visible on {device}")
            
            print(f"✅ {device} layout ({width}x{height}) working correctly")
    
    def test_form_validation(self):
        """Test form validation and error handling."""
        # Navigate to spells section
        spells_tab = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-section="spells"]'))
        )
        spells_tab.click()
        
        # Try to create spell with empty name
        spell_name = self.driver.find_element(By.ID, "spell-name")
        spell_name.clear()
        
        create_button = self.driver.find_element(By.CSS_SELECTOR, 'button[onclick="createSpell()"]')
        create_button.click()
        time.sleep(1)
        
        # Check for validation feedback
        # This could be a notification, error message, or form validation
        notifications = self.driver.find_elements(By.CLASS_NAME, "notification")
        invalid_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input:invalid")
        
        validation_present = len(notifications) > 0 or len(invalid_inputs) > 0
        self.assertTrue(validation_present, "No validation feedback for empty form")
        
        print("✅ Form validation working correctly")

class TestPerformanceInBrowser(unittest.TestCase):
    """Test performance metrics in real browser environment."""
    
    @classmethod
    def setUpClass(cls):
        """Set up browser for performance tests."""
        cls.browser = BrowserTestSuite()
        cls.driver = cls.browser.driver
        cls.base_url = cls.browser.base_url
    
    @classmethod
    def tearDownClass(cls):
        """Clean up browser."""
        cls.browser.teardown()
    
    def test_page_load_performance(self):
        """Test page loading performance."""
        start_time = time.time()
        
        self.driver.get(self.base_url)
        
        # Wait for page to be fully loaded
        WebDriverWait(self.driver, 30).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
        
        load_time = time.time() - start_time
        
        # Check if critical elements are present
        critical_elements = [
            (By.TAG_NAME, "nav"),
            (By.TAG_NAME, "main"),
            (By.ID, "spell-name"),
            (By.CLASS_NAME, "nav-btn")
        ]
        
        for selector_type, selector in critical_elements:
            element = self.driver.find_element(selector_type, selector)
            self.assertTrue(element.is_displayed(), f"Critical element not visible: {selector}")
        
        print(f"📊 Page Load Performance:")
        print(f"   Load time: {load_time:.2f}s")
        print(f"   Critical elements: {len(critical_elements)} found")
        
        # Performance assertion
        self.assertLess(load_time, 10.0, "Page load time too slow")  # < 10 seconds
    
    def test_interaction_responsiveness(self):
        """Test UI interaction responsiveness."""
        self.driver.get(self.base_url)
        
        # Wait for page load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Test navigation responsiveness
        nav_buttons = self.driver.find_elements(By.CLASS_NAME, "nav-btn")
        
        interaction_times = []
        
        for button in nav_buttons[:3]:  # Test first 3 buttons
            start_time = time.time()
            button.click()
            
            # Wait for section to become active
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".section.active"))
            )
            
            interaction_time = time.time() - start_time
            interaction_times.append(interaction_time)
        
        avg_interaction_time = sum(interaction_times) / len(interaction_times)
        max_interaction_time = max(interaction_times)
        
        print(f"⚡ Interaction Performance:")
        print(f"   Average response: {avg_interaction_time*1000:.2f}ms")
        print(f"   Max response: {max_interaction_time*1000:.2f}ms")
        print(f"   Interactions tested: {len(interaction_times)}")
        
        # Performance assertions
        self.assertLess(avg_interaction_time, 0.5, "Average interaction too slow")  # < 500ms
        self.assertLess(max_interaction_time, 1.0, "Max interaction too slow")  # < 1s

def run_browser_tests():
    """Run the complete browser automation test suite."""
    print("🌐 WoW Simulator - Browser Automation Test Suite")
    print("=" * 60)
    
    try:
        # Check if ChromeDriver is available
        from selenium import webdriver
        driver = webdriver.Chrome()
        driver.quit()
        print("✅ ChromeDriver available")
    except Exception as e:
        print(f"❌ ChromeDriver not available: {e}")
        print("💡 Install ChromeDriver to run browser tests")
        return None
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestUserInteractions,
        TestPerformanceInBrowser
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🌐 Browser Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
        print(f"   Success rate: {success_rate:.1f}%")
    
    if not result.failures and not result.errors:
        print("✅ All browser tests passed!")
        print("🌐 User interface working perfectly!")
    else:
        print("⚠️  Some browser tests failed")
    
    return result

if __name__ == '__main__':
    run_browser_tests()
