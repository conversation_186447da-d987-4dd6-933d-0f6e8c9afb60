#!/usr/bin/env python3
"""
Integration Test Suite for WoW Simulator
Tests end-to-end functionality including frontend-backend communication,
spell creation workflows, and complete user scenarios.
"""

import unittest
import json
import time
import requests
import subprocess
import threading
import os
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class IntegrationTestSuite:
    """Main integration test suite coordinator."""
    
    def __init__(self):
        self.backend_process = None
        self.driver = None
        self.base_url = "http://localhost:5000"
        self.frontend_url = f"file://{os.path.abspath('index.html')}"
        
    def setup_backend(self):
        """Start the backend server for testing."""
        try:
            # Check if backend is already running
            response = requests.get(f"{self.base_url}/api/health", timeout=2)
            if response.status_code == 200:
                print("✅ Backend already running")
                return True
        except requests.exceptions.RequestException:
            pass
        
        try:
            # Start backend server
            print("🚀 Starting backend server...")
            self.backend_process = subprocess.Popen(
                [sys.executable, "web_backend.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            for _ in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(f"{self.base_url}/api/health", timeout=1)
                    if response.status_code == 200:
                        print("✅ Backend server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
            
            print("❌ Backend server failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting backend: {e}")
            return False
    
    def setup_frontend(self):
        """Setup Selenium WebDriver for frontend testing."""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            
            print("✅ Frontend WebDriver initialized")
            return True
            
        except WebDriverException as e:
            print(f"❌ Error setting up WebDriver: {e}")
            print("💡 Make sure ChromeDriver is installed and in PATH")
            return False
        except Exception as e:
            print(f"❌ Unexpected error in frontend setup: {e}")
            return False
    
    def teardown(self):
        """Clean up test environment."""
        if self.driver:
            self.driver.quit()
            print("🧹 WebDriver closed")
        
        if self.backend_process:
            self.backend_process.terminate()
            self.backend_process.wait()
            print("🧹 Backend server stopped")

class TestEndToEndWorkflows(unittest.TestCase):
    """Test complete user workflows from frontend to backend."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment once for all tests."""
        cls.test_suite = IntegrationTestSuite()
        
        # Setup backend
        if not cls.test_suite.setup_backend():
            raise unittest.SkipTest("Backend setup failed")
        
        # Setup frontend
        if not cls.test_suite.setup_frontend():
            raise unittest.SkipTest("Frontend setup failed")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        cls.test_suite.teardown()
    
    def test_spell_creation_workflow(self):
        """Test complete spell creation from frontend to backend."""
        driver = self.test_suite.driver
        
        try:
            # Load the frontend
            driver.get(self.test_suite.frontend_url)
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "spell-name"))
            )
            
            # Navigate to spells section
            spells_tab = driver.find_element(By.CSS_SELECTOR, '[data-section="spells"]')
            spells_tab.click()
            
            # Fill in spell form
            spell_name = driver.find_element(By.ID, "spell-name")
            spell_name.clear()
            spell_name.send_keys("Integration Test Spell")
            
            spell_description = driver.find_element(By.ID, "spell-description")
            spell_description.clear()
            spell_description.send_keys("A spell created during integration testing")
            
            spell_school = driver.find_element(By.ID, "spell-school")
            spell_school.send_keys("fire")
            
            # Submit spell creation
            create_button = driver.find_element(By.CSS_SELECTOR, 'button[onclick="createSpell()"]')
            create_button.click()
            
            # Wait for response (notification or result)
            time.sleep(2)
            
            # Check for success notification or spell in list
            notifications = driver.find_elements(By.CLASS_NAME, "notification")
            spell_items = driver.find_elements(By.CLASS_NAME, "spell-item")
            
            success = len(notifications) > 0 or len(spell_items) > 0
            self.assertTrue(success, "Spell creation workflow failed")
            
        except TimeoutException:
            self.fail("Frontend elements not found - page may not have loaded correctly")
        except Exception as e:
            self.fail(f"Spell creation workflow failed: {e}")
    
    def test_advanced_builder_workflow(self):
        """Test advanced spell builder workflow."""
        driver = self.test_suite.driver
        
        try:
            # Load the frontend
            driver.get(self.test_suite.frontend_url)
            
            # Navigate to spells section
            WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-section="spells"]'))
            ).click()
            
            # Activate advanced mode
            advanced_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[onclick="toggleAdvancedMode()"]'))
            )
            advanced_button.click()
            
            # Wait for advanced builder to appear
            WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.ID, "advanced-spell-config"))
            )
            
            # Test effect selection
            effect_buttons = driver.find_elements(By.CLASS_NAME, "effect-btn")
            if effect_buttons:
                effect_buttons[0].click()  # Select first effect
                time.sleep(1)
            
            # Test condition addition
            add_condition_button = driver.find_elements(By.CSS_SELECTOR, 'button[onclick="addCondition()"]')
            if add_condition_button:
                add_condition_button[0].click()
                time.sleep(1)
            
            # Navigate through builder steps
            next_buttons = driver.find_elements(By.ID, "next-step")
            if next_buttons:
                next_buttons[0].click()
                time.sleep(1)
            
            # Check if builder is functional
            builder_steps = driver.find_elements(By.CLASS_NAME, "builder-step")
            self.assertGreater(len(builder_steps), 0, "Advanced builder not found")
            
        except TimeoutException:
            self.fail("Advanced builder elements not found")
        except Exception as e:
            self.fail(f"Advanced builder workflow failed: {e}")
    
    def test_template_loading_workflow(self):
        """Test spell template loading workflow."""
        driver = self.test_suite.driver
        
        try:
            # Load the frontend
            driver.get(self.test_suite.frontend_url)
            
            # Navigate to spells section
            WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-section="spells"]'))
            ).click()
            
            # Find and click a template
            template_cards = driver.find_elements(By.CLASS_NAME, "template-card")
            if template_cards:
                template_cards[0].click()
                time.sleep(2)
                
                # Check if template loaded (form should be populated)
                spell_name = driver.find_element(By.ID, "spell-name")
                name_value = spell_name.get_attribute("value")
                
                self.assertNotEqual(name_value, "", "Template did not populate spell name")
            else:
                self.fail("No template cards found")
                
        except TimeoutException:
            self.fail("Template elements not found")
        except Exception as e:
            self.fail(f"Template loading workflow failed: {e}")

class TestAPIIntegration(unittest.TestCase):
    """Test API endpoints and data flow."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.test_suite = IntegrationTestSuite()
        if not cls.test_suite.setup_backend():
            raise unittest.SkipTest("Backend setup failed")
        cls.base_url = cls.test_suite.base_url
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        cls.test_suite.teardown()
    
    def test_api_health_check(self):
        """Test API health endpoint."""
        response = requests.get(f"{self.base_url}/api/health")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(data["status"], "healthy")
    
    def test_spell_creation_api(self):
        """Test spell creation via API."""
        spell_data = {
            "name": "API Integration Test",
            "description": "Testing API integration",
            "school": "arcane",
            "cast_time": 2.0,
            "cooldown": 0.0,
            "mana_cost": 200,
            "target_type": "single_enemy",
            "base_damage": [150, 250],
            "spell_power_coefficient": 1.0,
            "can_crit": True
        }
        
        response = requests.post(
            f"{self.base_url}/api/spell",
            json=spell_data,
            headers={"Content-Type": "application/json"}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
    
    def test_spell_templates_api(self):
        """Test spell templates endpoint."""
        response = requests.get(f"{self.base_url}/api/templates")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("templates", data)
        self.assertIsInstance(data["templates"], dict)

class TestPerformanceIntegration(unittest.TestCase):
    """Test system performance under load."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.test_suite = IntegrationTestSuite()
        if not cls.test_suite.setup_backend():
            raise unittest.SkipTest("Backend setup failed")
        cls.base_url = cls.test_suite.base_url
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        cls.test_suite.teardown()
    
    def test_concurrent_spell_creation(self):
        """Test concurrent spell creation requests."""
        import concurrent.futures
        
        def create_spell(spell_id):
            spell_data = {
                "name": f"Concurrent Test Spell {spell_id}",
                "description": f"Testing concurrent creation {spell_id}",
                "school": "fire",
                "cast_time": 2.0,
                "cooldown": 0.0,
                "mana_cost": 200,
                "target_type": "single_enemy",
                "base_damage": [100, 200],
                "spell_power_coefficient": 1.0,
                "can_crit": True
            }
            
            response = requests.post(
                f"{self.base_url}/api/spell",
                json=spell_data,
                timeout=10
            )
            return response.status_code == 200
        
        # Test 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_spell, i) for i in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        success_rate = sum(results) / len(results)
        self.assertGreater(success_rate, 0.8, f"Concurrent request success rate too low: {success_rate}")

def run_integration_tests():
    """Run the complete integration test suite."""
    print("🔗 Starting WoW Simulator Integration Test Suite...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestAPIIntegration,
        TestPerformanceIntegration,
        TestEndToEndWorkflows  # This one last as it requires browser
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎯 Integration Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if not result.failures and not result.errors:
        print("✅ All integration tests passed!")
    
    return result

if __name__ == '__main__':
    run_integration_tests()
