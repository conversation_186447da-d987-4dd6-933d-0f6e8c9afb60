#!/usr/bin/env python3
"""
Load Testing Suite for WoW Simulator
Comprehensive load testing scenarios to validate system performance under stress.
"""

import unittest
import time
import requests
import json
import threading
import concurrent.futures
import statistics
import random
import sys
import os
from pathlib import Path
import subprocess
import queue
import psutil

class LoadTestScenarios:
    """Load testing scenario utilities and configurations."""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.results = {}
        self.backend_process = None
        self.backend_running = False
        
    def setup_backend(self):
        """Start backend server for load testing."""
        try:
            # Check if backend is already running
            response = requests.get(f"{self.base_url}/api/health", timeout=2)
            if response.status_code == 200:
                self.backend_running = True
                print("✅ Backend already running")
                return True
        except requests.exceptions.RequestException:
            pass
        
        try:
            # Start backend server
            print("🚀 Starting backend server for load testing...")
            base_dir = Path(__file__).parent.parent
            self.backend_process = subprocess.Popen(
                [sys.executable, "web_backend.py"],
                cwd=base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            for _ in range(30):
                try:
                    response = requests.get(f"{self.base_url}/api/health", timeout=1)
                    if response.status_code == 200:
                        self.backend_running = True
                        print("✅ Backend server started for load testing")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
            
            print("❌ Backend server failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting backend: {e}")
            return False
    
    def teardown_backend(self):
        """Stop backend server."""
        if self.backend_process:
            self.backend_process.terminate()
            self.backend_process.wait()
            print("🧹 Backend server stopped")
    
    def generate_test_spell_data(self):
        """Generate random spell data for testing."""
        schools = ['fire', 'frost', 'arcane', 'shadow', 'nature', 'holy']
        target_types = ['single_enemy', 'single_ally', 'aoe_enemy', 'aoe_ally']
        
        return {
            "name": f"Load Test Spell {random.randint(1000, 9999)}",
            "description": f"Generated for load testing {time.time()}",
            "school": random.choice(schools),
            "cast_time": round(random.uniform(0.0, 5.0), 1),
            "cooldown": round(random.uniform(0.0, 60.0), 1),
            "mana_cost": random.randint(50, 500),
            "target_type": random.choice(target_types),
            "base_damage": [random.randint(100, 300), random.randint(300, 500)],
            "spell_power_coefficient": round(random.uniform(0.5, 1.5), 2),
            "can_crit": random.choice([True, False])
        }

class TestBasicLoadScenarios(unittest.TestCase):
    """Test basic load scenarios."""
    
    @classmethod
    def setUpClass(cls):
        """Set up load testing environment."""
        cls.load_tester = LoadTestScenarios()
        if not cls.load_tester.setup_backend():
            raise unittest.SkipTest("Backend setup failed")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up load testing environment."""
        cls.load_tester.teardown_backend()
    
    def test_sustained_request_load(self):
        """Test sustained request load over time."""
        if not self.load_tester.backend_running:
            self.skipTest("Backend not running")
        
        print("\n🔄 Testing sustained request load...")
        
        def make_health_check():
            try:
                start = time.time()
                response = requests.get(f"{self.load_tester.base_url}/api/health", timeout=5)
                end = time.time()
                return {
                    'success': response.status_code == 200,
                    'duration': end - start,
                    'timestamp': start
                }
            except requests.exceptions.RequestException:
                return {'success': False, 'duration': 5.0, 'timestamp': time.time()}
        
        # Run sustained load for 60 seconds
        start_time = time.time()
        results = []
        
        while time.time() - start_time < 60:  # 60 seconds
            result = make_health_check()
            results.append(result)
            time.sleep(0.1)  # 10 requests per second
        
        # Analyze results
        successful_requests = [r for r in results if r['success']]
        success_rate = len(successful_requests) / len(results)
        
        if successful_requests:
            avg_response_time = statistics.mean([r['duration'] for r in successful_requests])
            max_response_time = max([r['duration'] for r in successful_requests])
            min_response_time = min([r['duration'] for r in successful_requests])
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        print(f"📊 Sustained Load Results:")
        print(f"   Duration: 60 seconds")
        print(f"   Total requests: {len(results)}")
        print(f"   Successful: {len(successful_requests)}")
        print(f"   Success rate: {success_rate*100:.1f}%")
        print(f"   Average response: {avg_response_time*1000:.2f}ms")
        print(f"   Max response: {max_response_time*1000:.2f}ms")
        print(f"   Min response: {min_response_time*1000:.2f}ms")
        
        # Assertions
        self.assertGreater(success_rate, 0.95, "Success rate too low under sustained load")
        if successful_requests:
            self.assertLess(avg_response_time, 0.3, "Average response time too slow")
    
    def test_burst_request_load(self):
        """Test burst request scenarios."""
        if not self.load_tester.backend_running:
            self.skipTest("Backend not running")
        
        print("\n💥 Testing burst request load...")
        
        def make_request():
            try:
                start = time.time()
                response = requests.get(f"{self.load_tester.base_url}/api/templates", timeout=10)
                end = time.time()
                return {
                    'success': response.status_code == 200,
                    'duration': end - start
                }
            except requests.exceptions.RequestException:
                return {'success': False, 'duration': 10.0}
        
        # Test burst of 100 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            start_time = time.time()
            futures = [executor.submit(make_request) for _ in range(100)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            total_time = time.time() - start_time
        
        successful_requests = [r for r in results if r['success']]
        success_rate = len(successful_requests) / len(results)
        
        if successful_requests:
            avg_response_time = statistics.mean([r['duration'] for r in successful_requests])
            max_response_time = max([r['duration'] for r in successful_requests])
        else:
            avg_response_time = max_response_time = 0
        
        requests_per_second = len(results) / total_time
        
        print(f"📊 Burst Load Results:")
        print(f"   Total requests: {len(results)}")
        print(f"   Successful: {len(successful_requests)}")
        print(f"   Success rate: {success_rate*100:.1f}%")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Requests/second: {requests_per_second:.1f}")
        print(f"   Average response: {avg_response_time*1000:.2f}ms")
        print(f"   Max response: {max_response_time*1000:.2f}ms")
        
        # Assertions
        self.assertGreater(success_rate, 0.8, "Success rate too low under burst load")
        self.assertGreater(requests_per_second, 10, "Throughput too low")

class TestSpellCreationLoad(unittest.TestCase):
    """Test spell creation under load."""
    
    @classmethod
    def setUpClass(cls):
        """Set up spell creation load testing."""
        cls.load_tester = LoadTestScenarios()
        if not cls.load_tester.setup_backend():
            raise unittest.SkipTest("Backend setup failed")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up load testing environment."""
        cls.load_tester.teardown_backend()
    
    def test_concurrent_spell_creation(self):
        """Test concurrent spell creation load."""
        if not self.load_tester.backend_running:
            self.skipTest("Backend not running")
        
        print("\n🎯 Testing concurrent spell creation...")
        
        def create_spell():
            spell_data = self.load_tester.generate_test_spell_data()
            try:
                start = time.time()
                response = requests.post(
                    f"{self.load_tester.base_url}/api/spell",
                    json=spell_data,
                    headers={"Content-Type": "application/json"},
                    timeout=15
                )
                end = time.time()
                return {
                    'success': response.status_code == 200,
                    'duration': end - start,
                    'spell_name': spell_data['name']
                }
            except requests.exceptions.RequestException as e:
                return {
                    'success': False,
                    'duration': 15.0,
                    'error': str(e),
                    'spell_name': spell_data['name']
                }
        
        # Test 50 concurrent spell creations
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            start_time = time.time()
            futures = [executor.submit(create_spell) for _ in range(50)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            total_time = time.time() - start_time
        
        successful_creations = [r for r in results if r['success']]
        success_rate = len(successful_creations) / len(results)
        
        if successful_creations:
            avg_creation_time = statistics.mean([r['duration'] for r in successful_creations])
            max_creation_time = max([r['duration'] for r in successful_creations])
        else:
            avg_creation_time = max_creation_time = 0
        
        creations_per_second = len(results) / total_time
        
        print(f"📊 Spell Creation Load Results:")
        print(f"   Total attempts: {len(results)}")
        print(f"   Successful: {len(successful_creations)}")
        print(f"   Success rate: {success_rate*100:.1f}%")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Creations/second: {creations_per_second:.1f}")
        print(f"   Average time: {avg_creation_time*1000:.2f}ms")
        print(f"   Max time: {max_creation_time*1000:.2f}ms")
        
        # Assertions
        self.assertGreater(success_rate, 0.8, "Spell creation success rate too low")
        if successful_creations:
            self.assertLess(avg_creation_time, 2.0, "Average spell creation too slow")
    
    def test_mixed_workload(self):
        """Test mixed workload of different operations."""
        if not self.load_tester.backend_running:
            self.skipTest("Backend not running")
        
        print("\n🔀 Testing mixed workload...")
        
        def health_check():
            try:
                response = requests.get(f"{self.load_tester.base_url}/api/health", timeout=5)
                return {'type': 'health', 'success': response.status_code == 200}
            except:
                return {'type': 'health', 'success': False}
        
        def get_templates():
            try:
                response = requests.get(f"{self.load_tester.base_url}/api/templates", timeout=5)
                return {'type': 'templates', 'success': response.status_code == 200}
            except:
                return {'type': 'templates', 'success': False}
        
        def create_spell():
            spell_data = self.load_tester.generate_test_spell_data()
            try:
                response = requests.post(
                    f"{self.load_tester.base_url}/api/spell",
                    json=spell_data,
                    timeout=10
                )
                return {'type': 'create', 'success': response.status_code == 200}
            except:
                return {'type': 'create', 'success': False}
        
        # Mixed workload: 40% health checks, 40% template requests, 20% spell creation
        operations = (
            [health_check] * 40 +
            [get_templates] * 40 +
            [create_spell] * 20
        )
        
        random.shuffle(operations)
        
        # Execute mixed workload
        with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
            start_time = time.time()
            futures = [executor.submit(op) for op in operations]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            total_time = time.time() - start_time
        
        # Analyze by operation type
        by_type = {}
        for result in results:
            op_type = result['type']
            if op_type not in by_type:
                by_type[op_type] = {'total': 0, 'successful': 0}
            by_type[op_type]['total'] += 1
            if result['success']:
                by_type[op_type]['successful'] += 1
        
        total_operations = len(results)
        operations_per_second = total_operations / total_time
        
        print(f"📊 Mixed Workload Results:")
        print(f"   Total operations: {total_operations}")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Operations/second: {operations_per_second:.1f}")
        
        for op_type, stats in by_type.items():
            success_rate = stats['successful'] / stats['total'] * 100
            print(f"   {op_type}: {stats['successful']}/{stats['total']} ({success_rate:.1f}%)")
        
        # Overall success rate
        total_successful = sum(stats['successful'] for stats in by_type.values())
        overall_success_rate = total_successful / total_operations
        
        print(f"   Overall success: {overall_success_rate*100:.1f}%")
        
        # Assertions
        self.assertGreater(overall_success_rate, 0.85, "Overall success rate too low")
        self.assertGreater(operations_per_second, 5, "Operations per second too low")

class TestSystemResourceLoad(unittest.TestCase):
    """Test system resource usage under load."""
    
    @classmethod
    def setUpClass(cls):
        """Set up resource monitoring."""
        cls.load_tester = LoadTestScenarios()
        if not cls.load_tester.setup_backend():
            raise unittest.SkipTest("Backend setup failed")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up load testing environment."""
        cls.load_tester.teardown_backend()
    
    def test_memory_usage_under_load(self):
        """Test memory usage during sustained load."""
        if not self.load_tester.backend_running:
            self.skipTest("Backend not running")
        
        print("\n🧠 Testing memory usage under load...")
        
        # Monitor memory usage
        memory_readings = []
        cpu_readings = []
        
        def monitor_resources():
            process = psutil.Process()
            while getattr(monitor_resources, 'running', True):
                memory_mb = process.memory_info().rss / 1024 / 1024
                cpu_percent = process.cpu_percent()
                memory_readings.append(memory_mb)
                cpu_readings.append(cpu_percent)
                time.sleep(0.5)
        
        # Start monitoring
        monitor_resources.running = True
        monitor_thread = threading.Thread(target=monitor_resources)
        monitor_thread.start()
        
        # Generate load
        def make_request():
            try:
                response = requests.get(f"{self.load_tester.base_url}/api/templates", timeout=5)
                return response.status_code == 200
            except:
                return False
        
        # Run load for 30 seconds
        start_time = time.time()
        results = []
        
        while time.time() - start_time < 30:
            result = make_request()
            results.append(result)
            time.sleep(0.1)  # 10 requests per second
        
        # Stop monitoring
        monitor_resources.running = False
        monitor_thread.join()
        
        # Analyze resource usage
        if memory_readings:
            initial_memory = memory_readings[0]
            peak_memory = max(memory_readings)
            avg_memory = statistics.mean(memory_readings)
            memory_growth = peak_memory - initial_memory
        else:
            initial_memory = peak_memory = avg_memory = memory_growth = 0
        
        if cpu_readings:
            avg_cpu = statistics.mean(cpu_readings)
            peak_cpu = max(cpu_readings)
        else:
            avg_cpu = peak_cpu = 0
        
        success_rate = sum(results) / len(results) if results else 0
        
        print(f"📊 Resource Usage Results:")
        print(f"   Test duration: 30 seconds")
        print(f"   Requests made: {len(results)}")
        print(f"   Success rate: {success_rate*100:.1f}%")
        print(f"   Initial memory: {initial_memory:.2f}MB")
        print(f"   Peak memory: {peak_memory:.2f}MB")
        print(f"   Average memory: {avg_memory:.2f}MB")
        print(f"   Memory growth: {memory_growth:.2f}MB")
        print(f"   Average CPU: {avg_cpu:.1f}%")
        print(f"   Peak CPU: {peak_cpu:.1f}%")
        
        # Assertions
        self.assertLess(memory_growth, 200, "Memory growth too high under load")  # < 200MB
        self.assertLess(avg_cpu, 80, "Average CPU usage too high")  # < 80%

def run_load_tests():
    """Run the complete load testing suite."""
    print("⚡ WoW Simulator - Load Testing Suite")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestBasicLoadScenarios,
        TestSpellCreationLoad,
        TestSystemResourceLoad
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("⚡ Load Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
        print(f"   Success rate: {success_rate:.1f}%")
    
    if not result.failures and not result.errors:
        print("✅ All load tests passed!")
        print("⚡ System handles load excellently!")
    else:
        print("⚠️  Some load tests failed - check system capacity")
    
    return result

if __name__ == '__main__':
    run_load_tests()
