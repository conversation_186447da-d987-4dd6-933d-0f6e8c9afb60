#!/usr/bin/env python3
"""
Performance Benchmark Suite for WoW Simulator
Comprehensive performance testing with benchmarks, memory usage, and response time analysis.
"""

import unittest
import time
import requests
import json
import threading
import concurrent.futures
import statistics
import sys
import os
from pathlib import Path
import subprocess
import gc

# Try to import psutil, fall back to basic monitoring if not available
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️  psutil not available - memory monitoring will be limited")

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

class PerformanceBenchmarks:
    """Performance benchmark utilities and metrics."""
    
    def __init__(self):
        self.metrics = {}
        self.baseline_memory = None
        self.start_time = None
        
    def start_benchmark(self, test_name):
        """Start a performance benchmark."""
        self.start_time = time.time()
        if PSUTIL_AVAILABLE:
            self.baseline_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        else:
            self.baseline_memory = 0  # Fallback
        gc.collect()  # Clean up before test
        
    def end_benchmark(self, test_name):
        """End a performance benchmark and record metrics."""
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        duration = end_time - self.start_time
        memory_used = end_memory - self.baseline_memory
        
        self.metrics[test_name] = {
            'duration': duration,
            'memory_used': memory_used,
            'start_memory': self.baseline_memory,
            'end_memory': end_memory
        }
        
        return duration, memory_used

class TestFrontendPerformance(unittest.TestCase):
    """Test frontend performance metrics."""
    
    def setUp(self):
        """Set up performance testing."""
        self.benchmarks = PerformanceBenchmarks()
        self.base_dir = Path(__file__).parent.parent
    
    def test_javascript_file_load_time(self):
        """Test JavaScript file loading performance."""
        self.benchmarks.start_benchmark('js_load')
        
        js_file = self.base_dir / 'static/js/app.js'
        
        # Simulate file loading multiple times
        load_times = []
        for _ in range(100):
            start = time.time()
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Simulate basic parsing
                lines = content.split('\n')
                functions = [line for line in lines if 'function ' in line]
            end = time.time()
            load_times.append(end - start)
        
        duration, memory = self.benchmarks.end_benchmark('js_load')
        
        avg_load_time = statistics.mean(load_times)
        max_load_time = max(load_times)
        
        print(f"\n📊 JavaScript Load Performance:")
        print(f"   Average load time: {avg_load_time*1000:.2f}ms")
        print(f"   Max load time: {max_load_time*1000:.2f}ms")
        print(f"   Total duration: {duration:.2f}s")
        print(f"   Memory used: {memory:.2f}MB")
        print(f"   Functions found: {len(functions)}")
        
        # Performance assertions
        self.assertLess(avg_load_time, 0.01, "JavaScript load time too slow")  # < 10ms
        self.assertLess(memory, 50, "Memory usage too high")  # < 50MB
    
    def test_css_file_load_time(self):
        """Test CSS file loading performance."""
        self.benchmarks.start_benchmark('css_load')
        
        css_file = self.base_dir / 'static/css/style.css'
        
        # Simulate CSS parsing multiple times
        load_times = []
        for _ in range(100):
            start = time.time()
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Simulate CSS parsing
                rules = content.count('{')
                media_queries = content.count('@media')
            end = time.time()
            load_times.append(end - start)
        
        duration, memory = self.benchmarks.end_benchmark('css_load')
        
        avg_load_time = statistics.mean(load_times)
        
        print(f"\n🎨 CSS Load Performance:")
        print(f"   Average load time: {avg_load_time*1000:.2f}ms")
        print(f"   CSS rules: {rules}")
        print(f"   Media queries: {media_queries}")
        print(f"   Memory used: {memory:.2f}MB")
        
        self.assertLess(avg_load_time, 0.005, "CSS load time too slow")  # < 5ms
    
    def test_html_parsing_performance(self):
        """Test HTML parsing performance."""
        self.benchmarks.start_benchmark('html_parse')
        
        html_file = self.base_dir / 'index.html'
        
        parse_times = []
        for _ in range(50):
            start = time.time()
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Simulate DOM parsing
                elements = content.count('<')
                ids = content.count('id=')
                classes = content.count('class=')
            end = time.time()
            parse_times.append(end - start)
        
        duration, memory = self.benchmarks.end_benchmark('html_parse')
        
        avg_parse_time = statistics.mean(parse_times)
        
        print(f"\n📄 HTML Parse Performance:")
        print(f"   Average parse time: {avg_parse_time*1000:.2f}ms")
        print(f"   Elements: {elements}")
        print(f"   IDs: {ids}")
        print(f"   Classes: {classes}")
        
        self.assertLess(avg_parse_time, 0.01, "HTML parsing too slow")  # < 10ms

class TestBackendPerformance(unittest.TestCase):
    """Test backend API performance."""
    
    def setUp(self):
        """Set up backend performance testing."""
        self.benchmarks = PerformanceBenchmarks()
        self.base_url = "http://localhost:5000"
        self.backend_process = None
        
        # Try to start backend if not running
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=2)
            self.backend_running = response.status_code == 200
        except requests.exceptions.RequestException:
            self.backend_running = False
            print("⚠️  Backend not running - some tests will be skipped")
    
    def test_api_response_times(self):
        """Test API endpoint response times."""
        if not self.backend_running:
            self.skipTest("Backend not running")
        
        self.benchmarks.start_benchmark('api_response')
        
        endpoints = [
            '/api/health',
            '/api/templates',
            '/api/spell_schools',
            '/api/target_types'
        ]
        
        response_times = {}
        
        for endpoint in endpoints:
            times = []
            for _ in range(20):  # Test each endpoint 20 times
                start = time.time()
                try:
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                    end = time.time()
                    if response.status_code == 200:
                        times.append(end - start)
                except requests.exceptions.RequestException:
                    pass
            
            if times:
                response_times[endpoint] = {
                    'avg': statistics.mean(times),
                    'max': max(times),
                    'min': min(times),
                    'count': len(times)
                }
        
        duration, memory = self.benchmarks.end_benchmark('api_response')
        
        print(f"\n🔗 API Response Performance:")
        for endpoint, metrics in response_times.items():
            print(f"   {endpoint}:")
            print(f"     Average: {metrics['avg']*1000:.2f}ms")
            print(f"     Max: {metrics['max']*1000:.2f}ms")
            print(f"     Min: {metrics['min']*1000:.2f}ms")
            print(f"     Requests: {metrics['count']}")
        
        # Performance assertions
        for endpoint, metrics in response_times.items():
            self.assertLess(metrics['avg'], 0.2, f"{endpoint} average response too slow")  # < 200ms
            self.assertLess(metrics['max'], 1.0, f"{endpoint} max response too slow")  # < 1s
    
    def test_concurrent_requests_performance(self):
        """Test performance under concurrent load."""
        if not self.backend_running:
            self.skipTest("Backend not running")
        
        self.benchmarks.start_benchmark('concurrent_load')
        
        def make_request():
            try:
                start = time.time()
                response = requests.get(f"{self.base_url}/api/health", timeout=10)
                end = time.time()
                return {
                    'success': response.status_code == 200,
                    'duration': end - start
                }
            except requests.exceptions.RequestException:
                return {'success': False, 'duration': 10.0}
        
        # Test with 50 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(50)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        duration, memory = self.benchmarks.end_benchmark('concurrent_load')
        
        successful_requests = [r for r in results if r['success']]
        success_rate = len(successful_requests) / len(results)
        
        if successful_requests:
            avg_response_time = statistics.mean([r['duration'] for r in successful_requests])
            max_response_time = max([r['duration'] for r in successful_requests])
        else:
            avg_response_time = 0
            max_response_time = 0
        
        print(f"\n⚡ Concurrent Load Performance:")
        print(f"   Total requests: {len(results)}")
        print(f"   Successful: {len(successful_requests)}")
        print(f"   Success rate: {success_rate*100:.1f}%")
        print(f"   Average response: {avg_response_time*1000:.2f}ms")
        print(f"   Max response: {max_response_time*1000:.2f}ms")
        print(f"   Total duration: {duration:.2f}s")
        
        # Performance assertions
        self.assertGreater(success_rate, 0.9, "Success rate too low under load")  # > 90%
        if successful_requests:
            self.assertLess(avg_response_time, 0.5, "Average response too slow under load")  # < 500ms

class TestMemoryPerformance(unittest.TestCase):
    """Test memory usage and performance."""
    
    def setUp(self):
        """Set up memory testing."""
        self.benchmarks = PerformanceBenchmarks()
    
    def test_memory_usage_stability(self):
        """Test that memory usage remains stable."""
        self.benchmarks.start_benchmark('memory_stability')
        
        base_dir = Path(__file__).parent.parent
        js_file = base_dir / 'static/js/app.js'
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_readings = [initial_memory]
        
        # Simulate heavy file operations
        for i in range(100):
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Simulate processing
                lines = content.split('\n')
                functions = [line for line in lines if 'function' in line]
                
            # Take memory reading every 10 iterations
            if i % 10 == 0:
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                memory_readings.append(current_memory)
                
            # Force garbage collection occasionally
            if i % 25 == 0:
                gc.collect()
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_readings.append(final_memory)
        
        duration, memory_used = self.benchmarks.end_benchmark('memory_stability')
        
        memory_growth = final_memory - initial_memory
        max_memory = max(memory_readings)
        avg_memory = statistics.mean(memory_readings)
        
        print(f"\n🧠 Memory Performance:")
        print(f"   Initial memory: {initial_memory:.2f}MB")
        print(f"   Final memory: {final_memory:.2f}MB")
        print(f"   Memory growth: {memory_growth:.2f}MB")
        print(f"   Max memory: {max_memory:.2f}MB")
        print(f"   Average memory: {avg_memory:.2f}MB")
        print(f"   Duration: {duration:.2f}s")
        
        # Memory assertions
        self.assertLess(memory_growth, 100, "Memory growth too high")  # < 100MB growth
        self.assertLess(max_memory - initial_memory, 150, "Peak memory usage too high")  # < 150MB peak

def run_performance_tests():
    """Run the complete performance test suite."""
    print("⚡ WoW Simulator - Performance Benchmark Suite")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestFrontendPerformance,
        TestBackendPerformance,
        TestMemoryPerformance
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Performance Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
        print(f"   Success rate: {success_rate:.1f}%")
    
    if not result.failures and not result.errors:
        print("✅ All performance tests passed!")
        print("🚀 System performance is excellent!")
    else:
        print("⚠️  Some performance issues detected")
    
    return result

if __name__ == '__main__':
    run_performance_tests()
