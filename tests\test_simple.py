#!/usr/bin/env python3
"""
Simple Test Suite for WoW Simulator
Basic tests that work with the current codebase structure.
"""

import unittest
import json
import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality that we know exists."""
    
    def test_project_structure(self):
        """Test that required files exist."""
        base_dir = Path(__file__).parent.parent
        
        required_files = [
            'index.html',
            'static/css/style.css',
            'static/js/app.js',
            'web_backend.py'
        ]
        
        for file_path in required_files:
            full_path = base_dir / file_path
            self.assertTrue(full_path.exists(), f"Required file missing: {file_path}")
    
    def test_html_structure(self):
        """Test that HTML file has required elements."""
        base_dir = Path(__file__).parent.parent
        html_file = base_dir / 'index.html'
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required sections
        required_elements = [
            'id="spell-name"',
            'id="spell-description"',
            'id="spell-school"',
            'id="advanced-spell-config"',
            'class="condition-type"',
            'toggleAdvancedMode()'
        ]
        
        for element in required_elements:
            self.assertIn(element, content, f"Required HTML element missing: {element}")
    
    def test_css_file_structure(self):
        """Test that CSS file has required styles."""
        base_dir = Path(__file__).parent.parent
        css_file = base_dir / 'static/css/style.css'
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required CSS classes
        required_classes = [
            '.spell-item',
            '.condition-item',
            '.effect-btn',
            '.builder-step',
            '.progress-step'
        ]
        
        for css_class in required_classes:
            self.assertIn(css_class, content, f"Required CSS class missing: {css_class}")
    
    def test_javascript_functions(self):
        """Test that JavaScript file has required functions."""
        base_dir = Path(__file__).parent.parent
        js_file = base_dir / 'static/js/app.js'
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required functions
        required_functions = [
            'function toggleAdvancedMode',
            'function createSpell',
            'function addCondition',
            'function updateConditionOptions',
            'function getConditionConfig',
            'function initializeSpellBuilder'
        ]
        
        for function in required_functions:
            self.assertIn(function, content, f"Required JavaScript function missing: {function}")
    
    def test_backend_file_structure(self):
        """Test that backend file has required components."""
        base_dir = Path(__file__).parent.parent
        backend_file = base_dir / 'web_backend.py'
        
        with open(backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required backend components
        required_components = [
            'from flask import',
            '@app.route',
            'def create_spell',
            'def get_spell_templates',
            'advanced_templates'
        ]
        
        for component in required_components:
            self.assertIn(component, content, f"Required backend component missing: {component}")
    
    def test_condition_system_completeness(self):
        """Test that condition system has comprehensive coverage."""
        base_dir = Path(__file__).parent.parent
        js_file = base_dir / 'static/js/app.js'
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for condition categories
        condition_categories = [
            'spell_critical',
            'target_health_below',
            'caster_mana_below',
            'proc_triggered',
            'combat_time',
            'time_of_day',
            'damage_taken_recently'
        ]
        
        for condition in condition_categories:
            self.assertIn(condition, content, f"Condition type missing: {condition}")
    
    def test_spell_templates_exist(self):
        """Test that spell templates are defined."""
        base_dir = Path(__file__).parent.parent
        backend_file = base_dir / 'web_backend.py'
        
        with open(backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for template definitions
        template_names = [
            'living_bomb',
            'chain_lightning',
            'hot_streak_proc',
            'pyroblast',
            'cheap_shot'
        ]
        
        for template in template_names:
            self.assertIn(f'"{template}"', content, f"Spell template missing: {template}")
    
    def test_responsive_design_classes(self):
        """Test that responsive design CSS classes exist."""
        base_dir = Path(__file__).parent.parent
        css_file = base_dir / 'static/css/style.css'
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for responsive design
        responsive_elements = [
            '@media (max-width: 768px)',
            '@media (max-width: 480px)',
            'grid-template-columns',
            'flex-direction: column'
        ]
        
        for element in responsive_elements:
            self.assertIn(element, content, f"Responsive design element missing: {element}")
    
    def test_validation_system(self):
        """Test that validation system components exist."""
        base_dir = Path(__file__).parent.parent
        js_file = base_dir / 'static/js/app.js'
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for validation functions
        validation_components = [
            'validateCondition',
            'updateLogicPreview',
            'validateBuilderState',
            'showNotification'
        ]
        
        for component in validation_components:
            self.assertIn(component, content, f"Validation component missing: {component}")

class TestAdvancedFeatures(unittest.TestCase):
    """Test advanced features and integrations."""
    
    def test_advanced_builder_components(self):
        """Test that advanced builder has all required components."""
        base_dir = Path(__file__).parent.parent
        html_file = base_dir / 'index.html'
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for advanced builder elements
        builder_elements = [
            'id="builder-step-1"',
            'id="builder-step-2"',
            'id="builder-step-3"',
            'class="effect-btn"',
            'class="condition-row"',
            'id="logic-preview"'
        ]
        
        for element in builder_elements:
            self.assertIn(element, content, f"Advanced builder element missing: {element}")
    
    def test_comprehensive_conditions(self):
        """Test that all condition categories are present."""
        base_dir = Path(__file__).parent.parent
        html_file = base_dir / 'index.html'
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for condition categories
        condition_groups = [
            'Spell Conditions',
            'Target Conditions',
            'Caster Conditions',
            'Proc Conditions',
            'Combat Conditions',
            'Environmental Conditions',
            'Advanced Conditions'
        ]
        
        for group in condition_groups:
            self.assertIn(group, content, f"Condition group missing: {group}")
    
    def test_effect_types_coverage(self):
        """Test that effect types are comprehensively covered."""
        base_dir = Path(__file__).parent.parent
        js_file = base_dir / 'static/js/app.js'
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for effect types
        effect_types = [
            'damage_over_time',
            'heal_over_time',
            'chain_damage',
            'aoe_damage',  # This is the actual name used
            'conditional_effect',
            'proc_effect'
        ]
        
        for effect in effect_types:
            self.assertIn(effect, content, f"Effect type missing: {effect}")

class TestPerformanceAndQuality(unittest.TestCase):
    """Test performance and code quality aspects."""
    
    def test_file_sizes_reasonable(self):
        """Test that files are not excessively large."""
        base_dir = Path(__file__).parent.parent
        
        file_size_limits = {
            'index.html': 100 * 1024,  # 100KB
            'static/css/style.css': 200 * 1024,  # 200KB
            'static/js/app.js': 500 * 1024,  # 500KB
            'web_backend.py': 200 * 1024  # 200KB
        }
        
        for file_path, size_limit in file_size_limits.items():
            full_path = base_dir / file_path
            if full_path.exists():
                file_size = full_path.stat().st_size
                self.assertLess(file_size, size_limit, 
                              f"File too large: {file_path} ({file_size} bytes > {size_limit} bytes)")
    
    def test_no_obvious_syntax_errors(self):
        """Test that files don't have obvious syntax errors."""
        base_dir = Path(__file__).parent.parent

        # Test Python file syntax
        backend_file = base_dir / 'web_backend.py'
        if backend_file.exists():
            try:
                with open(backend_file, 'r', encoding='utf-8') as f:
                    compile(f.read(), backend_file, 'exec')
            except SyntaxError as e:
                self.fail(f"Syntax error in {backend_file}: {e}")

        # Test HTML structure
        html_file = base_dir / 'index.html'
        if html_file.exists():
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Basic HTML structure checks
            self.assertIn('<!DOCTYPE html>', content)
            self.assertIn('<html', content)
            self.assertIn('</html>', content)
            self.assertIn('<head>', content)
            self.assertIn('</head>', content)
            self.assertIn('<body>', content)
            self.assertIn('</body>', content)

    def test_error_handling_improvements(self):
        """Test that JavaScript error handling improvements are in place."""
        base_dir = Path(__file__).parent.parent
        js_file = base_dir / 'static/js/app.js'

        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for error handling improvements
        error_handling_features = [
            'if (!overlay)',  # showLoading null check
            'console.warn',   # Warning messages
            'try {',          # Try-catch blocks
            'catch (error)',  # Error catching
            'console.error'   # Error logging
        ]

        for feature in error_handling_features:
            self.assertIn(feature, content, f"Error handling feature missing: {feature}")

    def test_loading_overlay_compatibility(self):
        """Test that loading overlay is properly handled."""
        base_dir = Path(__file__).parent.parent

        # Check main HTML file
        html_file = base_dir / 'index.html'
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for loading overlay element
        self.assertIn('loading-overlay', content, "Loading overlay element missing from main HTML")

        # Check JavaScript for proper handling
        js_file = base_dir / 'static/js/app.js'
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # Check for null safety in showLoading function
        self.assertIn('if (!overlay)', js_content, "showLoading function lacks null safety check")

def run_simple_tests():
    """Run the simple test suite."""
    print("🧪 WoW Simulator - Simple Test Suite")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestBasicFunctionality,
        TestAdvancedFeatures,
        TestPerformanceAndQuality
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 Simple Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
        print(f"   Success rate: {success_rate:.1f}%")
    
    if not result.failures and not result.errors:
        print("✅ All simple tests passed!")
        print("🎉 Core functionality is working correctly!")
    else:
        print("⚠️  Some tests failed - review the output above")
    
    return result

if __name__ == '__main__':
    run_simple_tests()
