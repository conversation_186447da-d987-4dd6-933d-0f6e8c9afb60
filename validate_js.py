#!/usr/bin/env python3
"""
JavaScript validation script for WoW Simulator
Checks for common syntax errors in JavaScript files.
"""

import re
import os
from pathlib import Path

def validate_javascript_syntax(file_path):
    """Basic JavaScript syntax validation."""
    print(f"🧪 Validating {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        errors = []
        warnings = []
        
        # Check for balanced braces
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            errors.append(f"Unbalanced braces: {open_braces} open, {close_braces} close")
        
        # Check for balanced parentheses (with some tolerance for strings)
        open_parens = content.count('(')
        close_parens = content.count(')')
        if abs(open_parens - close_parens) > 5:  # Allow some tolerance
            warnings.append(f"Possibly unbalanced parentheses: {open_parens} open, {close_parens} close")
        
        # Check for balanced square brackets
        open_brackets = content.count('[')
        close_brackets = content.count(']')
        if open_brackets != close_brackets:
            errors.append(f"Unbalanced square brackets: {open_brackets} open, {close_brackets} close")
        
        # Check for common syntax issues
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check for missing semicolons after function declarations
            if re.match(r'^\s*function\s+\w+\s*\([^)]*\)\s*\{', line):
                # This is okay - function declarations don't need semicolons
                pass
            
            # Check for potential missing commas in object literals
            if line.endswith('}') and i < len(lines):
                next_line = lines[i].strip() if i < len(lines) else ''
                if next_line.startswith('"') or next_line.startswith("'") or re.match(r'^\w+:', next_line):
                    warnings.append(f"Line {i}: Possible missing comma before next property")
            
            # Check for unexpected semicolons
            if ';;' in line:
                warnings.append(f"Line {i}: Double semicolon found")
        
        # Check for specific patterns that might cause issues
        if 'function(' in content:
            warnings.append("Found 'function(' - make sure there's a space: 'function ('")
        
        # Check for ES6 features that might not be supported
        if '=>' in content:
            warnings.append("Arrow functions found - ensure browser compatibility")
        
        if 'const ' in content or 'let ' in content:
            warnings.append("ES6 const/let found - ensure browser compatibility")
        
        # Report results
        if errors:
            print("❌ Syntax errors found:")
            for error in errors:
                print(f"   - {error}")
            return False
        elif warnings:
            print("⚠️  Potential issues found:")
            for warning in warnings:
                print(f"   - {warning}")
            print("✅ No critical syntax errors")
            return True
        else:
            print("✅ No syntax issues found")
            return True
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def validate_all_js_files():
    """Validate all JavaScript files in the project."""
    print("🧙‍♂️ WoW Simulator JavaScript Validation")
    print("=" * 40)
    
    js_files = [
        'static/js/app.js'
    ]
    
    all_valid = True
    
    for js_file in js_files:
        if Path(js_file).exists():
            if not validate_javascript_syntax(js_file):
                all_valid = False
        else:
            print(f"❌ File not found: {js_file}")
            all_valid = False
        print()
    
    print("=" * 40)
    if all_valid:
        print("🎉 All JavaScript files are valid!")
        print("\n💡 The web interface should work correctly.")
    else:
        print("❌ Some JavaScript files have issues.")
        print("\n🔧 Please fix the errors above before using the web interface.")
    
    return all_valid

if __name__ == '__main__':
    import sys
    sys.exit(0 if validate_all_js_files() else 1)
