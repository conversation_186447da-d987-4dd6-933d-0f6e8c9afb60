"""
WoW Simulator Web Backend
Flask API server to expose WoW Simulator functionality via REST endpoints.
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import traceback
from typing import Dict, List, Any

# Import WoW Simulator modules
from src.character import ModularCharacter
from src.spells.spell_builder import Spell<PERSON>uild<PERSON>, SpellTemplateLibrary
from src.spells.spell_validator import SpellValidator
from src.optimization.rotation_optimizer import RotationOptimizer, OptimizationGoal
from src.optimization.timeline_visualizer import TimelineVisualizer
from src.stats.standalone_stats import StatType

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# Global instances
spell_builder = SpellBuilder()
spell_validator = SpellValidator()
rotation_optimizer = RotationOptimizer()
timeline_visualizer = TimelineVisualizer()

# In-memory storage (in production, use a proper database)
characters = {}
spells = {}
session_data = {}

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "message": "WoW Simulator API is running"})

@app.route('/api/character', methods=['POST'])
def create_character():
    """Create or update a character."""
    try:
        data = request.get_json()
        
        # Create character
        character = ModularCharacter(data.get('name', 'Unnamed Character'))
        
        # Set stats
        if 'health' in data:
            character.stats.set_stat(StatType.HEALTH, data['health'])
        if 'mana' in data:
            character.stats.set_stat(StatType.MANA, data['mana'])
        if 'spell_power' in data:
            character.stats.set_stat(StatType.SPELL_POWER, data['spell_power'])
        if 'crit_chance' in data:
            character.stats.set_stat(StatType.CRIT_CHANCE, data['crit_chance'])
        if 'haste' in data:
            character.stats.set_stat(StatType.HASTE, data['haste'])
        if 'hit_chance' in data:
            character.stats.set_stat(StatType.HIT_CHANCE, data['hit_chance'])
        
        # Store character
        character_id = data.get('name', 'default')
        characters[character_id] = character
        
        return jsonify({
            "success": True,
            "message": f"Character '{character.name}' created successfully",
            "character_id": character_id,
            "character": serialize_character(character)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/api/character/<character_id>', methods=['GET'])
def get_character(character_id):
    """Get character information."""
    try:
        if character_id not in characters:
            return jsonify({"success": False, "error": "Character not found"}), 404
        
        character = characters[character_id]
        return jsonify({
            "success": True,
            "character": serialize_character(character)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spell-templates', methods=['GET'])
def get_spell_templates():
    """Get available spell templates."""
    try:
        templates = SpellTemplateLibrary.get_all_templates()
        return jsonify({
            "success": True,
            "templates": templates
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spell', methods=['POST'])
def create_spell():
    """Create a new spell."""
    try:
        data = request.get_json()
        
        # Validate spell data
        is_valid, errors, config = spell_validator.validate_spell_from_dict(data)
        if not is_valid:
            return jsonify({
                "success": False,
                "error": "Spell validation failed",
                "validation_errors": errors
            }), 400
        
        # Build spell
        spell = spell_builder.build_spell(config)
        
        # Store spell
        spell_id = f"spell_{len(spells) + 1}"
        spells[spell_id] = {
            "spell": spell,
            "config": data,
            "created_at": "now"  # In production, use proper timestamp
        }
        
        return jsonify({
            "success": True,
            "message": f"Spell '{spell.name}' created successfully",
            "spell_id": spell_id,
            "spell": serialize_spell(spell, data)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/api/spell/validate', methods=['POST'])
def validate_spell():
    """Validate spell configuration."""
    try:
        data = request.get_json()
        
        is_valid, errors, config = spell_validator.validate_spell_from_dict(data)
        
        return jsonify({
            "success": True,
            "is_valid": is_valid,
            "errors": errors,
            "config": config.__dict__ if config else None
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spells', methods=['GET'])
def get_spells():
    """Get all created spells."""
    try:
        spell_list = []
        for spell_id, spell_data in spells.items():
            spell_list.append({
                "id": spell_id,
                "spell": serialize_spell(spell_data["spell"], spell_data["config"]),
                "created_at": spell_data["created_at"]
            })
        
        return jsonify({
            "success": True,
            "spells": spell_list
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/optimize-rotation', methods=['POST'])
def optimize_rotation():
    """Optimize spell rotation."""
    try:
        data = request.get_json()
        
        # Get character
        character_id = data.get('character_id', 'default')
        if character_id not in characters:
            return jsonify({"success": False, "error": "Character not found"}), 404
        
        character = characters[character_id]
        
        # Get selected spells
        spell_ids = data.get('spell_ids', [])
        selected_spells = []
        for spell_id in spell_ids:
            if spell_id in spells:
                selected_spells.append(spells[spell_id]["spell"])
        
        if not selected_spells:
            return jsonify({"success": False, "error": "No valid spells selected"}), 400
        
        # Parse optimization parameters
        goal_str = data.get('goal', 'maximum_dps')
        goal = OptimizationGoal.MAXIMUM_DPS
        if goal_str == 'mana_efficient':
            goal = OptimizationGoal.MANA_EFFICIENT
        elif goal_str == 'burst_damage':
            goal = OptimizationGoal.BURST_DAMAGE
        
        duration = data.get('duration', 60)
        target_count = data.get('target_count', 1)
        movement_time = data.get('movement_time', 0.0)
        
        # Optimize rotation
        result = rotation_optimizer.optimize_rotation(
            character=character,
            available_spells=selected_spells,
            duration=duration,
            goal=goal,
            target_count=target_count,
            movement_time=movement_time
        )
        
        # Generate timeline visualization
        timeline = timeline_visualizer.visualize_rotation(result)
        
        return jsonify({
            "success": True,
            "result": serialize_rotation_result(result),
            "timeline": timeline
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/api/test-spell', methods=['POST'])
def test_spell():
    """Test a spell with multiple iterations."""
    try:
        data = request.get_json()
        
        # Get character and spell
        character_id = data.get('character_id', 'default')
        spell_id = data.get('spell_id')
        
        if character_id not in characters:
            return jsonify({"success": False, "error": "Character not found"}), 404
        
        if spell_id not in spells:
            return jsonify({"success": False, "error": "Spell not found"}), 404
        
        character = characters[character_id]
        spell = spells[spell_id]["spell"]
        
        # Test parameters
        iterations = data.get('iterations', 100)
        target_armor = data.get('target_armor', 0)
        
        # Simulate spell testing
        results = simulate_spell_test(spell, character, iterations, target_armor)
        
        return jsonify({
            "success": True,
            "results": results
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

# Helper functions
def serialize_character(character):
    """Convert character to JSON-serializable format."""
    return {
        "name": character.name,
        "health": character.stats.get_stat(StatType.HEALTH),
        "mana": character.stats.get_stat(StatType.MANA),
        "spell_power": character.stats.get_stat(StatType.SPELL_POWER),
        "crit_chance": character.stats.get_stat(StatType.CRIT_CHANCE),
        "haste": character.stats.get_stat(StatType.HASTE),
        "hit_chance": character.stats.get_stat(StatType.HIT_CHANCE),
        "current_health": character.current_health,
        "current_mana": character.current_mana
    }

def serialize_spell(spell, config):
    """Convert spell to JSON-serializable format."""
    return {
        "name": spell.name,
        "school": spell.school.name.lower(),
        "base_damage": spell.base_damage,
        "cast_time": spell.cast_time,
        "cooldown": spell.cooldown,
        "mana_cost": spell.mana_cost,
        "spell_power_coefficient": spell.spell_power_coefficient,
        "can_crit": spell.can_crit,
        "description": config.get("description", ""),
        "target_type": config.get("target_type", "single_enemy")
    }

def serialize_rotation_result(result):
    """Convert rotation result to JSON-serializable format."""
    return {
        "total_dps": result.total_dps,
        "total_damage": result.total_damage,
        "mana_used": result.mana_used,
        "efficiency": result.efficiency,
        "rotation_steps": [
            {
                "spell_name": step.spell_name,
                "cast_time": step.cast_time,
                "damage": step.damage,
                "mana_cost": step.mana_cost,
                "timestamp": step.timestamp
            }
            for step in result.rotation_steps
        ]
    }

def simulate_spell_test(spell, character, iterations, target_armor):
    """Simulate spell testing with multiple iterations."""
    import random
    
    damages = []
    crits = 0
    
    for _ in range(iterations):
        # Calculate base damage
        if isinstance(spell.base_damage, list):
            base_damage = random.uniform(spell.base_damage[0], spell.base_damage[1])
        else:
            base_damage = spell.base_damage
        
        # Add spell power scaling
        spell_power_bonus = base_damage * spell.spell_power_coefficient * (character.stats.get_stat(StatType.SPELL_POWER) / 100)
        damage = base_damage + spell_power_bonus
        
        # Check for critical hit
        if spell.can_crit and random.random() < character.stats.get_stat(StatType.CRIT_CHANCE):
            damage *= 2
            crits += 1
        
        # Apply armor reduction (simplified)
        if target_armor > 0:
            armor_reduction = target_armor / (target_armor + 400)
            damage *= (1 - armor_reduction)
        
        damages.append(round(damage))
    
    # Calculate statistics
    avg_damage = sum(damages) / len(damages)
    min_damage = min(damages)
    max_damage = max(damages)
    crit_rate = (crits / iterations) * 100
    dps = avg_damage / max(spell.cast_time, 1.5)  # Account for GCD
    dpm = avg_damage / spell.mana_cost if spell.mana_cost > 0 else 0
    
    return {
        "avg_damage": round(avg_damage, 1),
        "min_damage": min_damage,
        "max_damage": max_damage,
        "crit_rate": round(crit_rate, 1),
        "dps": round(dps, 1),
        "dpm": round(dpm, 2),
        "iterations": iterations
    }

if __name__ == '__main__':
    print("🧙‍♂️ Starting WoW Simulator Web Backend...")
    print("📡 API will be available at: http://localhost:5000")
    print("🔗 Frontend should connect to: http://localhost:5000/api")
    app.run(debug=True, host='0.0.0.0', port=5000)
