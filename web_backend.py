"""
WoW Simulator Web Backend
Flask API server to expose WoW Simulator functionality via REST endpoints.
"""

from flask import Flask, request, jsonify, session
from flask_cors import CORS
import json
import traceback
import uuid
import time
from typing import Dict, List, Any
from datetime import datetime, timedelta

# Import WoW Simulator modules
from src.character import ModularCharacter
from src.spells.spell_builder import SpellBuilder, SpellTemplateLibrary
from src.spells.spell_validator import SpellValidator
from src.optimization.rotation_optimizer import RotationOptimizer, OptimizationGoal
from src.optimization.timeline_visualizer import TimelineVisualizer
from src.stats.standalone_stats import StatType

app = Flask(__name__)
app.secret_key = 'wow-simulator-secret-key-change-in-production'  # Change this in production!
CORS(app, supports_credentials=True)  # Enable CORS with credentials for sessions

# Global instances
spell_builder = SpellBuilder()
spell_validator = SpellValidator()
rotation_optimizer = RotationOptimizer()
timeline_visualizer = TimelineVisualizer()

# Enhanced in-memory storage with session management
# In production, replace with proper database (PostgreSQL, MongoDB, etc.)
class DataStore:
    def __init__(self):
        self.sessions = {}  # session_id -> session_data
        self.characters = {}  # session_id -> {character_id -> character}
        self.spells = {}  # session_id -> {spell_id -> spell_data}
        self.rotations = {}  # session_id -> {rotation_id -> rotation_data}
        self.last_cleanup = time.time()

    def cleanup_expired_sessions(self):
        """Remove expired sessions (older than 24 hours)."""
        current_time = time.time()
        if current_time - self.last_cleanup < 3600:  # Cleanup every hour
            return

        expired_sessions = []
        for session_id, session_info in self.sessions.items():
            if current_time - session_info.get('last_activity', 0) > 86400:  # 24 hours
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            self.remove_session(session_id)

        self.last_cleanup = current_time

    def create_session(self):
        """Create a new session."""
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            'created_at': time.time(),
            'last_activity': time.time(),
            'user_agent': request.headers.get('User-Agent', ''),
            'ip_address': request.remote_addr
        }
        self.characters[session_id] = {}
        self.spells[session_id] = {}
        self.rotations[session_id] = {}
        return session_id

    def get_session(self, session_id):
        """Get session data and update last activity."""
        if session_id in self.sessions:
            self.sessions[session_id]['last_activity'] = time.time()
            return self.sessions[session_id]
        return None

    def remove_session(self, session_id):
        """Remove a session and all its data."""
        self.sessions.pop(session_id, None)
        self.characters.pop(session_id, None)
        self.spells.pop(session_id, None)
        self.rotations.pop(session_id, None)

# Global data store
data_store = DataStore()

def get_or_create_session():
    """Get existing session or create a new one."""
    data_store.cleanup_expired_sessions()

    session_id = session.get('session_id')
    if not session_id or not data_store.get_session(session_id):
        session_id = data_store.create_session()
        session['session_id'] = session_id

    return session_id

def require_session(f):
    """Decorator to ensure a valid session exists."""
    def decorated_function(*args, **kwargs):
        try:
            session_id = get_or_create_session()
            return f(session_id, *args, **kwargs)
        except Exception as e:
            return jsonify({
                "success": False,
                "error": "Session error",
                "details": str(e)
            }), 500
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "message": "WoW Simulator API is running",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(data_store.sessions)
    })

@app.route('/api/session', methods=['GET'])
@require_session
def get_session_info(session_id):
    """Get current session information."""
    session_info = data_store.get_session(session_id)
    return jsonify({
        "success": True,
        "session_id": session_id,
        "session_info": {
            "created_at": datetime.fromtimestamp(session_info['created_at']).isoformat(),
            "last_activity": datetime.fromtimestamp(session_info['last_activity']).isoformat(),
            "characters_count": len(data_store.characters.get(session_id, {})),
            "spells_count": len(data_store.spells.get(session_id, {})),
            "rotations_count": len(data_store.rotations.get(session_id, {}))
        }
    })

@app.route('/api/character', methods=['POST'])
@require_session
def create_character(session_id):
    """Create or update a character."""
    try:
        data = request.get_json()

        # Create character
        character = ModularCharacter(data.get('name', 'Unnamed Character'))

        # Set stats
        if 'health' in data:
            character.stats.set_stat(StatType.HEALTH, data['health'])
        if 'mana' in data:
            character.stats.set_stat(StatType.MANA, data['mana'])
        if 'spell_power' in data:
            character.stats.set_stat(StatType.SPELL_POWER, data['spell_power'])
        if 'crit_chance' in data:
            character.stats.set_stat(StatType.CRIT_CHANCE, data['crit_chance'])
        if 'haste' in data:
            character.stats.set_stat(StatType.HASTE, data['haste'])
        if 'hit_chance' in data:
            character.stats.set_stat(StatType.HIT_CHANCE, data['hit_chance'])

        # Store character in session
        character_id = data.get('name', 'default')
        if session_id not in data_store.characters:
            data_store.characters[session_id] = {}
        data_store.characters[session_id][character_id] = character

        return jsonify({
            "success": True,
            "message": f"Character '{character.name}' created successfully",
            "character_id": character_id,
            "character": serialize_character(character),
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/api/character/<character_id>', methods=['GET'])
@require_session
def get_character(session_id, character_id):
    """Get character information."""
    try:
        session_characters = data_store.characters.get(session_id, {})
        if character_id not in session_characters:
            return jsonify({"success": False, "error": "Character not found"}), 404

        character = session_characters[character_id]
        return jsonify({
            "success": True,
            "character": serialize_character(character),
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/characters', methods=['GET'])
@require_session
def get_all_characters(session_id):
    """Get all characters for the current session."""
    try:
        session_characters = data_store.characters.get(session_id, {})
        characters_list = []

        for char_id, character in session_characters.items():
            characters_list.append({
                "id": char_id,
                "character": serialize_character(character)
            })

        return jsonify({
            "success": True,
            "characters": characters_list,
            "count": len(characters_list),
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spell-templates', methods=['GET'])
def get_spell_templates():
    """Get available spell templates."""
    try:
        templates = SpellTemplateLibrary.get_all_templates()
        return jsonify({
            "success": True,
            "templates": templates
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spell', methods=['POST'])
@require_session
def create_spell(session_id):
    """Create a new spell."""
    try:
        data = request.get_json()

        # Validate spell data
        is_valid, errors, config = spell_validator.validate_spell_from_dict(data)
        if not is_valid:
            return jsonify({
                "success": False,
                "error": "Spell validation failed",
                "validation_errors": errors
            }), 400

        # Build spell
        spell = spell_builder.build_spell(config)

        # Store spell in session
        if session_id not in data_store.spells:
            data_store.spells[session_id] = {}

        spell_id = f"spell_{int(time.time() * 1000)}_{len(data_store.spells[session_id])}"
        data_store.spells[session_id][spell_id] = {
            "spell": spell,
            "config": data,
            "created_at": datetime.now().isoformat(),
            "spell_id": spell_id
        }

        return jsonify({
            "success": True,
            "message": f"Spell '{spell.name}' created successfully",
            "spell_id": spell_id,
            "spell": serialize_spell(spell, data),
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/api/spell/validate', methods=['POST'])
def validate_spell():
    """Validate spell configuration."""
    try:
        data = request.get_json()
        
        is_valid, errors, config = spell_validator.validate_spell_from_dict(data)
        
        return jsonify({
            "success": True,
            "is_valid": is_valid,
            "errors": errors,
            "config": config.__dict__ if config else None
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spells', methods=['GET'])
@require_session
def get_spells(session_id):
    """Get all created spells for the current session."""
    try:
        session_spells = data_store.spells.get(session_id, {})
        spell_list = []

        for spell_id, spell_data in session_spells.items():
            spell_list.append({
                "id": spell_id,
                "spell": serialize_spell(spell_data["spell"], spell_data["config"]),
                "created_at": spell_data["created_at"]
            })

        return jsonify({
            "success": True,
            "spells": spell_list,
            "count": len(spell_list),
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/spell/<spell_id>', methods=['DELETE'])
@require_session
def delete_spell(session_id, spell_id):
    """Delete a spell."""
    try:
        session_spells = data_store.spells.get(session_id, {})

        if spell_id not in session_spells:
            return jsonify({"success": False, "error": "Spell not found"}), 404

        spell_name = session_spells[spell_id]["spell"].name
        del session_spells[spell_id]

        return jsonify({
            "success": True,
            "message": f"Spell '{spell_name}' deleted successfully",
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/optimize-rotation', methods=['POST'])
@require_session
def optimize_rotation(session_id):
    """Optimize spell rotation."""
    try:
        data = request.get_json()

        # Get character
        character_id = data.get('character_id', 'default')
        session_characters = data_store.characters.get(session_id, {})
        if character_id not in session_characters:
            return jsonify({"success": False, "error": "Character not found"}), 404

        character = session_characters[character_id]

        # Get selected spells
        spell_ids = data.get('spell_ids', [])
        session_spells = data_store.spells.get(session_id, {})
        selected_spells = []

        for spell_id in spell_ids:
            if spell_id in session_spells:
                selected_spells.append(session_spells[spell_id]["spell"])

        if not selected_spells:
            return jsonify({"success": False, "error": "No valid spells selected"}), 400
        
        # Parse optimization parameters
        goal_str = data.get('goal', 'maximum_dps')
        goal = OptimizationGoal.MAXIMUM_DPS
        if goal_str == 'mana_efficient':
            goal = OptimizationGoal.MANA_EFFICIENT
        elif goal_str == 'burst_damage':
            goal = OptimizationGoal.BURST_DAMAGE
        
        duration = data.get('duration', 60)
        target_count = data.get('target_count', 1)
        movement_time = data.get('movement_time', 0.0)
        
        # Optimize rotation
        result = rotation_optimizer.optimize_rotation(
            character=character,
            available_spells=selected_spells,
            duration=duration,
            goal=goal,
            target_count=target_count,
            movement_time=movement_time
        )
        
        # Generate timeline visualization
        timeline = timeline_visualizer.visualize_rotation(result)

        # Store rotation result for future reference
        rotation_id = f"rotation_{int(time.time() * 1000)}"
        if session_id not in data_store.rotations:
            data_store.rotations[session_id] = {}

        data_store.rotations[session_id][rotation_id] = {
            "result": result,
            "timeline": timeline,
            "parameters": data,
            "created_at": datetime.now().isoformat(),
            "character_id": character_id,
            "spell_ids": spell_ids
        }

        return jsonify({
            "success": True,
            "result": serialize_rotation_result(result),
            "timeline": timeline,
            "rotation_id": rotation_id,
            "session_id": session_id
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@app.route('/api/test-spell', methods=['POST'])
@require_session
def test_spell(session_id):
    """Test a spell with multiple iterations."""
    try:
        data = request.get_json()

        # Get character and spell
        character_id = data.get('character_id', 'default')
        spell_id = data.get('spell_id')

        session_characters = data_store.characters.get(session_id, {})
        session_spells = data_store.spells.get(session_id, {})

        if character_id not in session_characters:
            return jsonify({"success": False, "error": "Character not found"}), 404

        if spell_id not in session_spells:
            return jsonify({"success": False, "error": "Spell not found"}), 404

        character = session_characters[character_id]
        spell = session_spells[spell_id]["spell"]
        
        # Test parameters
        iterations = data.get('iterations', 100)
        target_armor = data.get('target_armor', 0)
        
        # Simulate spell testing
        results = simulate_spell_test(spell, character, iterations, target_armor)
        
        return jsonify({
            "success": True,
            "results": results,
            "session_id": session_id,
            "spell_name": spell.name,
            "character_name": character.name
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

# Helper functions
def serialize_character(character):
    """Convert character to JSON-serializable format."""
    return {
        "name": character.name,
        "health": character.stats.get_stat(StatType.HEALTH),
        "mana": character.stats.get_stat(StatType.MANA),
        "spell_power": character.stats.get_stat(StatType.SPELL_POWER),
        "crit_chance": character.stats.get_stat(StatType.CRIT_CHANCE),
        "haste": character.stats.get_stat(StatType.HASTE),
        "hit_chance": character.stats.get_stat(StatType.HIT_CHANCE),
        "current_health": character.current_health,
        "current_mana": character.current_mana
    }

def serialize_spell(spell, config):
    """Convert spell to JSON-serializable format."""
    return {
        "name": spell.name,
        "school": spell.school.name.lower(),
        "base_damage": spell.base_damage,
        "cast_time": spell.cast_time,
        "cooldown": spell.cooldown,
        "mana_cost": spell.mana_cost,
        "spell_power_coefficient": spell.spell_power_coefficient,
        "can_crit": spell.can_crit,
        "description": config.get("description", ""),
        "target_type": config.get("target_type", "single_enemy")
    }

def serialize_rotation_result(result):
    """Convert rotation result to JSON-serializable format."""
    return {
        "total_dps": result.total_dps,
        "total_damage": result.total_damage,
        "mana_used": result.mana_used,
        "efficiency": result.efficiency,
        "rotation_steps": [
            {
                "spell_name": step.spell_name,
                "cast_time": step.cast_time,
                "damage": step.damage,
                "mana_cost": step.mana_cost,
                "timestamp": step.timestamp
            }
            for step in result.rotation_steps
        ]
    }

def simulate_spell_test(spell, character, iterations, target_armor):
    """Simulate spell testing with multiple iterations."""
    import random
    
    damages = []
    crits = 0
    
    for _ in range(iterations):
        # Calculate base damage
        if isinstance(spell.base_damage, list):
            base_damage = random.uniform(spell.base_damage[0], spell.base_damage[1])
        else:
            base_damage = spell.base_damage
        
        # Add spell power scaling
        spell_power_bonus = base_damage * spell.spell_power_coefficient * (character.stats.get_stat(StatType.SPELL_POWER) / 100)
        damage = base_damage + spell_power_bonus
        
        # Check for critical hit
        if spell.can_crit and random.random() < character.stats.get_stat(StatType.CRIT_CHANCE):
            damage *= 2
            crits += 1
        
        # Apply armor reduction (simplified)
        if target_armor > 0:
            armor_reduction = target_armor / (target_armor + 400)
            damage *= (1 - armor_reduction)
        
        damages.append(round(damage))
    
    # Calculate statistics
    avg_damage = sum(damages) / len(damages)
    min_damage = min(damages)
    max_damage = max(damages)
    crit_rate = (crits / iterations) * 100
    dps = avg_damage / max(spell.cast_time, 1.5)  # Account for GCD
    dpm = avg_damage / spell.mana_cost if spell.mana_cost > 0 else 0
    
    return {
        "avg_damage": round(avg_damage, 1),
        "min_damage": min_damage,
        "max_damage": max_damage,
        "crit_rate": round(crit_rate, 1),
        "dps": round(dps, 1),
        "dpm": round(dpm, 2),
        "iterations": iterations
    }

if __name__ == '__main__':
    print("🧙‍♂️ Starting WoW Simulator Web Backend...")
    print("📡 API will be available at: http://localhost:5000")
    print("🔗 Frontend should connect to: http://localhost:5000/api")
    app.run(debug=True, host='0.0.0.0', port=5000)
