"""
Working demonstration of the modular stats system.
This uses the standalone version that doesn't have import issues.
"""

from src.stats.standalone_stats import SimpleStatManager, StatType


def demo_basic_stats():
    """Demonstrate basic stat operations."""
    print("=== Basic Stats Demo ===")
    
    # Create a stat manager
    stats = SimpleStatManager()
    
    # Set some base stats
    stats.set_stat(StatType.HEALTH, 1000)
    stats.set_stat(StatType.MANA, 500)
    stats.set_stat(StatType.SPELL_POWER, 100)
    stats.set_stat(StatType.CRIT_CHANCE, 0.05)
    
    print("Base stats:")
    print(f"  Health: {stats.get_stat(StatType.HEALTH)}")
    print(f"  Mana: {stats.get_stat(StatType.MANA)}")
    print(f"  Spell Power: {stats.get_stat(StatType.SPELL_POWER)}")
    print(f"  Crit Chance: {stats.get_stat(StatType.CRIT_CHANCE)}")
    
    print("\nEffective stats (same as base since no modifiers):")
    print(f"  Health: {stats.get_effective_stat(StatType.HEALTH)}")
    print(f"  Spell Power: {stats.get_effective_stat(StatType.SPELL_POWER)}")


def demo_flat_bonuses():
    """Demonstrate flat stat bonuses."""
    print("\n=== Flat Bonuses Demo ===")
    
    stats = SimpleStatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    stats.set_stat(StatType.CRIT_CHANCE, 0.05)
    
    print(f"Base spell power: {stats.get_stat(StatType.SPELL_POWER)}")
    print(f"Base crit chance: {stats.get_stat(StatType.CRIT_CHANCE)}")
    
    # Add a flat bonus from equipment
    stats.add_flat_bonus("weapon_bonus", {
        StatType.SPELL_POWER: 50,
        StatType.CRIT_CHANCE: 0.02
    })
    
    print(f"\nAfter weapon bonus:")
    print(f"  Spell power: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    print(f"  Crit chance: {stats.get_effective_stat(StatType.CRIT_CHANCE)}")
    
    # Add another bonus from enchantment
    stats.add_flat_bonus("enchant_bonus", {
        StatType.SPELL_POWER: 25
    })
    
    print(f"\nAfter enchantment bonus:")
    print(f"  Spell power: {stats.get_effective_stat(StatType.SPELL_POWER)}")


def demo_percentage_bonuses():
    """Demonstrate percentage-based bonuses."""
    print("\n=== Percentage Bonuses Demo ===")
    
    stats = SimpleStatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    print(f"Base spell power: {stats.get_stat(StatType.SPELL_POWER)}")
    
    # Add a 20% bonus
    stats.add_percentage_bonus("talent_bonus", {
        StatType.SPELL_POWER: 20.0  # 20%
    })
    
    print(f"After 20% talent bonus: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    
    # Add another 15% bonus
    stats.add_percentage_bonus("buff_bonus", {
        StatType.SPELL_POWER: 15.0  # 15%
    })
    
    print(f"After additional 15% buff: {stats.get_effective_stat(StatType.SPELL_POWER)}")


def demo_stacking_modifiers():
    """Demonstrate stacking modifiers."""
    print("\n=== Stacking Modifiers Demo ===")
    
    stats = SimpleStatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    print(f"Base spell power: {stats.get_stat(StatType.SPELL_POWER)}")
    
    # Add a stacking buff (like Arcane Intellect stacks)
    stats.add_stacking_modifier("arcane_power", {
        StatType.SPELL_POWER: 10  # 10 spell power per stack
    }, max_stacks=5)
    
    print(f"\nAdding stacks of Arcane Power:")
    for i in range(1, 6):
        stats.add_stack("arcane_power")
        modifier = stats.get_modifier("arcane_power")
        stacks = modifier.get_stacks() if hasattr(modifier, 'get_stacks') else 0
        print(f"  Stack {stacks}: {stats.get_effective_stat(StatType.SPELL_POWER)} spell power")
    
    # Try to add more stacks (should be capped)
    stats.add_stack("arcane_power")
    modifier = stats.get_modifier("arcane_power")
    stacks = modifier.get_stacks() if hasattr(modifier, 'get_stacks') else 0
    print(f"  Trying to add 6th stack: {stacks} stacks, {stats.get_effective_stat(StatType.SPELL_POWER)} spell power")


def demo_stat_breakdown():
    """Demonstrate stat breakdown analysis."""
    print("\n=== Stat Breakdown Demo ===")
    
    stats = SimpleStatManager()
    stats.set_stat(StatType.SPELL_POWER, 100)
    
    # Add various modifiers
    stats.add_flat_bonus("equipment", {StatType.SPELL_POWER: 50})
    stats.add_percentage_bonus("talents", {StatType.SPELL_POWER: 20.0})
    stats.add_stacking_modifier("buff", {StatType.SPELL_POWER: 15}, max_stacks=3)
    stats.add_stack("buff")
    stats.add_stack("buff")
    
    print(f"Final spell power: {stats.get_effective_stat(StatType.SPELL_POWER)}")
    
    # Get detailed breakdown
    breakdown = stats.get_stat_breakdown(StatType.SPELL_POWER)
    print("\nDetailed breakdown:")
    for key, value in breakdown.items():
        print(f"  {key}: {value}")


def demo_complete_character():
    """Demonstrate a complete character with multiple stats and modifiers."""
    print("\n=== Complete Character Demo ===")
    
    stats = SimpleStatManager()
    
    # Set base character stats
    print("Creating a Level 60 Mage:")
    stats.set_stat(StatType.HEALTH, 3000)
    stats.set_stat(StatType.MANA, 4500)
    stats.set_stat(StatType.SPELL_POWER, 150)
    stats.set_stat(StatType.CRIT_CHANCE, 0.08)
    stats.set_stat(StatType.HIT_CHANCE, 0.83)
    
    print("Base stats:")
    for stat_type in [StatType.HEALTH, StatType.MANA, StatType.SPELL_POWER, StatType.CRIT_CHANCE, StatType.HIT_CHANCE]:
        print(f"  {stat_type.value}: {stats.get_stat(stat_type)}")
    
    # Add equipment bonuses
    stats.add_flat_bonus("staff_of_dominance", {
        StatType.SPELL_POWER: 85,
        StatType.CRIT_CHANCE: 0.02
    })
    
    stats.add_flat_bonus("robe_of_the_archmage", {
        StatType.SPELL_POWER: 45,
        StatType.MANA: 300
    })
    
    stats.add_flat_bonus("ring_of_spell_power", {
        StatType.SPELL_POWER: 30
    })
    
    # Add talent bonuses
    stats.add_percentage_bonus("arcane_mind", {
        StatType.MANA: 30.0  # 30% more mana
    })
    
    stats.add_percentage_bonus("arcane_instability", {
        StatType.SPELL_POWER: 6.0  # 6% more spell damage
    })
    
    # Add temporary buffs
    stats.add_flat_bonus("arcane_intellect", {
        StatType.MANA: 400
    })
    
    stats.add_stacking_modifier("arcane_power", {
        StatType.SPELL_POWER: 20
    }, max_stacks=4)
    
    # Add some stacks
    stats.add_stack("arcane_power")
    stats.add_stack("arcane_power")
    stats.add_stack("arcane_power")
    
    print("\nFinal character stats:")
    for stat_type in [StatType.HEALTH, StatType.MANA, StatType.SPELL_POWER, StatType.CRIT_CHANCE, StatType.HIT_CHANCE]:
        effective = stats.get_effective_stat(stat_type)
        base = stats.get_stat(stat_type)
        bonus = effective - base
        print(f"  {stat_type.value}: {effective:.2f} (base: {base}, bonus: +{bonus:.2f})")


def main():
    """Run all demonstrations."""
    print("WoW Simulator - Modular Stats System Demo")
    print("=" * 50)
    
    demo_basic_stats()
    demo_flat_bonuses()
    demo_percentage_bonuses()
    demo_stacking_modifiers()
    demo_stat_breakdown()
    demo_complete_character()
    
    print("\n" + "=" * 50)
    print("✅ Demo complete! The modular stats system provides:")
    print("✓ Easy addition of new stat types")
    print("✓ Flexible modifier system")
    print("✓ Stacking and percentage modifiers")
    print("✓ Detailed analysis and debugging")
    print("✓ Clean separation of concerns")
    print("✓ Easy integration with character/spell systems")


if __name__ == "__main__":
    main()
